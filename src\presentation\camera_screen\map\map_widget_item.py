from ast import List
import json
import pickle
import logging
import math
from src.common.widget.notifications.notify import Notifications
from src.common.qml.models.video_model import VideoModel
from src.presentation.camera_screen.stream_object_base_widget import StreamObjectBaseWidget
from src.common.qml.models.map_controller import FloorModel, Map2DController, MapState
logger = logging.getLogger(__name__)
from PySide6.QtCore import Qt, QUrl, QSize,Slot
from PySide6.QtQuickWidgets import QQuickWidget
from PySide6.QtGui import QPainter, QWheelEvent, QResizeEvent, QDrag
from PySide6.QtCore import Qt, Signal, QSize, QMimeData, QUrl
from PySide6.QtQml import qmlRegisterType
from src.common.controller.main_controller import main_controller
from src.styles.style import Style

class Map2DWidgetItem(StreamObjectBaseWidget):
    open_camera_in_tab_signal = Signal(tuple)
    drop_when_not_editable_signal = Signal(tuple)

    def __init__(self, parent=None, floor_model: FloorModel = None, width=None, height=None, row=None, col=None, callback_fullscreen=None, stack_item=None, tab_model = None, is_editable = False):
        super().__init__(parent, stack_item=stack_item, tab_model = tab_model)
        self.setFocusPolicy(Qt.StrongFocus)
        self.setMouseTracking(True)
        self.setAcceptDrops(True)
        self.stack_item = stack_item
        self.tab_model = tab_model
        self.row = row
        self.col = col
        self.name = "Map2D"
        self.root_width = width
        self.root_height = height
        self.map_state = MapState()
        self.map_state.editMode = is_editable
        self.map_controller = Map2DController(floor_model, self.map_state)
        self.width = width
        self.height = height
        self.setup_ui()

    def setup_ui(self):
        self.map_view = QQuickWidget(self)
        qmlRegisterType(VideoModel, "CustomComponents", 1, 0, "VideoModel")
        self.map_state.dropEventChanged.connect(self.dropEventChanged)
        self.map_controller.saveCameraStatusSignal.connect(self.saveCameraStatusSignal)
        self.map_view.rootContext().setContextProperty("map2dController", self.map_controller)
        self.map_view.rootContext().setContextProperty("widget2D", self)
        self.map_view.setSource(QUrl("qrc:/src/common/qml/map/Map2DonGrid.qml"))
        self.map_view.setResizeMode(QQuickWidget.SizeRootObjectToView)
        self.map_view.setGeometry(0, 0, self.width, self.height)
        self.map_view.setAcceptDrops(True)

        if not self.map_state.editMode:
            self.header_top_widget = self.create_header_top_widget_base()
            self.header_top_widget.setVisible(False)
    
    def update_resize(self, width, height):
        # logger.debug(f'update_resize')
        self.on_resize_change(QResizeEvent(QSize(width, height), QSize(self.width, self.height)))

    def on_resize_change(self, event):
        # set scene rect
        new_size = event.size()
        self.width = new_size.width()
        self.height = new_size.height()
        self.root_width = new_size.width()
        self.root_height = new_size.height()
        self.map_view.setGeometry(0, 0, self.width, self.height)

        if self.header_top_widget is not None:
            self.header_top_widget.setGeometry(min(self.x() + new_size.width() / 10 * 9, self.x() + new_size.width() - 100), 0, max(new_size.width()/10, 100), self.HEIGHT_HEADER)
            
    def saveCameraStatusSignal(self, status):
        if status:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                            title=self.tr('Floor saved successfully.'), icon=Style.PrimaryImage.sucess_result)
        else:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                            title=self.tr('Failed to save floor.'), icon=Style.PrimaryImage.fail_result)
            
    def dropEventChanged(self, data):
        if(data.isEmpty()):
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('To drag an item onto the Map, you need to enter edit mode.'), icon=Style.PrimaryImage.info_result)
            return
        try:
            data = pickle.loads(data.data())
            
        except:
            data = bytes(data.data()).decode('utf-8')
            data = json.loads(data)
        
        self.stack_item.swap_item(data)

    @Slot(result='QVariant')
    def getPosition(self):
        row = self.stack_item.position.x()
        col = self.stack_item.position.y()   
        logger.info(f"getPosition {row,col,self.stack_item}")
        return [row,col]
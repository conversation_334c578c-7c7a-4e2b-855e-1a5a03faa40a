import logging
logger = logging.getLogger(__name__)
from PySide6.QtWidgets import QWidget,QStackedWidget, QPushButton,QComboBox,QAbstractItemView, QGridLayout, QLineEdit, QHBoxLayout,QFrame,QVBoxLayout,QLabel,QApplication,QDialog
from PySide6.QtGui import QIcon
from PySide6.QtCore import QThread, Qt,QSize,QEvent
from src.presentation.device_management_screen.widget.list_custom_widgets import ImageAdjWidget
from src.styles.style import Style
from functools import partial
from PySide6.QtCore import Signal as pyqtSignal
from src.common.controller.main_controller import main_controller
from src.common.widget.notifications.notify import Notifications
from src.common.widget.preset_listview import PresetListView,PresetItem
from src.presentation.device_management_screen.widget.list_custom_widgets import NoScrollSlider
from src.utils.camera_qsettings import Camera_Qsettings
from src.common.camera.grid_item_selected import grid_item_selected
from src.common.controller.controller_manager import Controller
# Class: PTZWidget
import threading

class WorkerThread(QThread):
    finished = pyqtSignal()

    def __init__(self, callback=None, parent=None):
        super().__init__(parent)
        self.callback = callback

    def run(self):
        if self.callback:
            self.callback()
        self.finished.emit()


class SquareButton(QPushButton):
    # add icon
    def __init__(self, text='', icon: QIcon = None, height: int = 32,width:int = 32, style: str = None,callback_click = None,callback_exit_click = None):
        super().__init__(icon, text)
        self.callback_click = callback_click
        self.callback_exit_click = callback_exit_click
        self.setIconSize(QSize(width, height))
        self.setObjectName("button")
        # self.setFixedSize(width, height)
        self.setFocusPolicy(Qt.NoFocus)
        if style is not None:
            self.setStyleSheet(style)
        else:
            # create style sheet hover press and pressed
            self.setStyleSheet(
                f'''
                QPushButton {{
                    background-color: rgba(0, 0, 0, 0.7);
                    border-radius: 4px;
                }}
                QPushButton:hover#button {{
                    background-color: {Style.PrimaryColor.on_hover_secondary};
                    border-radius: 4px;
                }}
                QPushButton:pressed {{
                    background-color: rgba(29, 90, 223, 0.4);
                    border-radius: 4px;
                }}
                QPushButton:disabled {{
                    background-color: {Style.PrimaryColor.on_background};
                    border-radius: 4px;
                    color: #000000
                }}
                '''
            )

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            # self.timer.start(500)
            if self.callback_click is not None:
                self.callback_click()

    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            if self.callback_exit_click is not None:
                self.callback_exit_click()


class PTZWidget(QWidget):
    ip_camera = None
    port = None
    username = None
    password = None
    index = None
    signal_data_ptz = pyqtSignal(dict)

    def __init__(self, ip_camera=None, port=None, username=None, password=None, index=None, parent=None,controller:Controller = None,camera_model = None):
        super().__init__(parent)
        self.parent_widget = parent
        self.controller = controller
        self.camera_model = camera_model
        PTZWidget.ip_camera = ip_camera
        PTZWidget.port = port
        PTZWidget.username = username
        PTZWidget.password = password
        PTZWidget.index = index
        self.message = None
        self.ptz_onvif = None
        self.profileToken = None
        self.camera_id = None
        self.is_goto_patrol = False # flag trang thai co dang thuc hien phien goto_patrol khong
        self.item = None
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.main_layout.setSpacing(0)
        self.setLayout(self.main_layout)
        self.list_direction = [
            'call_preset',
            'setting_preset',
            'delete_preset',
            'play_patrol',
            'stop_patrol',
            'setting_patrol',
            'delete_patrol',
            'add_preset',
            'delete_presets',
            'up_preset',
            'down_preset'
        ]
        self.grid_ptz_widget = QWidget()
        self.layout_grid = QGridLayout(self.grid_ptz_widget)
        # self.layout_grid.setContentsMargins(0,0,0,0)
        self.setup_ui_grid_ptz()
        ####### UI Control Speed ###########
        self.control_speed_widget = QWidget()
        self.control_speed_layout = QHBoxLayout(self.control_speed_widget)
        # self.control_speed_layout.setContentsMargins(0,10,0,10)
        self.control_speed_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.setup_ui_control_speed()
        #####################################
        ####### UI PTZ advance ###########
        # self.ptz_advance_widget = QWidget()
        # self.ptz_advance_widget.hide()
        # self.ptz_advance_layout = QHBoxLayout(self.ptz_advance_widget)
        # self.ptz_advance_layout.setContentsMargins(0,0,0,0)
        # self.setup_ui_ptz_advande()
        #####################################
        ####### UI preset patrol###########
        self.preset_patrol_widget = QWidget()
        self.preset_patrol_widget.hide()
        self.preset_patrol_layout = QVBoxLayout(self.preset_patrol_widget)
        self.preset_patrol_layout.setContentsMargins(0,0,0,0)
        self.preset_layout = QHBoxLayout()
        self.preset_layout.setSpacing(1)
        self.setup_ui_preset_patrol()
        #####################################
        ####### UI Image Adjustment ###########
        self.image_adj_widget = ImageAdjWidget(callback=self.callback_image_adj)
        self.image_adj_widget.hide()
        #####################################
        self.main_layout.addWidget(self.grid_ptz_widget)
        self.main_layout.addWidget(self.control_speed_widget)
        # self.main_layout.addWidget(self.ptz_advance_widget)
        self.main_layout.addWidget(self.preset_patrol_widget)
        self.main_layout.addWidget(self.image_adj_widget)
        # self.signal_data_ptz.connect(self.update_ptz_data)

    def setup_ui_grid_ptz(self):
        self.list_icon_move = [
            main_controller.get_theme_attribute("Image", "left_top"),
            main_controller.get_theme_attribute("Image", "top"),
            main_controller.get_theme_attribute("Image", "right_top"),
            main_controller.get_theme_attribute("Image", "left"),
            main_controller.get_theme_attribute("Image", "around"),
            main_controller.get_theme_attribute("Image", "right"),
            main_controller.get_theme_attribute("Image", "left_bottom"),
            main_controller.get_theme_attribute("Image", "bottom"),
            main_controller.get_theme_attribute("Image", "right_bottom"),
        ]

        self.list_icon_option = [
            main_controller.get_theme_attribute("Image", "zoom_in"),
            main_controller.get_theme_attribute("Image", "zoom_out"),
            main_controller.get_theme_attribute("Image", "focus_near"),
            main_controller.get_theme_attribute("Image", "focus_far"),
            main_controller.get_theme_attribute("Image", "iris_add"),
            main_controller.get_theme_attribute("Image", "iris_not_add"),
        ]
        self.list_action_move = [
            'left_top',
            'move_up',
            'right_top',
            'move_left',
            'around',
            'move_right',
            'left_bottom',
            'move_down',
            'right_bottom',
        ]
        self.list_action_option = [
            'zoom_in',
            'zoom_out',
            'focus_near',
            'focus_far',
            'iris_add',
            'iris_not_add',
        ]
        # Cột thứ nhất - Grid View 3x3
        column_PTZ_layout = QGridLayout()
        #left_top
        left_top = SquareButton(
            icon=QIcon(self.list_icon_move[0]),
            callback_click=partial(self.callback_click,self.list_action_move[0]),
            callback_exit_click=partial(self.callback_exit_click,self.list_action_move[0])
        )
        #move_up
        move_up = SquareButton(
            icon=QIcon(self.list_icon_move[1]),callback_click=partial(self.callback_click,self.list_action_move[1]),callback_exit_click=partial(self.callback_exit_click,self.list_action_move[1])
        )
        #right_top
        right_top = SquareButton(
            icon=QIcon(self.list_icon_move[2]),
            callback_click=partial(self.callback_click,self.list_action_move[2]),
            callback_exit_click=partial(self.callback_exit_click,self.list_action_move[2])
        )
        #move_left
        move_left = SquareButton(
            icon=QIcon(self.list_icon_move[3]),callback_click=partial(self.callback_click,self.list_action_move[3]),callback_exit_click=partial(self.callback_exit_click,self.list_action_move[3])
        )

        #move_up
        around = SquareButton(
            icon=QIcon(self.list_icon_move[4]),callback_click=partial(self.callback_click,self.list_action_move[4]),callback_exit_click=partial(self.callback_exit_click,self.list_action_move[4])
        )
        #right_top
        move_right = SquareButton(
            icon=QIcon(self.list_icon_move[5]),callback_click=partial(self.callback_click,self.list_action_move[5]),callback_exit_click=partial(self.callback_exit_click,self.list_action_move[5])
        )
        #left_bottom
        left_bottom = SquareButton(
            icon=QIcon(self.list_icon_move[6]),callback_click=partial(self.callback_click,self.list_action_move[6]),callback_exit_click=partial(self.callback_exit_click,self.list_action_move[6])
        )
        #move_down
        move_down = SquareButton(
            icon=QIcon(self.list_icon_move[7]),callback_click=partial(self.callback_click,self.list_action_move[7]),callback_exit_click=partial(self.callback_exit_click,self.list_action_move[7])
        )
        #right_bottom
        right_bottom = SquareButton(
            icon=QIcon(self.list_icon_move[8]),callback_click=partial(self.callback_click,self.list_action_move[8]),callback_exit_click=partial(self.callback_exit_click,self.list_action_move[8])
        )
        column_PTZ_layout.addWidget(left_top, 0, 0)
        column_PTZ_layout.addWidget(move_up, 0, 1)
        column_PTZ_layout.addWidget(right_top, 0, 2)
        column_PTZ_layout.addWidget(move_left, 1, 0)
        column_PTZ_layout.addWidget(around, 1, 1)
        column_PTZ_layout.addWidget(move_right, 1, 2)
        column_PTZ_layout.addWidget(left_bottom, 2, 0)
        column_PTZ_layout.addWidget(move_down, 2, 1)
        column_PTZ_layout.addWidget(right_bottom, 2, 2)

        column_PTZ_layout.setSpacing(2)
        self.layout_grid.addLayout(column_PTZ_layout, 0, 0)

        # Cột thứ hai - Grid View 3x2

        self.column_option_layout = QGridLayout()  # Sử dụng QGridLayout cho cột thứ hai

        zoom_in = SquareButton(
            icon=QIcon(self.list_icon_option[0]),callback_click=partial(self.callback_click,self.list_action_option[0]),callback_exit_click=partial(self.callback_exit_click,self.list_action_option[0])
        )
        zoom_out = SquareButton(
            icon=QIcon(self.list_icon_option[1]),callback_click=partial(self.callback_click,self.list_action_option[1]),callback_exit_click=partial(self.callback_exit_click,self.list_action_option[1])
        )

        focus_near = SquareButton(
            icon=QIcon(self.list_icon_option[2]),callback_click=partial(self.callback_click,self.list_action_option[2]),callback_exit_click=partial(self.callback_exit_click,self.list_action_option[2])
        )
        focus_near.hide()
        focus_far = SquareButton(
            icon=QIcon(self.list_icon_option[3]),callback_click=partial(self.callback_click,self.list_action_option[3]),callback_exit_click=partial(self.callback_exit_click,self.list_action_option[3])
        )
        focus_far.hide()
        iris_add = SquareButton(
            icon=QIcon(self.list_icon_option[4]),callback_click=partial(self.callback_click,self.list_action_option[4]),callback_exit_click=partial(self.callback_exit_click,self.list_action_option[4])
        )
        iris_add.hide()
        iris_not_add = SquareButton(
            icon=QIcon(self.list_icon_option[5]),callback_click=partial(self.callback_click,self.list_action_option[5]),callback_exit_click=partial(self.callback_exit_click,self.list_action_option[5])
        )
        iris_not_add.hide()
        self.column_option_layout.addWidget(focus_near,1,0)
        self.column_option_layout.addWidget(focus_far,1,1)
        self.column_option_layout.addWidget(zoom_in,2,0)
        self.column_option_layout.addWidget(zoom_out,2,1)
        self.column_option_layout.addWidget(iris_add,3,0)
        self.column_option_layout.addWidget(iris_not_add,3,1)

        self.column_option_layout.setSpacing(2)
        self.layout_grid.addLayout(self.column_option_layout, 1, 0)  # Thêm cột thứ hai vào layout chính

    def setup_ui_preset_patrol(self):
        self.btn_preset = QPushButton(icon=QIcon(main_controller.get_theme_attribute("Image", "preset")))
        self.btn_preset.setIconSize(QSize(25, 25))
        self.btn_preset.clicked.connect(lambda: self.btn_control_clicked(0))
        self.btn_preset.setStyleSheet(Style.StyleSheet.button_style8)

        self.btn_patrol = QPushButton(icon=QIcon(main_controller.get_theme_attribute("Image", "patrol")))
        self.btn_patrol.setIconSize(QSize(25, 25))
        self.btn_patrol.clicked.connect(lambda: self.btn_control_clicked(1))
        self.btn_patrol.setStyleSheet(Style.StyleSheet.button_style7)
        self.btn_pattern = QPushButton(icon=QIcon(main_controller.get_theme_attribute("Image", "pattern")))
        self.btn_pattern.setIconSize(QSize(25, 25))
        self.btn_pattern.clicked.connect(lambda: self.btn_control_clicked(2))
        self.btn_pattern.setStyleSheet(Style.StyleSheet.button_style7)
        self.preset_layout.addWidget(self.btn_preset)
        self.preset_layout.addWidget(self.btn_patrol)
        self.preset_layout.addWidget(self.btn_pattern)
        self.stackedwidget = QStackedWidget()
        self.preset = PresetListView(self,list_icon=self.get_list_icon())
        self.preset.setSelectionMode(QAbstractItemView.NoSelection)
        self.data_presets = []
        self.preset.clicked.connect(self.handle_item_clicked)
        #self.preset.doubleClicked.connect(self.handle_item_doubleclicked)
        self.preset.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        # for index in range(32):
        #     self.preset.update_preset_below(name= f'preset {index}')
        self.preset.setStyleSheet(f"QListView::item {{ background-color: transparent;color: {Style.PrimaryColor.on_background};border-bottom: 1px solid {Style.PrimaryColor.on_background}; }}"
                                  f"QListView {{ background-color: transparent;color:{Style.PrimaryColor.on_background}; border: 1px solid {Style.PrimaryColor.on_background}}}"
                                  f"QLabel {{ color:#FFFFFF;}}"
                                  f"QLineEdit {{ background-color: transparent;color:#FFFFFF;}}")
        self.patrol = QStackedWidget()
        self.list_patrol_widget = PresetListView(self,list_icon=self.get_list_icon(mode= 'patrol'))
        self.list_patrol_widget.setSelectionMode(QAbstractItemView.NoSelection)
        self.data_patrols = []
        self.list_patrol_widget.clicked.connect(self.handle_item_clicked)
        #self.preset.doubleClicked.connect(self.handle_item_doubleclicked)
        self.list_patrol_widget.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        # for index in range(32):
        #     self.preset.update_preset_below(name= f'preset {index}')
        self.list_patrol_widget.setStyleSheet(
            f"QListView::item {{ background-color: transparent;color: {Style.PrimaryColor.on_background};border-bottom: 1px solid {Style.PrimaryColor.on_background}; }}"
              f"QListView {{ background-color: transparent;color:{Style.PrimaryColor.on_background}; border: 1px solid {Style.PrimaryColor.on_background}}}"
              f"QLabel {{ color:#FFFFFF;}}"
              f"QLineEdit {{ background-color: transparent;color:#FFFFFF;}}")
        self.setting_patrol_widget = QWidget()
        self.setting_patrol_layout = QVBoxLayout(self.setting_patrol_widget)
        self.setting_patrol_widget.setObjectName("setting_patrol_widget")
        self.setting_patrol_widget.setStyleSheet(
            f"QLabel {{ color:#FFFFFF;}}"
            f"QWidget#setting_patrol_widget {{ background-color: transparent;border:1px solid {Style.PrimaryColor.on_background};}}")
        self.create_ui_setting_patrol()
        self.patrol.addWidget(self.list_patrol_widget)
        self.patrol.addWidget(self.setting_patrol_widget)
        self.pattern = QLabel('Not Supported')
        self.pattern.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.pattern.setStyleSheet("QLabel { color:#FFFFFF;}")
        self.stackedwidget.addWidget(self.preset)
        self.stackedwidget.addWidget(self.patrol)
        self.stackedwidget.addWidget(self.pattern)
        self.preset_patrol_layout.addLayout(self.preset_layout)
        self.preset_patrol_layout.addWidget(self.stackedwidget)

    def setup_ui_ptz_advance(self):
        self.list_icon_ptz_advance = [
            main_controller.get_theme_attribute("Image", "ptz_advance_brightness"),
            main_controller.get_theme_attribute("Image", "ptz_advance_contrast"),
            main_controller.get_theme_attribute("Image", "ptz_advance_sharpness"),
            main_controller.get_theme_attribute("Image", "ptz_advance_saturation"),
            main_controller.get_theme_attribute("Image", "ptz_advance_menu")
        ]
        self.list_action_ptz_advance = [
            'brightness',
            'contrast',
            'sharpnest',
            'saturation',
            'menu',
        ]
        brightness = SquareButton(
            icon=QIcon(self.list_icon_ptz_advance[0]),callback_click=partial(self.callback_ptz_advance,self.list_action_ptz_advance[0]),callback_exit_click=None
        )
        #move_up
        contrast = SquareButton(
            icon=QIcon(self.list_icon_ptz_advance[1]),callback_click=partial(self.callback_ptz_advance,self.list_action_ptz_advance[1]),callback_exit_click=None
        )
        #right_top
        sharpness = SquareButton(
            icon=QIcon(self.list_icon_ptz_advance[2]),callback_click=partial(self.callback_ptz_advance,self.list_action_ptz_advance[2]),callback_exit_click=None
        )
        #move_left
        saturation = SquareButton(
            icon=QIcon(self.list_icon_ptz_advance[3]),callback_click=partial(self.callback_ptz_advance,self.list_action_ptz_advance[3]),callback_exit_click=None
        )
        menu = SquareButton(
            icon=QIcon(self.list_icon_ptz_advance[4]),callback_click=partial(self.callback_ptz_advance,self.list_action_ptz_advance[3]),callback_exit_click=None
        )
        self.ptz_advance_layout.addWidget(brightness)
        self.ptz_advance_layout.addWidget(contrast)
        self.ptz_advance_layout.addWidget(sharpness)
        self.ptz_advance_layout.addWidget(saturation)
        self.ptz_advance_layout.addWidget(menu)

    def remove_grid_layout(self):
        while self.grid_layout.count():
            item = self.grid_layout.takeAt(0)
            widget_to_remove = item.widget()
            if widget_to_remove:
                widget_to_remove.setParent(None)
            else:
                widget_to_remove.layout().setParent(None)
        self.grid_layout.addWidget(QLabel('Preset'), 0, 0, Qt.AlignmentFlag.AlignCenter)
        self.grid_layout.addWidget(QLabel('Speed'), 0, 1, Qt.AlignmentFlag.AlignCenter)
        self.grid_layout.addWidget(QLabel('Time (s)'), 0, 2, Qt.AlignmentFlag.AlignCenter)
        self.row = 0
        self.list_preset = []

    def get_setting_patrol(self):
        sum = self.grid_layout.count() + 1
        row_sum = sum//3
        # logger.debug(f'row_sum = {row_sum}')
        if row_sum > 1:
            count = 1
            while row_sum != count:
                combobox_item = self.grid_layout.itemAtPosition(count,0)
                combobox = combobox_item.widget()
                speed_item = self.grid_layout.itemAtPosition(count,1)
                speed = speed_item.widget()
                time_item = self.grid_layout.itemAtPosition(count,2)
                time = time_item.widget()
                count += 1
                preset = {}
                preset['preset_index'] = combobox.currentIndex() + 1
                preset['speed'] = int(speed.text())
                preset['time'] = int(time.text())
                self.list_preset.append(preset)

    def load_data_patrol(self, index = None):
        self.row = 0
        self.data_patrols = Camera_Qsettings.get_instance().get_patrols(camera_id= self.camera_id)
        self.list_preset = []
        for idx, item in enumerate(self.data_patrols):
            if index == idx + 1:
                logger.debug(item['name'])
                self.item_control.label_name.setText(item['name'])
                if item['list_preset'] != None:
                    self.data_presets = self.ptz_onvif.get_presets(ProfileToken = self.profileToken)
                    if self.data_presets is not None:
                        for preset in item['list_preset']:
                            self.add_preset(preset_index = preset['preset_index'],speed= preset['speed'],time= preset['time'])
                else:
                    pass
                    #self.list_preset = []

    def create_ui_setting_patrol(self):
        self.setting_patrol_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        #self.setting_patrol_widget.setStyleSheet("QLabel { color:#FFFFFF;}")
        self.setting_patrol_layout.setContentsMargins(0,0,0,0)
        self.item_control = PresetItem(name = 'f',index = 1,list_icon=self.get_list_icon(mode='setting_patrol'))
        self.item_control.label_name.setReadOnly(True)
        self.item_control.label_name.setStyleSheet("QLineEdit { background-color: transparent;color:#FFFFFF;}")

        self.grid_layout = QGridLayout()
        self.grid_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.grid_layout.addWidget(QLabel('Preset'), 0, 0, Qt.AlignmentFlag.AlignCenter)
        self.grid_layout.addWidget(QLabel('Speed'), 0, 1, Qt.AlignmentFlag.AlignCenter)
        self.grid_layout.addWidget(QLabel('Time (s)'), 0, 2, Qt.AlignmentFlag.AlignCenter)
        self.control_layout = QHBoxLayout()
        self.btn_ok = QPushButton('Ok')
        self.btn_ok.setFixedSize(60,25)
        self.btn_ok.setStyleSheet(Style.StyleSheet.button_style1)
        self.btn_ok.clicked.connect(self.btn_ok_clicked)
        self.btn_cancel = QPushButton('Cancel')
        self.btn_cancel.setFixedSize(60,25)
        self.btn_cancel.setStyleSheet(Style.StyleSheet.button_style9)
        self.btn_cancel.clicked.connect(self.btn_cancel_clicked)
        self.control_layout.addWidget(self.btn_ok)
        self.control_layout.addWidget(self.btn_cancel)
        self.control_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setting_patrol_layout.addWidget(self.item_control.main_widget,10)
        self.setting_patrol_layout.addLayout(self.grid_layout,80)
        self.setting_patrol_layout.addLayout(self.control_layout,10)

    def btn_ok_clicked(self):
        # logger.debug(f'btn_ok_clicked')
        # logger.debug(f'camera_id = {self.camera_id} index = {self.index} list_preset = {self.list_preset}')
        self.get_setting_patrol()
        Camera_Qsettings.get_instance().add_list_preset(camera_id=self.camera_id,index = self.index,list_preset= self.list_preset)
        self.patrol.setCurrentIndex(0)


    def btn_cancel_clicked(self):
        logger.debug(f'btn_calcel_clicked')
        self.patrol.setCurrentIndex(0)

    def add_preset(self,preset_index = None,speed = None,time = None):
        #logger.debug(f'preset_index = {preset_index} speed = {speed} time = {time}')
        self.row += 1
        combobox = QComboBox()
        for index, item in enumerate(self.data_presets):
            combobox.addItem(item.Name)
        #combobox.setStyleSheet("border: 1px solid #B5122E;border-radius: 6px;")
        self.grid_layout.addWidget(combobox, self.row, 0, Qt.AlignmentFlag.AlignCenter)
        if preset_index != None:
            combobox.setCurrentIndex(preset_index - 1)
        speed_widget = QLineEdit('15') if speed == None else QLineEdit(str(speed))
        #speed_widget.setStyleSheet("border: 1px solid #B5122E;border-radius: 6px;")
        time_widget = QLineEdit('15') if time == None else QLineEdit(str(time))
        #time_widget.setStyleSheet("border: 1px solid #B5122E;border-radius: 6px;")
        self.grid_layout.addWidget(speed_widget, self.row, 1, Qt.AlignmentFlag.AlignCenter)
        self.grid_layout.addWidget(time_widget, self.row, 2, Qt.AlignmentFlag.AlignCenter)

    def get_list_icon(self,mode = 'preset'):
        if mode == 'preset':
            list_icon = []
            icon1 = {'direction': 'call_preset',
                     'icon': [main_controller.get_theme_attribute("Image", "call_preset"), Style.PrimaryImage.call_preset_hover],
                     'callback': self.callback_icon_clicked}
            icon2 = {'direction': 'setting_preset',
                     'icon': [main_controller.get_theme_attribute("Image", "setting_preset"), Style.PrimaryImage.setting_preset_hover],
                     'callback': self.callback_icon_clicked}
            icon3 = {'direction': 'delete_preset',
                     'icon': [main_controller.get_theme_attribute("Image", "delete_preset"), Style.PrimaryImage.delete_preset_hover],
                     'callback': self.callback_icon_clicked}
            list_icon.append(icon1)
            list_icon.append(icon2)
            list_icon.append(icon3)
            return list_icon
        elif mode == 'patrol':
            list_icon = []
            icon1 = {'direction': self.list_direction[3],
                     'icon': [main_controller.get_theme_attribute("Image", "play_patrol"), Style.PrimaryImage.play_patrol_hover],
                     'callback': self.callback_icon_clicked}
            icon2 = {'direction': self.list_direction[4],
                     'icon': [main_controller.get_theme_attribute("Image", "stop_patrol"), Style.PrimaryImage.stop_patrol_hover],
                     'callback': self.callback_icon_clicked}
            icon3 = {'direction': self.list_direction[5],
                     'icon': [main_controller.get_theme_attribute("Image", "setting_preset"), Style.PrimaryImage.setting_preset_hover],
                     'callback': self.callback_icon_clicked}
            icon4 = {'direction': self.list_direction[6],
                     'icon': [main_controller.get_theme_attribute("Image", "delete_preset"), Style.PrimaryImage.delete_preset_hover],
                     'callback': self.callback_icon_clicked}
            list_icon.append(icon1)
            list_icon.append(icon2)
            list_icon.append(icon3)
            list_icon.append(icon4)
            return list_icon

        elif mode == 'setting_patrol':
            list_icon = []
            icon1 = {'direction': self.list_direction[7],
                     'icon': [Style.PrimaryImage.add_preset, Style.PrimaryImage.add_preset],
                     'callback': self.callback_icon_clicked}
            icon2 = {'direction': self.list_direction[8],
                     'icon': [Style.PrimaryImage.delete_preset_patrol, Style.PrimaryImage.delete_preset_patrol],
                     'callback': self.callback_icon_clicked}
            icon3 = {'direction': self.list_direction[9],
                     'icon': [Style.PrimaryImage.down_preset, Style.PrimaryImage.down_preset],
                     'callback': self.callback_icon_clicked}
            icon4 = {'direction': self.list_direction[10],
                     'icon': [Style.PrimaryImage.up_preset, Style.PrimaryImage.up_preset],
                     'callback': self.callback_icon_clicked}
            list_icon.append(icon1)
            list_icon.append(icon2)
            list_icon.append(icon3)
            list_icon.append(icon4)
            return list_icon
    def callback_icon_clicked(self,direction = None, name = None, index = None):
        if self.ptz_onvif is not None and self.profileToken is not None:
            if direction == self.list_direction[0]:
                logger.debug(f'call_preset')
                self.ptz_onvif.goto_preset(index = index, profiletoken = self.profileToken)
            elif direction == self.list_direction[1]:
                logger.debug(f'setting_preset')
                self.ptz_onvif.set_preset(name = name, index = index, profiletoken = self.profileToken)
            elif direction == self.list_direction[2]:
                logger.debug(f'delete_preset')
                self.ptz_onvif.remove_preset(index = index, profiletoken = self.profileToken)
                self.data_presets = self.ptz_onvif.get_presets(ProfileToken = self.profileToken)
                if self.data_presets is not None:
                    return None
                else:
                    return self.data_presets[index - 1].Name
            elif direction == self.list_direction[3]:
                logger.debug(f'play_patrol')
                if not self.is_goto_patrol:
                    thread = threading.Thread(target=self.goto_patrol,args=(index,))
                    thread.start()
            elif direction == self.list_direction[4]:
                logger.debug(f'stop_patrol')
                self.ptz_onvif.stop_patrol = True
            elif direction == self.list_direction[5]:
                self.index = index
                self.patrol.setCurrentIndex(1)
                self.remove_grid_layout()
                self.load_data_patrol(index)
            elif direction == self.list_direction[6]:
                logger.debug(f'delete_patrol')
                self.index = index
                Camera_Qsettings.get_instance().add_list_preset(camera_id=self.camera_id,index = index,list_preset= [])
                #self.remove_grid_layout()
            elif direction == self.list_direction[7]:
                self.add_preset()
            elif direction == self.list_direction[8]:
                self.remove_grid_layout()
            elif direction == self.list_direction[9]:
                logger.debug(f'up_preset')
                self.remove_grid_layout()
                for idx, item in enumerate(self.data_patrols):
                    if self.index == idx + 1:
                        if item['list_preset'] != None:
                            list_revert = item['list_preset']
                            list_revert.reverse()
                            Camera_Qsettings.get_instance().add_list_preset(camera_id=self.camera_id,index = self.index,list_preset= list_revert)
                            self.load_data_patrol(self.index)
            elif direction == self.list_direction[10]:
                logger.debug(f'down_preset')
                self.remove_grid_layout()
                for idx, item in enumerate(self.data_patrols):
                    if self.index == idx + 1:
                        if item['list_preset'] != None:
                            logger.debug(f'index_down_preset = {index}')
                            list_revert = item['list_preset']
                            list_revert.reverse()
                            list_revert = item['list_preset']
                            Camera_Qsettings.get_instance().add_list_preset(camera_id=self.camera_id,index = self.index,list_preset= list_revert)
                            self.load_data_patrol(self.index)
        return None
    def handle_item_clicked(self,index):
        # Lấy item được chọn từ model
        if self.item != None:
            try:
                self.item.hide_control()
            except Exception as e:
                logger.debug(f'handle_item_clicked = {e}')
        current_index = self.stackedwidget.currentIndex()
        if current_index == 0:
            self.item = self.preset.list_view_model.itemFromIndex(index)
            self.item.show_control()
        if current_index == 1:
            self.item = self.list_patrol_widget.list_view_model.itemFromIndex(index)
            self.item.show_control()

    def goto_patrol(self,index):
        self.is_goto_patrol = True
        self.data_patrols = Camera_Qsettings.get_instance().get_patrols(camera_id= self.camera_id)
        for idx, item in enumerate(self.data_patrols):
            if index == idx + 1:
                if item['list_preset'] != None:
                    self.ptz_onvif.goto_patrol(ProfileToken = self.profileToken,list_preset = item['list_preset'])
        self.is_goto_patrol = False

    def btn_control_clicked(self,index):
        self.stackedwidget.setCurrentIndex(index)
        self.btn_preset.setStyleSheet(Style.StyleSheet.button_style7)
        self.btn_patrol.setStyleSheet(Style.StyleSheet.button_style7)
        self.btn_pattern.setStyleSheet(Style.StyleSheet.button_style7)
        if index == 0:
            self.btn_preset.setStyleSheet(Style.StyleSheet.button_style8)
        elif index == 1:
            self.btn_patrol.setStyleSheet(Style.StyleSheet.button_style8)
        else:
            self.btn_pattern.setStyleSheet(Style.StyleSheet.button_style8)

    def callback_ptz_advance(self,direction = None):
        logger.debug('callback_ptz_advance')
        # tinh nang nay de sau lam tiep

        # layout.addLayout(row3_layout, 1, 0, 1, 2)
        # self.hide_ptz_control('Standard')
    def setup_ui_control_speed(self):
        self.down_speed = QPushButton()
        self.down_speed.setFixedWidth(20)
        self.down_speed.clicked.connect(self.down_speed_clicked)
        self.up_speed = QPushButton()
        self.up_speed.setFixedWidth(20)
        self.up_speed.clicked.connect(self.up_speed_clicked)
        self.down_speed.setStyleSheet(f'''
                                QPushButton {{background-color: {Style.PrimaryColor.on_background};border: None;border-radius: 2px;image: url({main_controller.get_theme_attribute("Image", "down_speed")});}}
                                QPushButton:hover {{background-color: {Style.PrimaryColor.on_hover_secondary};border-radius: 2px;image: url({main_controller.get_theme_attribute("Image", "down_speed")});}}
                                ''')
        self.up_speed.setStyleSheet(f'''
                                QPushButton {{background-color: {Style.PrimaryColor.on_background};border: None;border-radius: 2px; image: url({main_controller.get_theme_attribute("Image", "up_speed")});}}
                                QPushButton:hover {{background-color: {Style.PrimaryColor.on_hover_secondary};border-radius: 2px;image: url({main_controller.get_theme_attribute("Image", "up_speed")});}}
                                ''')
        self.speed = NoScrollSlider(Qt.Horizontal, self)
        self.speed.setMinimum(0)
        self.speed.setMaximum(10)
        self.speed.setValue(5)
        self.speed.setStyleSheet(Style.StyleSheet.slider_style)
        self.control_speed_layout.addWidget(self.down_speed,10)
        self.control_speed_layout.addWidget(self.speed,80)
        self.control_speed_layout.addWidget(self.up_speed,10)

    def down_speed_clicked(self):
        logger.debug(f'down_speed_clicked')
        current_value = self.speed.value()
        speed = current_value - 1
        if speed >= 0:
            self.speed.setValue(speed)

    def up_speed_clicked(self):
        logger.debug(f'up_speed_clicked')
        current_value = self.speed.value()
        speed = current_value + 1
        if speed <= 10:
            self.speed.setValue(speed)

    # def callback_click(self,direction = None,speed = None):
    #     logger.debug(f'callback_click')
    #     if self.ptz_onvif != None and self.profileToken != None:
    #         speed = self.speed.value()/10
    #         logger.debug(f'speed = {speed}')
    #         if self.ptz_onvif.is_done:
    #             self.ptz_onvif.is_done = False
    #             message = {"name_move": direction,"speed": speed, "profileToken": self.profileToken,'exit_click': False}
    #             self.ptz_onvif.request_queue.put(message)
    #             self.workerthread = WorkerThread(parent=self,callback=self.ptz_onvif.process_queue)
    #             self.workerthread.start()
    #         else:
    #             message = {"name_move": direction,"speed": speed, "profileToken": self.profileToken,'exit_click': False}
    #             # self.ptz_onvif.request_queue.put(self.message)
    # def callback_exit_click(self,direction = None):
    #     # if self.message:
    #     message = {"name_move": direction,"speed": None, "profileToken": self.profileToken,'exit_click': True}
    #     self.ptz_onvif.request_queue.put(message)
    #     self.workerthread = WorkerThread(parent=self,callback=self.ptz_onvif.process_queue)
    #     self.workerthread.start()
    #     logger.debug(f'callback_exit_click = {self.message}')
    #         # self.message['exit_click'] = True

    def callback_click(self,direction = None,speed = None):
        # logger.debug(f'callback_click')
        # if self.profileToken != None or self.camera_model.data.type == "AVIGILON":
        if self.camera_model.data.ptzCap is not None and len(self.camera_model.data.ptzCap) > 0:
            speed = self.speed.value()/10
            # logger.debug(f'callback_click= {direction,speed}')
            if direction == 'move_left':
                x = -speed
                y = 0
                zoom = None
            elif direction == 'move_right':
                x = speed
                y = 0
                zoom = None
            elif direction == 'move_up':
                x = 0
                y = speed
                zoom = None
            elif direction == 'move_down':
                x = 0
                y = -speed
                zoom = None
            elif direction == 'zoom_in':
                x = 0
                y = 0
                zoom = speed
            elif direction == 'zoom_out':
                x = 0
                y = 0
                zoom = -speed
            elif direction == 'left_top':
                x = -speed
                y = speed
                zoom = None
            elif direction == 'right_top':
                x = speed
                y = speed
                zoom = None
            elif direction == 'left_bottom':
                x = -speed
                y = -speed
                zoom = None
            elif direction == 'right_bottom':
                x = speed
                y = -speed
                zoom = None
            elif direction == 'around':
                x = speed
                y = 0
                zoom = None
            else:
                logger.debug(f'callback_clic1k= {direction,speed}')
                x = 0
                y = 0
                zoom = 0
            if self.camera_model.data.type == "AVIGILON":
                data = {
                    "cameraId": self.camera_model.data.id,
                    "endPoint": "/camera/commands/pan-tilt-zoom",
                    "requestData": {
                        "continuous": {
                            "panAmount": x,
                            "tiltAmount": -y,
                            "zoomAmount": 0 if zoom == None else zoom,
                            "action": "START"
                            }
                    }
                }
                self.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
            else:
                self.controller.ptz_continuous_move(parent=self,cameraId=self.camera_model.data.id, x = x, y = y,zoom = zoom)

    def callback_exit_click(self,direction = None):
        # logger.debug(f'callback_exit_click')
        if self.camera_model.data.type == "AVIGILON":
            data = {
                "cameraId": self.camera_model.data.id,
                "endPoint": "/camera/commands/pan-tilt-zoom",
                "requestData": {
                    "continuous": {
                        "panAmount": 0,
                        "tiltAmount": 0,
                        "zoomAmount": 0,
                        "action": "STOP"
                        }
                }
            }
            self.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
        else:
            self.controller.ptz_stop(cameraId=self.camera_model.data.id)

    def hide_ptz_control(self, data):
        if(data == 'Standard'):
            self.column_option_layout.itemAtPosition(3, 0).widget().setVisible(False)
            self.column_option_layout.itemAtPosition(3, 1).widget().setVisible(False)
        else:
            self.column_option_layout.itemAtPosition(3, 0).widget().setVisible(True)
            self.column_option_layout.itemAtPosition(3, 1).widget().setVisible(True)

    def button_click(self,action):
        self.button_move(action)


    def button_move(self, direction=None):
        logger.debug(f'direction = {direction}')
                # self.my_thread.connect(
        #     PTZWidget.ip_camera, 80, PTZWidget.username, PTZWidget.password, PTZWidget.index)
        #self.my_thread.control_ptz(direction)
        if self.ptz_onvif != None and self.profileToken != None:
            self.ptz_onvif.control_ptz(name_move = direction,profileToken = self.profileToken)
        else:
            logger.debug(f'Khong nhan biet duoc camera nay')
        # self.my_thread.connect(
        #     PTZWidget.ip_camera, 80, PTZWidget.username, PTZWidget.password, PTZWidget.index)
        # self.my_thread.control_ptz(direction)

    def handle_thread_finished(self):
        # Thực hiện các thao tác sau khi thread kết thúc
        self.thread_running = False
        pass
    def callback_image_adj(self,):
        if self.ptz_onvif != None and self.profileToken != None:
            brightness = self.image_adj_widget.brightness.slider.value()
            sharpness = self.image_adj_widget.sharpness.slider.value()
            contrast = self.image_adj_widget.contrast.slider.value()
            saturation = self.image_adj_widget.color_saturation.slider.value()
            self.ptz_onvif.set_image_adj(brightness = brightness,sharpness = sharpness,contrast = contrast,saturation = saturation, profileToken = self.profileToken)
    def get_list_slider(self):
        if self.ptz_onvif != None and self.profileToken != None:
            range_image_adj = self.ptz_onvif.get_range_image_adj(self.profileToken)
            image_adj = self.ptz_onvif.get_imaging_settings(self.profileToken)
            #logger.debug(f'image_adj = {image_adj}')
            range_image_adj.Brightness['Value'] = int(image_adj.Brightness)
            range_image_adj.Sharpness['Value'] = int(image_adj.Sharpness)
            range_image_adj.Contrast['Value'] = int(image_adj.Contrast)
            range_image_adj.ColorSaturation['Value'] = int(image_adj.ColorSaturation)
            return {'brightness': range_image_adj.Brightness,'sharpness': range_image_adj.Sharpness,'contrast': range_image_adj.Contrast,'saturation': range_image_adj.ColorSaturation}
        return None
    def get_image_adj(self):
        if self.ptz_onvif != None and self.profileToken != None:
            list_sliders = self.get_list_slider()
            self.image_adj_widget.update_slider(list_sliders=list_sliders)
    def get_presets(self):
        logger.debug(f'get_presets =')
        if self.data_presets != None and len(self.data_presets) != 0:
            self.data_presets = []
            self.preset.remove_preset()
        if self.ptz_onvif != None and self.profileToken != None:
            self.data_presets = self.ptz_onvif.get_presets(ProfileToken = self.profileToken)
            if self.data_presets is not None:
                for index, item in enumerate(self.data_presets):
                    self.preset.update_preset_below(name= item.Name,index=index + 1)

    def get_patrols(self):
        logger.debug(f'get_presets =')
        if len(self.data_patrols) != 0:
            self.data_patrols = []
            self.list_patrol_widget.remove_preset()
        if self.ptz_onvif != None and self.profileToken != None and self.camera_id != None:
            self.data_patrols = Camera_Qsettings.get_instance().get_patrols(camera_id= self.camera_id)
            for index, item in enumerate(self.data_patrols):
                self.list_patrol_widget.update_preset_below(name= item['name'],index=index + 1)

    def get_ptz_data_thread(self):
        thread = WorkerThread(parent=self, callback=self.get_ptz_data)
        thread.start()
    # update_ptz_data: Update Ptz data (Image Adj, Preset, Patrol) len UI
    def update_ptz_data(self, data):
        # Xoa data va UI preset truoc do
        if self.data_presets != None and len(self.data_presets) != 0:
            self.data_presets = []
            self.preset.remove_preset()
        # Xoa data va UI Patrol truoc do
        if len(self.data_patrols) != 0:
            self.data_patrols = []
            self.list_patrol_widget.remove_preset()
        # Update data va UI Image Adj
        list_sliders = data['range_image_adj']
        self.image_adj_widget.update_slider(list_sliders=list_sliders)
        # Update data va UI Preset
        # if self.ptz_onvif != None and self.profileToken != None:
        #     self.data_presets = data['data_presets']
        #     if self.data_presets is not None:
        #         for index, item in enumerate(self.data_presets):
        #             self.preset.update_preset_below(name= item.Name,index=index + 1)
        # Update data va UI Patrol
        # if self.ptz_onvif != None and self.profileToken != None and self.camera_id != None:
        #     self.data_patrols = data['data_patrols']
        #     for index, item in enumerate(self.data_patrols):
        #         self.list_patrol_widget.update_preset_below(name= item['name'],index=index + 1)

    def get_ptz_data(self):
        range_image_adj = self.get_list_slider()
        # data_presets = self.ptz_onvif.get_presets(ProfileToken = self.profileToken)
        # data_patrols = Camera_Qsettings.get_instance().get_patrols(camera_id= self.camera_id)
        # ptz_data = {'range_image_adj': range_image_adj,'data_presets': data_presets, 'data_patrols': data_patrols}
        ptz_data = {'range_image_adj': range_image_adj,'data_presets': None, 'data_patrols': None}
        # logger.debug(f'ffffff = {ptz_data}')
        self.signal_data_ptz.emit(ptz_data)
        #self.update_ptz_data(ptz_data)

class PTZ_Dropdow(QWidget):
    def __init__(self):
        super().__init__()
        self.is_hidden = False

        # Tạo frame chứa nút button
        self.frame = QFrame()
        self.frame.setFrameShape(QFrame.Panel)
        self.frame.setFrameShadow(QFrame.Sunken)

        self.frame.setStyleSheet("border: none")
        self.ptz_widget = PTZWidget(parent_widget=self)

        #self.ptz_widget.setStyleSheet("background:red")

        self.layouts = QVBoxLayout(self.frame)
        self.layouts.setAlignment(Qt.AlignTop)
        self.layouts.setContentsMargins(0, 0, 0, 0)
        self.layouts.addWidget(self.ptz_widget)
        # Tạo nút dropdown
        self.dropdown_button = QPushButton()
        self.dropdown_button.setIcon(QIcon(main_controller.get_theme_attribute("Image", "drop_dow"),))
        self.dropdown_button.setFixedSize(10,10)
        self.dropdown_button.setStyleSheet("background-color: transparent;border: none")
        self.dropdown_button.clicked.connect(self.dropdown_button_clicked)

        self.label_ptz = QLabel("PTZ")
        self.label_ptz.setStyleSheet("color:white")
        self.drop_dow = QHBoxLayout()
        self.drop_dow.setContentsMargins(4,0,0,0)
        self.drop_dow.addWidget(self.dropdown_button )
        self.drop_dow.addWidget(self.label_ptz)

        self.contain = QWidget()
        self.contain.setLayout(self.drop_dow)

        main_layout = QVBoxLayout(self)
        main_layout.addWidget(self.contain)
        main_layout.addWidget(self.frame)
        main_layout.setContentsMargins(0,0,0,0)

        self.setLayout(main_layout)
        #self.toggle_widget()


    def hide_ptz(self,data):
        self.ptz_widget.hide_ptz_control(data)

    def dropdown_button_clicked(self):
        logger.debug('dropdown_button_clicked')
        current_tab = main_controller.current_tab
        # kiem tra xem da chon camera hay chua
        if not grid_item_selected.is_tab_index(current_tab):
                # chua co camera nao duoc chon
                Notifications(parent=main_controller.list_parent['HomeScreen'], title=self.tr('Please select Camera.'),icon=Style.PrimaryImage.info_result)
        else:
            if grid_item_selected.data['tab_index'] is None:
                # chua co camera nao duoc chon
                Notifications(parent=main_controller.list_parent['HomeScreen'], title=self.tr('Please select Camera.'),icon=Style.PrimaryImage.info_result)
            else:
                camera_widget = grid_item_selected.data['widget']
                if camera_widget.camera_model.ipAddress == None:
                    Notifications(parent=main_controller.list_parent['HomeScreen'], title=self.tr('This camera does not support PTZ.'),icon=Style.PrimaryImage.info_result)
                else:
                    self.toggle_widget()

    def toggle_widget(self):
        # logger.debug('toggle_widget')
        if self.is_hidden:
            self.frame.setVisible(True)
            self.setFixedHeight(200)
            self.ptz_widget.grid_ptz_widget.show()
            self.ptz_widget.control_speed_widget.show()
            self.ptz_widget.ptz_advance_widget.hide()
            self.ptz_widget.preset_patrol_widget.hide()
            self.ptz_widget.image_adj_widget.hide()
            self.is_hidden = False
            self.dropdown_button.setIcon(QIcon(main_controller.get_theme_attribute("Image", "drop_dow"),))
            # self.ptz_widget.get_presets()
            # self.ptz_widget.get_patrols()
            # self.ptz_widget.get_image_adj()
            self.ptz_widget.get_ptz_data_thread()
        else:
            self.frame.setVisible(False)
            self.setFixedHeight(40)
            self.is_hidden = True
            self.dropdown_button.setIcon(QIcon(main_controller.get_theme_attribute("Image", "drop_dow_right")))


class PtzDialog(QDialog):
    def __init__(self, parent=None,controller = None,camera_model = None):
        super().__init__(parent, Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TransparentForMouseEvents)
        self.setFixedHeight(210)
        self.setFixedWidth(210)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.controller = controller
        self.camera_model = camera_model
        self.setObjectName('PtzDialog')
        self.camera_widget = parent
        self.setStyleSheet(f'''
                                background-color: rgba(255, 255, 255, 0.2);
                           ''')
        layout_dialog = QVBoxLayout()
        layout_dialog.setContentsMargins(0,0,0,0)
        self.ptz_widget = PTZWidget(parent = self,controller = self.controller,camera_model = self.camera_model)
        self.ptz_widget.ptz_onvif= None
        # self.ptz_widget.ptz_onvif = camera_manager.get_ptz_onvif(camera_id=self.camera_widget.camera_id)
        self.ptz_widget.profileToken = self.camera_widget.camera_model.data.profileToken

        self.ptz_widget.grid_ptz_widget.show()
        self.ptz_widget.control_speed_widget.show()
        self.ptz_widget.get_ptz_data_thread()

        layout_dialog.addWidget(self.ptz_widget)
        self.setLayout(layout_dialog)
        self.setModal(False)
        QApplication.instance().installEventFilter(self)

    def is_ptz_avaiable(self):
        if self.ptz_widget.ptz_onvif is not None:
            return self.ptz_widget.ptz_onvif
        return None
    def eventFilter(self, source, event):
        if event.type() == QEvent.MouseButtonPress:
            # Kiểm tra xem vị trí của sự kiện chuột có nằm trong vùng của cửa sổ cha hay không

            global_pos = event.globalPos()
            if not self.geometry().contains(global_pos):
                QApplication.instance().removeEventFilter(self)
                self.close()
                self.camera_widget.btn_ptz.set_icon(main_controller.get_theme_attribute("Image", "icon_ptz_off"))
                self.camera_widget.ptz_dialog = None
                self.camera_widget.is_ptz_dialog = False
        return super().eventFilter(source, event)

    # def mousePressEvent(self,event):
    #     self.camera_widget.callback_mousePressEvent(event)

    # def mouseMoveEvent(self,event):
    #     self.camera_widget.callback_mouseMoveEvent(event)

    # def mouseReleaseEvent(self,event):
    #     self.camera_widget.callback_mouseReleaseEvent(event)

    def wheelEvent(self,event):
        self.camera_widget.callback_wheelEvent(event)

    def keyPressEvent(self,event):
        # logger.debug(f'keyPressEvent')
        self.camera_widget.callback_keyPressEvent(event)

    def keyReleaseEvent(self,event):
        # logger.debug(f'keyReleaseEvent')
        self.camera_widget.callback_keyReleaseEvent(event)

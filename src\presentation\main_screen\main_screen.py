from PySide6.QtGui import QKeyEvent
from src.common.model.scan_camera_model import CameraData
from src.common.controller.main_controller import main_controller,connect_slot
from src.common.controller.controller_manager import Controller, controller_manager
from src.common.widget.notifications.listen_message_notifications import listen_show_notification
from src.presentation.server_screen.server_screen import ServerScreen
from src.presentation.user_permissions_screen.user_permissions_screen import UserPermissionsScreen
from src.utils.config import Config
from src.utils.auth_qsettings import AuthQSettings
from src.utils.camera_qsettings import Camera_Qsettings,camera_qsettings
from src.api.api_client import HTTPStatusCode, LogErrorMessage, TypeRequestVMS
from src.styles.style import Style
from src.presentation.setting_screen.setting_screen import SettingScreen
from src.presentation.device_management_screen.device_screen import DeviceScreen
from src.presentation.camera_screen.camera_screen import CameraScreen
from src.common.widget.custom_side_menu import Tab<PERSON>istWidget
from src.common.websocket.websocket_client import <PERSON><PERSON><PERSON><PERSON>lient
from src.common.widget.notifications.notify import Notifications
from PySide6.QtCore import Signal, QUrl, Qt,QTimer
from PySide6.QtWidgets import QHBoxLayout, QWidget, QStackedWidget, QApplication, QProgressDialog, QDialog, \
    QVBoxLayout, QProgressBar, QLabel
from src.common.model.camera_model import Camera,CameraModel, camera_model_manager
from src.common.model.group_model import Group, GroupModel, group_model_manager
# from src.common.model.map_model import Floor,FloorModel,floor_manager,Building,BuildingModel,building_manager,Map,MapModel,map_manager
import logging
logger = logging.getLogger(__name__)
import time
from src.common.joystick.joystick_manager import joystick_manager
from src.common.model.event_data_model import EventAI, event_manager
from src.common.model.screen_model import ScreenModel, screenModelManager
class MainScreen(QWidget):
    signal_groups_data = Signal()
    signal_tree_group_data = Signal()
    signal_message_dialog = Signal(tuple)
    signal_warning_setting = Signal()

    def __init__(self, parent=None, window_parent=None):
        super().__init__(parent)
        self.window_parent = window_parent
        
        # create controller
        self.thread_check_onvif = None
        self.init_health_check_camera = False
        self.health_check_camera_thread = None
        self.image_list ={}
        self.start_time = time.time()
        self.current_time = time.time()
        self.timer = None

        self.dialog_load_data = None

        main_controller.list_parent['MainScreen'] = self
        main_controller.progress_dialog = QProgressDialog(
            self.tr("Processing..."), self.tr("Cancel"), 0, 0, self, flags=Qt.WindowType.Widget)
        main_controller.progress_dialog.setWindowFlag(Qt.FramelessWindowHint)
        # main_controller.progress_dialog.setAttribute(Qt.WA_TranslucentBackground)
        main_controller.progress_dialog.setStyleSheet(Style.StyleSheet.progress_dialog)
        main_controller.progress_dialog.setCancelButton(None)
        main_controller.progress_dialog.close()
        main_controller.change_server = self.change_server

        listen_show_notification.register_parent(self)
        self.list_ptz_onvif = []
        self.list_ipcamera = []
        self.list_camera_id = []
        # create joystick manager 
        joystick_manager.add_device_signal.connect(self.add_device_signal)
        joystick_manager.exit_device_signal.connect(self.exit_device_signal)
        self.load_data_from_qsetting()
        self.load_ui()
        self.connect_slot()
        
    def connect_slot(self):
        connect_slot(
            (main_controller.add_all_tab_signal, self.tab_widget.add_tab_slot),
            (main_controller.remove_all_tab_signal, self.tab_widget.remove_all_tab_slot),
            (main_controller.complete_fetching_data,self.complete_fetching_data),
            (main_controller.signal_load_animation, self.load_animation_fetch_data),
            (group_model_manager.add_group_list_signal,self.add_group_list_signal),
            (self.signal_tree_group_data,self.update_list_tree_group_data),
            (self.signal_message_dialog, self.message_dialog),
            (self.signal_warning_setting,self.update_list_warning_setting))
        main_controller.update_list_tree_group_data = self.send_to_signal_of_update_list_tree_group_data
        main_controller.show_message_dialog = self.send_to_signal_of_message_dialog
        main_controller.update_list_setting_warning_alert = self.send_signal_get_warning_setting_from_server

    def load_data_from_qsetting(self):
        if len(camera_qsettings.json_screens) == 0:
            output = {}
            json_screens = {}
            screens = QApplication.screens()
            for screen in screens:
                screen_index = QApplication.screens().index(screen)
                data = {"index": screen_index, "name":screen.name(), "state": False}
                json_screens[screen_index] = data
                screenModel = ScreenModel(data=data)
                screenModelManager.addScreen(screen=screenModel)
            output[0] = json_screens
            camera_qsettings.set_screen(json_screens=output)
        else:
            for idx, data in camera_qsettings.json_screens.items():
                if int(idx) == 0:
                    screenModelManager.isTracking = False
                else:
                    screenModelManager.isTracking = True
                for index, screen in data.items():
                    screenModel = ScreenModel(data=screen)
                    screenModelManager.addScreen(screen=screenModel)

    def load_animation_fetch_data(self, is_loading):
        if is_loading:
            if self.dialog_load_data is None:
                self.dialog_load_data = LoadingDataDialog(self)
                self.dialog_load_data.show()
        else:
            if self.dialog_load_data is not None:
                self.dialog_load_data.close()
                self.dialog_load_data = None

    def generate_url(self,server_url,path):
        if server_url.startswith('https://'):
            return server_url.replace('https://','wss://', 1) + path
        return server_url
    
    def complete_fetching_data(self, data):
        """
        Initializes and starts WebSocket clients based on configurations and server details.

        Args:
            data: The controller instance containing server and API client details.
        """
        controller: Controller = data
        ip = controller.server.data.server_ip
        port_server = controller.server.data.server_port
        
        if Config.ENABLE_WEBSOCKET_EVENT:
            url = (
                controller.api_client.websocket_url
                if controller.api_client.is_ip_address
                else self.generate_url(controller.api_client.server_url, '/socket')
            )
            headers = {
                'Authorization': f'Bearer {controller.api_client.access_token}',
                'Id': f'{controller.api_client.user_id}',
            }
            controller.websocket_event = WebsocketClient(
                                            url=url,
                                            header = headers
                                        )
            controller.websocket_event.connect_background()
        # WebSocket for VMS
        if Config.ENABLE_WEBSOCKET_VMS:
            if controller.api_client.is_ip_address:
                url_vms_ws = QUrl()
                url_vms_ws.setScheme("ws")  # Use "wss" for secure WebSocket if needed
                url_vms_ws.setHost(ip)
                url_vms_ws.setPort(int(port_server))
                url_vms_ws.setPath('/user/123')

                headers = {'Id': '123'}
                controller.websocket_vms = WebsocketClient(
                                                url=url_vms_ws.toString(),
                                                header = headers
                                            )
                controller.websocket_vms.connect_background()
            else:
                url_vms_ws = self.generate_url(controller.api_client.server_url, '/vms-service/user')
                headers = {'Authorization': f'Bearer {controller.api_client.access_token}'}
                controller.websocket_vms = WebsocketClient(url=url_vms_ws, header = headers)
                controller.websocket_vms.connect_background()

    def add_key_signal(self,data):

        key_event = QKeyEvent(QKeyEvent.KeyPress, data, Qt.NoModifier)
        QApplication.postEvent(self, key_event)

        release_event = QKeyEvent(QKeyEvent.KeyRelease, data, Qt.NoModifier)
        QApplication.postEvent(self, release_event)



    def add_device_signal(self,joy):
        # logger.debug(f'add_device_signal = {type(joy)}')
        Notifications(parent=self, title=f'{joy} Joystick {self.tr("is now connected")}',
                icon=Style.PrimaryImage.info_result)


    def exit_device_signal(self,joy):
        # logger.debug(f'exit_device_signal = {joy}')
        Notifications(parent=self, title=f'{joy} Joystick {self.tr("is now disconnected")}',
                icon=Style.PrimaryImage.info_result)

    def key_received_signal(self,key):
        pass
        # logger.debug(f'key_received_signal = {key}')
        # if key.keytype == Key.BUTTON:
        #     print(f'key.keytype = {key.keytype} key.number = {key.number}')
        # elif key.keytype == Key.AXIS:
        #     print(f'key.keytype = {key.keytype} key.number = {key.number} key.value = {key.value} ')
    
    def process_shortcut(self):
        pass

    def load_ui(self):
        self.layout = QHBoxLayout()
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(self.layout)

        self.stacked_widget = QStackedWidget()
        self.server_screen = ServerScreen(parent=self,window_parent=self.window_parent)
        self.camera_screen = CameraScreen(parent=self, window_parent=self.window_parent)
        self.device_screen = DeviceScreen(parent=self, window_parent=self.window_parent)
        self.user_permissions_screen = UserPermissionsScreen(parent=self, window_parent=self.window_parent)
        self.setting_screen = SettingScreen(parent=self, window_parent=self.window_parent)
        # self.setting_screen.logout_emit_signal.connect(main_controller.logout)
        self.setting_screen.change_server_emit_signal.connect(main_controller.change_server)

        self.tab_widget = TabListWidget(parent=self, window_parent=self.window_parent, list_Qwidget=[
            self.server_screen,
            self.camera_screen,
            self.device_screen,
            self.user_permissions_screen,
            self.setting_screen,
        ])
        self.stacked_widget.addWidget(self.tab_widget)
        # self.tab_widget.button_logout_signal.connect(
        #     main_controller.logout)
        self.tab_widget.button_change_server_signal.connect(
            main_controller.change_server)

        self.layout.addWidget(self.stacked_widget)
        self.set_dynamic_stylesheet()
        
        # if Config.SERVER_AVAILABLE:
        #     pass
        #     # get data from server
        #     self.get_data_to_ui()


    def get_data_to_ui(self):
        main_controller.get_update_data_from_server_by_thread()
        if Config.ENABLE_EVENT_BAR:
            pass
            # main_controller.get_setting_warning_and_alert()
            # main_controller.get_current_day_event_by_thread(
            #     parent=self, page_idx=1, page_size=20, warning=main_controller.is_filter_warning)
            # main_controller.get_yesterday_event_by_thread(
            #     parent=self, page_idx=1, page_size=20, warning=True)
            # main_controller.get_current_day_event_for_warning(
            #     parent=self, page_idx=1, page_size=30, warning=True)

    def create_controller(self):
        main_controller.update_list_tree_group_data = self.send_to_signal_of_update_list_tree_group_data
        main_controller.show_message_dialog = self.send_to_signal_of_message_dialog
        main_controller.update_list_setting_warning_alert = self.send_signal_get_warning_setting_from_server

    def add_group_list_signal(self,data):
        controller,group_list = data
        # group_list = group_model_manager.get_group_list()
        # tạm thời xử lý sau
        # self.device_screen.update_list_groups_data(
        #     group_list)

    def send_to_signal_of_message_dialog(self, response, message=None):
        data = (response, message)
        self.signal_message_dialog.emit(data)

    def send_to_signal_of_update_list_groups_data(self):
        self.signal_groups_data.emit()

    def send_to_signal_of_update_list_tree_group_data(self):
        self.signal_tree_group_data.emit()

    def send_signal_get_warning_setting_from_server(self):
        self.signal_warning_setting.emit()

    def message_dialog(self, data):
        response, message = data
        if response is not None and response.status_code == HTTPStatusCode.UNAUTHORIZED.value:
            main_controller.expired_token_goto_login_screen()
            return
        if message and response:
            if message == TypeRequestVMS.CREATE_CAMERA:
                Notifications(parent=self, title=self.tr('Add Camera Successfully Using RTSP'),
                              icon=Style.PrimaryImage.sucess_result)
            elif message == TypeRequestVMS.UPDATE_CAMERA:
                Notifications(parent=self, title=self.tr('Update Camera to Server Successfully'),
                              icon=Style.PrimaryImage.sucess_result)
            elif message == TypeRequestVMS.DELETE_CAMERA:
                Notifications(parent=self, title=self.tr("Successfully deleted Camera Group on Server"),
                              icon=Style.PrimaryImage.sucess_result)
            elif message == TypeRequestVMS.CREATE_CAMERA_GROUP:
                Notifications(parent=self, title=self.tr('Added Camera Group to Server Successfully'),
                              icon=Style.PrimaryImage.sucess_result)
            elif message == TypeRequestVMS.UPDATE_CAMERA_GROUP:
                Notifications(parent=self, title=self.tr('Update Camera Group to Server Successfully'),
                              icon=Style.PrimaryImage.sucess_result)
            elif message == TypeRequestVMS.DELETE_CAMERA_GROUP:
                Notifications(parent=self, title=self.tr("Successfully deleted Camera Group on Server"),
                              icon=Style.PrimaryImage.sucess_result)
            elif message == TypeRequestVMS.UPDATE_WARNING_ALERT_SETTING:
                Notifications(parent=self, title=self.tr('Update Successfully'),
                              icon=Style.PrimaryImage.sucess_result)
            else:
                error_messsage = LogErrorMessage(response=response)
                Notifications(parent=self, title=str(
                    error_messsage.type), icon=Style.PrimaryImage.fail_result)
        elif response is not None:
            error_messsage = LogErrorMessage(response=response)
            Notifications(parent=self, title=str(error_messsage.type),
                        icon=Style.PrimaryImage.fail_result)
            main_controller.progress_dialog.close()
        elif response is None and message is not None:
            if message == TypeRequestVMS.UPDATE_MAP_SUCCESS:
                Notifications(parent=self, title=self.tr("Successfully Updated Map"),
                              icon=Style.PrimaryImage.sucess_result)
            elif message == TypeRequestVMS.UPDATE_MAP_FAILED:
                Notifications(parent=self, title=self.tr("Update map fail"),
                              icon=Style.PrimaryImage.fail_result)
            elif message == "START_EDIT_MAP_CAMERA":
                Notifications(parent=self, title=self.tr("Start edit map"),
                              icon=Style.PrimaryImage.sucess_result)
            elif message == "END_EDIT_MAP_CAMERA":
                Notifications(parent=self, title=self.tr("End edit map"),
                              icon=Style.PrimaryImage.sucess_result)
            elif message == "NOT_EDIT_MAP_CAMERA":
                # Please Enable Edit Map
                Notifications(parent=self, title=self.tr("Please enable edit map"),
                                icon=Style.PrimaryImage.fail_result)
            elif message == "OPEN_CAMERA_SUCCESS":
                Notifications(parent=self, title=self.tr("Open camera success"),
                                icon=Style.PrimaryImage.sucess_result)
            elif message == "OPEN_CAMERA_FAILED":
                Notifications(parent=self, title=self.tr("Open camera failed"),
                                icon=Style.PrimaryImage.fail_result)
            elif message == "CAMERA_EXIST_IN_MAP":
                Notifications(parent=self, title=self.tr("Camera exist in map"),
                                icon=Style.PrimaryImage.fail_result)
            elif message == "SAVE_FLOOR":
                Notifications(parent=self, title=self.tr("Floor saved!"),
                                icon=Style.PrimaryImage.sucess_result)
            elif message == "CAMERA_ALREADY_HAVE_LOCATION":
                Notifications(parent=self, title=self.tr("Camera already have location"),
                                icon=Style.PrimaryImage.fail_result)
            else:
                Notifications(parent=self, title=str(message),
                          icon=Style.PrimaryImage.fail_result, time=3000)
        return
    
    def update_list_tree_group_data(self):
        self.camera_screen.update_list_tree_group_data(
            main_controller.list_tree_group, main_controller.list_camera_non_group)
        # self.playback_screen.update_list_tree_group_data(
        #     main_controller.list_tree_group, main_controller.list_camera_non_group, main_controller.list_cameras)
        # Sau khi TreeView được vẽ xong thì kiểm tra trạng thái active của camera luôn
        # if not self.init_health_check_camera:
        #     self.init_health_check_camera = True

    def update_list_warning_setting(self):
        self.setting_screen.widget_alert.list_setting_checked_from_server(list_warning_from_server=main_controller.list_warning_method,
                                                                          list_channel_from_server=main_controller.list_alert_channel)

    def on_window_resized(self, event):
        self.camera_screen.on_window_resized(event)

    def on_websocket_vms(self, event: dict):
        # logger.debug(f'on_websocket_vms: event: {event}')
        event_type = event.get('type', None)
        server_ip = event.get('server_ip', None)
        controller:Controller = controller_manager.get_controller(server_ip = server_ip)
        if event_type is not None:
            event_data = event.get('data', None)
            self.current_time = time.time()
            if event_type == 'discovery_camera':
                socket_id = event.get('socket_id')
                if event_data:
                    # logger.debug(f'on_websocket_test = {event}')
                    if event_data == "Done":
                        test = "Done"
                    else:
                        test = CameraData.fromDict(model_dict=event_data)

                else:
                    test = None
                main_controller.listen_ws_add_camera_signal.emit((test, socket_id))
            elif event_type == 'vms_camera_delete':
                camera_model = camera_model_manager.get_camera_model(id = event_data['id'])
                if camera_model is not None:
                    # camera_model_manager.delete_camera(camera_model=camera_model)
                    # camera_model_manager.delete_camera_model_signal.emit([camera_model])
                    # controller.get_groups()
                    controller.get_cameras()
                    controller.get_groups()

            elif event_type == 'vms_camera_update':
                pass
                # data = Camera.from_dict(event_data)
                # print(f'on_websocket_vms: data: {data}')
                # camera_model = controller.get_camera(ids=event_data['id'])
                # diff = []
                # if camera_model is not None:
                #     camera_model_origin:CameraModel = camera_model_manager.get_camera_model(id = camera_model.data.id)
                #     diff = camera_model_origin.diff_camera_model(camera = camera_model.data)
                #     if 'cameraGroupIds' in diff:
                #         controller.get_groups() 

            elif event_type == 'vms_camera_add':
                data = Camera.from_dict(event_data)
                camera_model = CameraModel(camera=data)
                camera_model.data.server_ip = server_ip
                camera_model_manager.add_camera(camera=camera_model)
                if camera_model.data.cameraGroupIds is not None and len(camera_model.data.cameraGroupIds) > 0:
                    # logger.debug(f'callback_create_camera {camera_model.data.cameraGroupIds}')
                    controller.get_groups()
            elif event_type == 'vms_camera_group_add':
                data = Group.from_dict(event_data)
                group_model = GroupModel(group=data)
                group_model.data.server_ip = server_ip
                group_model_manager.add_group(group=group_model)
                if group_model.data.cameraIds is not None:
                    if len(group_model.data.cameraIds) > 0:
                        controller.get_cameras()
            elif event_type == 'vms_camera_group_update':
                pass
                # data_group = Group.from_dict(event_data)
                # group_model: GroupModel = group_model_manager.get_group_model(id=data_group.id)
                # if group_model is not None:
                #     group_model.diff_group_model(group=data_group)
                # controller.get_cameras()
            elif event_type == 'vms_camera_group_delete':
                controller.get_groups()

            elif event_type == 'vms_aiflow_update':
                pass
                # logger.debug(f'event_type = {event_type,event_data["restreamPort"]}')
                # id = event_data.get('id',None)
                # restreamPort = event_data.get('restreamPort',None)
                # state = event_data.get('state',None)
                # restreamEndpoint = event_data.get('restreamEndpoint',None)
                # camera_id = event_data.get('cameraId',None)
                # new_ai_flow = AiFlow.from_dict(event_data)
                # aiflow_model: AiFlowModel = aiflow_model_manager.get_aiflow_model(id = id)
                # if aiflow_model is not None:
                #     diff = aiflow_model.diff_aiflow_model(aiflow=new_ai_flow)
                #     # logger.debug(f'diff diff = {diff}')
                #     # aiflow_model: AiFlowModel = aiflow_model_manager.get_aiflow_model(id = id)
                #     # aiflow_model.data = new_ai_flow
                # if restreamPort is not None and restreamEndpoint is not None and camera_id is not None and state == "PROCESSING":
                #     url_restream = "rtsp://"+controller.api_client.server_ip+':'+str(restreamPort)+restreamEndpoint
                #     camera_model = camera_model_manager.get_camera_model(id=camera_id)
                #     if camera_model is not None:
                #         camera_model.data.urlRestream = url_restream
                #         video_capture = camera_manager.get_video_capture(camera_id=camera_id,stream_type= StreamCameraType.ai_stream)
                #         if video_capture is not None:
                #             video_capture.stream_link = url_restream

            elif event_type == 'vms_tab_create' or event_type == 'vms_tab_update' or event_type == 'vms_tab_delete':
                pass
                # tab = Tab.from_dict(event_data)
                # tab.server_ip = server_ip
                # new_id = tab.direction['id']
                # tab_model = tab_model_manager.get_tab_model(tab_name=tab.name,tab_type=tab.type,server_ip=server_ip)
                # if event_type == 'vms_tab_create':
                #     pass
                #     if tab_model is not None:
                #         old_id = tab_model.data.direction.get('id',None)
                #         if new_id == old_id:
                #             # case client chính là client gửi request create_tabmodel
                #             # do rơi vào kịch bản người dùng save as 1 tab không định danh thành 1 tab virtual window lên chưa có tab id mà của sổ virtual 
                #             # được lưu với key dict  là name lên cần edit lại key dict này 
                #             if tab_model.data.name in main_controller.list_parent:
                #                 main_controller.list_parent[tab.id] = main_controller.list_parent.pop(tab.name) 
                #             tab_model.data.id = tab.id
                #             tab_model.id_changed.emit(tab_model.data.id)
                #             tab_model_manager.edit_key(tab_model=tab_model)
                #         else:
                #             # case client khong phai là client gửi request create_tabmodel
                #             new_tab_model = TabModel(tab = tab)
                #             new_tab_model.convert_data()
                #             self.camera_screen.main_treeview_widget.update_tabmodel(new_tabmodel=new_tab_model,old_tabmodel=tab_model)
                #             self.camera_screen.main_treeview_widget.add_tab_model(tab_model = tab_model,status=True,server_ip=server_ip)
                #     else:
                #         # case client khong phai là client gửi request create_tabmodel
                #         tab_model = TabModel(tab = tab)
                #         tab_model_manager.add_tab_model(tab_model=tab_model)
                #         tab_model.convert_data()
                #         self.camera_screen.main_treeview_widget.add_tab_model(tab_model = tab_model,server_ip=server_ip)
                # elif event_type == 'vms_tab_update':
                #     tab_model = tab_model_manager.get_tab_model(id=tab.id)
                #     if tab_model is not None:
                #         tab_model.update_tabmodel(tab.direction,tab)

                # elif event_type == 'vms_tab_delete':
                #     self.camera_screen.main_treeview_widget.remove_tab_model(tab_model = tab_model)
            elif event_type == 'vms_discovered_camera_add':
                logger.debug(f'data = {event_data}')

    def on_websocket_event(self, event: dict):
        logger.debug(f'on_websocket_event = {event}')
        # event_type = event['type']
        event_type = event.get('type',None)
        print(f'on_websocket_event: event_type: {event_type}')
        if event_type is not None:
            if self.camera_screen and Config.ENABLE_EVENT_BAR:
                try:
                    logger.debug(f'on_websocket_event1 = {event_type}')
                    # self.camera_screen.event_bar.list_button.current_index() == 0 -> là trạng thái Realtime
                    # self.camera_screen.event_bar.list_button.current_index() == 1 -> là trạng thái Warning
                    if (event_type == 'warning'):
                        event_data = event['data']
                        # data = event_data['event']['data']
                        # print(f'on_websocket_event: data: {data}')
                        event_ai = EventAI.from_dict(event_data)
                        event_ai.imageFullUrl = event_ai.imageUrl
                        event_ai.imageUrl = event_ai.imageCropUrl
                        logger.info(f'on_websocket_event = {event_ai.cameraId,event_ai.cameraName, event_ai.imageUrl,event_ai.imageFullUrl}')
                        event_manager.add_event(event_ai)
                except Exception as e:
                    pass
                    logger.info(f'on_websocket_event error: {e}')

    def retranslate_Ui(self):
        self.server_screen.retranslate_server_screen()
        self.camera_screen.retranslate_camera_screen()
        self.device_screen.retranslate_device_screen()
        self.setting_screen.retranslateUiSettingScreen()
        self.user_permissions_screen.re_translate_user_screen()
        # self.playback_screen.retranslateUi()
        # self.map_screen.retranslateUi_map()≥
        # for index in range(self.tab_widget.count()):
        # self.tab_widget.widget(2).retranslateUi()

    def restyle_Ui(self):
        self.server_screen.restyle_ui_server_screen()
        self.camera_screen.restyle_camera_screen()
        self.device_screen.restyle_device_screen()
        self.setting_screen.restyle_ui_setting_screen()
        self.user_permissions_screen.restyle_user_screen()
        self.set_dynamic_stylesheet()

    def change_server(self):
        # clear data Qsetting and set go to server form
        main_controller.clear_all()
        auth_settings = AuthQSettings.get_instance()
        auth_settings.clear_all()
        main_controller.api_client.change_server()
        Camera_Qsettings.get_instance().clear_all()
        
    def close_websocket(self):
        if Config.ENABLE_WEBSOCKET_EVENT:
            if self.ws:
                self.ws.stop()
                self.ws = None
        if Config.ENABLE_WEBSOCKET_VMS:
            if self.ws_vms:
                self.ws_vms.stop()
                self.ws_vms = None

    def stop_app(self):
        for controller in controller_manager.data.values():
            if controller.websocket_vms is not None:
                controller.websocket_vms.messageProcessor.message_queue.put(None)
                controller.websocket_vms.close()
                controller.websocket_vms = None
            if controller.websocket_event is not None:
                controller.websocket_event.messageProcessor.message_queue.put(None)
                controller.websocket_event.close()
                controller.websocket_event = None

    def window_state_changed(self, state):
        self.camera_screen.widget_button_system.window_state_changed(state)
        self.device_screen.title_bar.widget_button_system.window_state_changed(state)
        self.server_screen.title_bar.window_state_changed(state)
        self.setting_screen.title_bar.widget_button_system.window_state_changed(state)
        self.user_permissions_screen.title_bar.widget_button_system.window_state_changed(state)

    def set_dynamic_stylesheet(self):
        self.stacked_widget.setStyleSheet(
            f'''
                QStackedWidget {{
                    background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                    color: {Style.PrimaryColor.white};
                }}
            '''
        )
        
        self.tab_widget.set_dynamic_stylesheet()
        
class LoadingDataDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumWidth(300)
        self.setMinimumHeight(100)
        self.parent1 = parent
        self.setWindowFlag(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setObjectName("LoadingDataDialog")
        main_layout = QVBoxLayout(self)
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        widget.setObjectName('main_widget')
        widget.setStyleSheet(
            f'''
                QWidget#main_widget {{
                    background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                    color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                    border-radius: 4px;
                    border: 1px solid {Style.PrimaryColor.primary};
                    padding: 8px;
                }}
                QLabel{{
                    color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                }}
                QProgressBar {{
                    border: 2px solid {Style.PrimaryColor.on_hover};
                    border-radius: 5px;
                    background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                    text-align: center;
                    font-weight: bold;
                }}
                QProgressBar::chunk {{
                    background: {Style.PrimaryColor.primary};
                    border-radius: 5px;
                }}
            ''')

        # Create a QProgressBar
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(10)
        self.progress_bar.setMaximum(0)

        self.label_is_loading = QLabel(self.tr('Connecting...'))

        layout.addWidget(self.label_is_loading)
        layout.addWidget(self.progress_bar)

        # Timer for the animation
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_loading)

        main_layout.addWidget(widget)
        # self.setLayout(layout)

    def start_loading(self):
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(0)  # Set to 0 for an indeterminate state (loading)
        self.timer.start(2000)  # Start the timer

    def stop_loading(self):
        self.timer.stop()
        self.progress_bar.setVisible(False)

    def update_loading(self):
        # No need to update the progress bar value as it is indeterminate
        pass

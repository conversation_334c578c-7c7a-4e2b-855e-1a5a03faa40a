import pickle
from PySide6.QtGui import QDrag, QResizeEvent
from PySide6.QtCore import Qt, QUrl, QSize,Slot
from PySide6.QtQuickWidgets import QQuickWidget
from PySide6.QtQml import qmlRegisterType
from src.common.model.tab_model import TabModel
from src.common.model.camera_model import camera_model_manager
from src.presentation.camera_screen.stream_object_base_widget import StreamObjectBaseWidget
from src.common.qml.models.map_controller import MapState,MapModel
from src.common.qml.models.video_model import VideoModel
from src.common.model.camera_model import CameraModel
from src.common.widget.notifications.notify import Notifications
from src.common.controller.main_controller import main_controller
import json
from src.styles.style import Style
import logging
logger = logging.getLogger(__name__)
class MapGridWidgetItem(StreamObjectBaseWidget):
    def __init__(self, parent=None,map_model:MapModel = None, stack_item=None, tab_model: TabModel = None, row=0, col=0, width=1, height=1, is_editable=False):
        super().__init__(parent, stack_item=stack_item, tab_model = tab_model)
        self.setFocusPolicy(Qt.StrongFocus)
        self.setMouseTracking(True)
        self.setAcceptDrops(True)
        self.map_model = map_model
        self.stack_item = stack_item
        self.tab_model = tab_model
        self.is_editable = is_editable
        self.row = row
        self.col = col
        self.name = "MapOSM"
        self.root_width = width
        self.root_height = height
        # self.camera_list = CameraDroppedItemModel(server_ip=server_ip)
        # get maincontroller
        self.width = width
        self.height = height
        self.setup_ui()

    def setup_ui(self):
        qmlRegisterType(CameraModel, "CustomComponents", 1, 0, "CameraModel")
        qmlRegisterType(MapState, "CustomComponents", 1, 0, "MapState")
        qmlRegisterType(VideoModel, "CustomComponents", 1, 0, "VideoModel")
        self.mapState = MapState()
        self.mapState.editMode = self.is_editable
        self.mapState.viewModeChanged.connect(self.viewModeChanged)
        self.mapState.editModeChanged.connect(self.editModeChanged)
        self.mapState.notifyChanged.connect(self.notifyChanged)
        self.map_view = QQuickWidget(self)
        if self.mapState.editMode:
            self.clone_map_model = self.map_model.clone()
            self.clone_map_model.notifyChanged.connect(self.notifyChanged)
        else:
            self.mapState.dropEventChanged.connect(self.dropEventChanged)
        # logger.info(f'MapGridWidgetItem1 = {self.clone_map_model}')
        self.map_view.rootContext().setContextProperty("mapModel", self.clone_map_model if self.mapState.editMode else self.map_model)
        self.map_view.rootContext().setContextProperty("mapState", self.mapState)
        self.map_view.rootContext().setContextProperty("widget", self)
        self.map_view.setSource(QUrl("qrc:/src/common/qml/map/MapOnGrid.qml"))
        self.map_view.setResizeMode(QQuickWidget.SizeRootObjectToView)
        self.map_view.setGeometry(0, 0, self.width, self.height)
        self.map_view.setAcceptDrops(True)

        if not self.mapState.editMode:
            self.header_top_widget = self.create_header_top_widget_base_for_map()
            self.btn_lock_map.icon_clicked = self.btn_lock_map_clicked
            self.header_top_widget.setVisible(False)
            self.header_top_widget.setGeometry(0, 0, self.width, self.HEIGHT_HEADER)

    def btn_lock_map_clicked(self):
        self.mapState.lockMode = not self.mapState.lockMode
        if self.mapState.lockMode:
            self.btn_lock_map.button.setStyleSheet(Style.StyleSheet.button_style19)
        else:
            self.btn_lock_map.button.setStyleSheet("background-color: red;")

    def dropEventChanged(self, data):
        if(data.isEmpty()):
            self.mapState.notifyChanged.emit(MapState.notifyKey.SelectEditMode)
            return
        try:
            data = pickle.loads(data.data())
        except:
            data = bytes(data.data()).decode('utf-8')
            data = json.loads(data)

        
        print(data)
        self.stack_item.swap_item(data)

    def viewModeChanged(self):
        if not self.mapState.editMode:
            if self.mapState.viewMode == self.mapState.ViewMode.FULLSCREEN:
                self.header_top_widget.setVisible(False)
                self.header_top_widget.setGeometry(0, 0, 0, 0)
            else:
                self.header_top_widget.setVisible(True)
                self.header_top_widget.setGeometry(0, 0, self.root_width, self.HEIGHT_HEADER)
            
    def editModeChanged(self):
        logger.debug(f'editModeChanged = {self.mapState.viewMode,self.mapState.editMode,self.mapState.EditMode.EDITABLE}')

    def notifyChanged(self,notifyKey):
        if notifyKey == MapState.notifyKey.SelectGridType:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('Please enlarge the grid cell or switch to a grid size smaller than 3x3.'), icon=Style.PrimaryImage.info_result)
        elif notifyKey == MapState.notifyKey.SelectEditMode:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('To drag an item onto the Map, you need to enter edit mode.'), icon=Style.PrimaryImage.info_result)
        elif notifyKey == MapState.notifyKey.MapSavedSuccessfully:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('Map saved successfully.'), icon=Style.PrimaryImage.sucess_result)
        elif notifyKey == MapState.notifyKey.MapSaveFailed:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('Failed to save map.'), icon=Style.PrimaryImage.info_result)
        elif notifyKey == MapState.notifyKey.LocationAlert:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('This camera is already assigned to another position on the map.'), icon=Style.PrimaryImage.info_result)
    @Slot(result='QVariant')
    def getPosition(self):
        row = self.stack_item.position.x()
        col = self.stack_item.position.y()   
        logger.info(f"getPosition {row,col,self.stack_item}")
        return [row,col]
    
    def update_resize(self, width, height):
        # logger.debug(f'update_resize')
        self.on_resize_change(QResizeEvent(QSize(width, height), QSize(self.width, self.height)))

    def update_map_model(self):
        self.map_model.merge(self.clone_map_model)
        self.mapState.notifyChanged.emit(MapState.notifyKey.MapSavedSuccessfully)
        # try:
        #     self.map_model.merge(self.clone_map_model)
        #     self.mapState.notifyChanged.emit(MapState.notifyKey.MapSavedSuccessfully)
        # except Exception as e:
        #     self.mapState.notifyChanged.emit(MapState.notifyKey.MapSaveFailed)

    def on_resize_change(self, event):
        # set scene rect
        new_size = event.size()
        self.width = new_size.width()
        self.height = new_size.height()
        self.root_width = new_size.width()
        self.root_height = new_size.height()
        self.map_view.setGeometry(0, 0, self.width, self.height)
        if self.header_top_widget is not None and not self.mapState.editMode:
            self.header_top_widget.setGeometry(0, 0, new_size.width(), self.HEIGHT_HEADER)

    # def mousePressEvent(self, event):
    #     if not self.is_show_header:
    #         return
    #     if event.button() == Qt.LeftButton:
    #         if self.stack_item is None:
    #             return
    #         if not self.is_3d_locate and not self.is_crop_frame and not self.is_ptz_dialog:
    #             self.grid_item_clicked(main_controller.current_tab)
    #         elif self.is_crop_frame:
    #             self.start_pos = event.pos()

    #         row = self.stack_item.position.x()
    #         col = self.stack_item.position.y()
    #         item_grid = self.childAt(event.position().toPoint())
    #         position = (row, col)
    #         data_bytes = pickle.dumps(position)
    #         if isinstance(item_grid, QLabel):
    #             # Create and store the drag object
    #             self.drag = QDrag(self)
    #             mime_data = QMimeData()
    #             mime_data.setObjectName("swap_item")
    #             mime_data.setText(self.camera_name)
    #             mime_data.setData("application/position", data_bytes)
    #             self.drag.setMimeData(mime_data)
    #             # Execute the drag operation
    #     elif event.button() == Qt.RightButton:
    #         if not self.is_virtual_window:
    #             self.grid_item_clicked(main_controller.current_tab)
    #             mouse_position = QCursor.pos()
    #             self.showMenu(mouse_position)

    # def mouseMoveEvent(self, event):
    #     if self.drag is not None:
    #         # Execute the drag operation
    #         self.drag.exec()

    # def mouseReleaseEvent(self, event):
    #     pass

    # def dragEnterEvent(self, event):
    #     event.acceptProposedAction()
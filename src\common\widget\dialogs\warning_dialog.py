from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel, QVBoxLayout
from src.utils.config import Config
from src.common.widget.dialogs.base_dialog import NewBaseDialog,FooterType
from src.styles.style import Style
from PySide6.QtGui import QPixmap
from PySide6.QtCore import Qt
import logging

logger = logging.getLogger(__name__)

class WarningDialog(NewBaseDialog):
    def __init__(self, parent=None, warn='', icon=None):
        # Create main vertical layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        
        # Create horizontal layout for icon and text
        content_layout = QHBoxLayout()
        content_layout.setSpacing(10)
        content_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        self.icon = QLabel()
        if icon is None:
            icon = Style.PrimaryImage.info_result
        self.icon.setPixmap(QPixmap(icon))
        
        label = QLabel(warn)
        
        # Only wrap text if it's longer than 80 characters
        if len(warn) > 80:
            label.setWordWrap(True)
        else:
            label.setWordWrap(False)
            
        label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        
        content_layout.addWidget(self.icon)
        content_layout.addWidget(label)
        
        widget_main = QWidget()
        widget_main.setLayout(content_layout)
        
        # Calculate width based on text length
        if len(warn) > 80:
            # For long text, use maximum width
            dialog_width = Config.WIDTH_DIALOG_SHORTCUT
        else:
            # For short text, calculate minimum width needed
            # Approximate 8 pixels per character plus padding
            dialog_width = min(len(warn) * 8 + 100, Config.WIDTH_DIALOG_SHORTCUT)
        
        super().__init__(parent, title=self.tr("Notification"), content_widget=widget_main,
                         width_dialog=dialog_width, footer_type=FooterType.OK_CANCEL)
        self.setObjectName("WarningDialog")
        self.save_update_signal.connect(self.accept)

    def cancel_clicked(self):
        self.close()
        
from PySide6.QtGui import Qt, QPixmap, QStandardItem, QIcon,QGuiApplication,QStandardItemModel,QFont
from PySide6.QtCore import Signal
from PySide6.QtWidgets import <PERSON><PERSON>abel, <PERSON>Widget, QHBoxLayout, QGroupBox, QVBoxLayout, QDialog, QPushButton

from src.common.model.user_model import user_model_manager
from src.common.model.user_role_model import role_model_manager
from src.common.widget.tree_view_widget import TreeViewType, ViewTypePicker
from typing import List
from src.common.model.group_model import Group,GroupModel,group_model_manager
from src.common.model.camera_model import Camera,CameraModel,camera_model_manager
from src.common.model.group_tree_model import GroupTreeModel
from src.styles.style import Style
from src.common.controller.main_controller import main_controller
from src.common.widget.tree_view_widget import TreeViewWidget, TreeViewType
from src.common.widget.search_widget.search_bar import SearchBar
from src.common.widget.custom_tree_view import TreeViewBase
from src.utils.theme_setting import theme_setting
from unidecode import unidecode
import re
import logging
logger = logging.getLogger(__name__)
class CameraGroupType:
    NONE = 0
    PARENT_GROUP = 1
    SUB_GROUP = 2
    CAMERA_GROUP = 3
    FILTER_GROUP = 4
    FILTER_SUB_GROUP = 5


class MultiDropDown(QWidget):
    multidropdown_dialog_result_signal = Signal(list)
    signal_change_data = Signal(tuple)

    def __init__(self, parent=None,model = None, icon_bottom=None, list_tree_group: List[GroupTreeModel] = [],
                 list_group: List[GroupModel] = [], list_camera: List[CameraModel] = [], enable_groupbox=True,
                 tree_view_type=TreeViewType.GROUP, group_type=CameraGroupType.NONE, enable_only_item_checkbox=False,
                 enable_checkbox=True, text='', enable_multi_item_checkbox=True,title = ''):
        super().__init__(parent)
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()
        self.model = model
        self.title = title
        self.enable_multi_item_checkbox = enable_multi_item_checkbox
        self.enable_checkbox = enable_checkbox
        self.enable_only_item_checkbox = enable_only_item_checkbox
        self.new_list_subgroup_after_select_parentgroup = None
        self.new_list_parentgroup_after_select_subgroup = None
        self.list_group = list_group
        self.list_camera = list_camera
        self.list_tree_group = list_tree_group
        self.tree_view_type = tree_view_type
        self.group_type = group_type
        self.text = text
        self.enable_groupbox = enable_groupbox
        self.update_filter = None
        if self.enable_groupbox:
            self.setup_ui_groupbox()
        else:
            self.setup_ui_icon()

    def setup_ui_groupbox(self):
        # if self.tree_view_type == TreeViewType.CAMERA:
        #     self.groupbox_camera()
        # else:
        self.groupbox_group()

    def groupbox_camera(self):
        group_box = QGroupBox()
        # self.group_box.setStyleSheet("""QGroupBox {
        #                                     font: bold;border: 1px solid silver;border-radius: 6px;margin-top: 6px;background-color: #FFFFFF;color: #5C687F; font-size: 12px;
        #                                     }
        #                                 QGroupBox::title {
        #                                     subcontrol-origin: margin;left: 7px;padding: 0px 5px 0px 5px;color: #5C687F;
        #                                     }
        #                                 QLabel {
        #                                     background-color: #FFFFFF;}
        #                                 """)
        group_box.setStyleSheet("""
            QGroupBox {
                font: bold;
                border: 1px solid silver;
                border-radius: 6px;
                margin-top: 6px;
                background-color: #FFFFFF;
                color: #000000;
                font-size: 12px;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 7px;
                padding: 0px 5px 0px 5px;
                color: #000000;
            }""")
        self.icon_dropdown = IconDropDown()
        self.icon_dropdown.clicked = self.icon_dropdown_clicked
        self.camerabox1 = CameraBox()
        self.camerabox1.setFixedHeight(50)
        self.camerabox2 = CameraBox()
        self.camerabox2.setFixedHeight(50)
        self.hbox = QHBoxLayout()
        self.widget = QWidget()
        self.hbox1 = QHBoxLayout()
        self.hbox1.setContentsMargins(0, 0, 0, 0)
        # self.hbox1.addWidget(self.camerabox1)
        # self.hbox1.addWidget(self.camerabox2)
        self.widget.setLayout(self.hbox1)
        self.hbox.addWidget(self.widget)
        self.hbox.addWidget(self.icon_dropdown)

        vbox = QVBoxLayout()
        vbox.addLayout(self.hbox)

        # Đặt QVBoxLayout cho QGroupBox
        group_box.setLayout(vbox)
        main = QVBoxLayout()
        main.setContentsMargins(0, 0, 0, 0)
        main.addWidget(group_box)
        self.setLayout(main)

    def groupbox_group(self):
        self.group_box = QGroupBox(self.title)
        #self.group_box.setObjectName('group_box')
        # self.group_box.setStyleSheet("QGroupBox {font: bold;border: 1px solid silver;border-radius: 6px;margin-top: 6px;background-color: #FFFFFF;color: #5C687F; font-size: 14px;}"
        #                              "QGroupBox::title {subcontrol-origin: margin;left: 7px;padding: 0px 5px 0px 5px;color: #5C687F;}"
        #                              "QLabel {background-color: #FFFFFF;}")
        self.group_box.setStyleSheet("""
            QGroupBox {
                font: bold;
                border: 1px solid silver;
                border-radius: 6px;
                margin-top: 6px;
                background-color: #FFFFFF;
                color: #000000;
                font-size: 12px;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 7px;
                padding: 0px 5px 0px 5px;
                color: #000000;
            }""")
        self.vbox_layout = QVBoxLayout()
        self.list_string = QLabel()
        self.list_string.setAlignment(Qt.AlignLeft)
        self.list_string.setStyleSheet("font-size:14px;")
        if self.text != '':
            self.list_string.setText(self.text)
        self.list_id = []
        if self.model is not None:
            if self.tree_view_type==TreeViewType.GROUP:
                if self.model.data.cameraGroupIds is not None and len(self.model.data.cameraGroupIds) > 0:
                    for id in self.model.data.cameraGroupIds:
                        self.list_id.append(id)
            else:
                if self.model.data.cameraIds is not None and len(self.model.data.cameraIds) > 0:
                    for id in self.model.data.cameraIds:
                        self.list_id.append(id)
        self.icon_dropdown = IconDropDown()
        self.icon_dropdown.clicked = self.icon_dropdown_clicked
        self.hbox_layout = QHBoxLayout()

        self.hbox_layout.addWidget(self.list_string)
        self.hbox_layout.addWidget(self.icon_dropdown)
        self.vbox_layout.addLayout(self.hbox_layout)
        self.group_box.setLayout(self.vbox_layout)

        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.addWidget(self.group_box)

        self.setLayout(self.main_layout)

    def setup_ui_icon(self):
        self.hbox_layout = QHBoxLayout()
        self.setLayout(self.hbox_layout)
        self.icon_dropdown = IconDropDown()
        self.icon_dropdown.clicked = self.icon_dropdown_clicked
        self.hbox_layout.addWidget(self.icon_dropdown)

    def name_item_selected(self, data):
        list_string = ''
        self.list_id = []
        list_name = []
        for id, name in data['data'].items():
            list_name.append(name)
            self.list_id.append(id)
            if list_string == '':
                list_string = name
            else:
                list_string = f'{list_string}, {name}'
        data_emit = (list_name, self.list_id)
        self.signal_change_data.emit(data_emit)
        self.list_string.setText(list_string)

    def icon_dropdown_clicked(self, event):
        if event.button() == Qt.LeftButton:
            # Lấy vị trí của con trỏ chuột và hiển thị MultiDropdown tại đó
            pos = self.icon_dropdown.mapToGlobal(event.pos())
            pos.setX(pos.x() + 5)
            
            self.multidropdown_dialog = MultiDropDownDialog(model = self.model,tree_view_type=self.tree_view_type)
            self.multidropdown_dialog.item_selected_signal.connect(self.name_item_selected)
            if (self.desktop_screen_size.height() - pos.y()) < self.multidropdown_dialog.height():
                # logger.debug(f'pos.y() = {pos.y()}')
                pos.setY(self.desktop_screen_size.height() - self.multidropdown_dialog.height())
            self.multidropdown_dialog.move(pos)
            self.multidropdown_dialog.exec()

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            # Lấy vị trí của con trỏ chuột và hiển thị MultiDropdown tại đó
            pos = self.mapToGlobal(event.pos())
            pos.setX(pos.x() + 5)
            self.multidropdown_dialog = MultiDropDownDialog(model=self.model, tree_view_type=self.tree_view_type)
            self.multidropdown_dialog.item_selected_signal.connect(self.name_item_selected)
            if (self.desktop_screen_size.height() - pos.y()) < self.multidropdown_dialog.height():
                pos.setY(self.desktop_screen_size.height() - self.multidropdown_dialog.height())
            self.multidropdown_dialog.move(pos)
            self.multidropdown_dialog.exec()

    def set_text_data(self, list_text):
        list_string = ''
        for name in list_text:
            if list_string == '':
                list_string = name
            else:
                list_string = f'{list_string}, {name}'
        self.list_string.setText(list_string)

class IconDropDown(QLabel):
    def __init__(self, parent=None, icon_bottom=None,size = None):
        super().__init__(parent)
        self.parent = parent
        self.clicked = None
        self.icon_bottom = Style.PrimaryImage.expand_bottom if icon_bottom == None else icon_bottom
        self.pixmap_bottom = QPixmap(self.icon_bottom)
        #logger.debug(f'ggggggggg = {self.pixmap_bottom.width()} {self.pixmap_bottom.height()}')
        self.setPixmap(self.pixmap_bottom)
        self.setFixedSize(self.pixmap_bottom.width(),
                          self.pixmap_bottom.height())
        # if size != None:
        #     self.setFixedSize(size,size)
        # else:
        #     self.setFixedSize(self.pixmap_bottom.width(),
        #                   self.pixmap_bottom.height())
        self.setStyleSheet(
            f'''
                QLabel {{
                    background-color: transparent;
                    
                }}
                QLabel:hover {{
                    background-color: rgba(29, 90, 223, 0.1);
                }}
                '''
        )

    def on_camera_triggered(self):
        # logger.debug("Camera action triggered")
        pass

    def on_group_camera_triggered(self):
        # logger.debug("Group Camera action triggered")
        pass

    def mousePressEvent(self, event):
        if self.clicked != None:
            self.clicked(event)


class CameraBox(QWidget):
    def __init__(self, parent=None, title=''):
        super().__init__(parent)
        self.setFixedWidth(100)
        self.group_box = QGroupBox()
        self.vbox_layout = QVBoxLayout()
        self.vbox_layout.setContentsMargins(0, 0, 0, 0)
        self.list_string = QLabel(title)
        self.list_string.setAlignment(Qt.AlignLeft)
        self.icon_dropdown = IconDropDown(
            icon_bottom='src/assets/images/delete.png')
        self.icon_dropdown.clicked = self.icon_dropdown_clicked
        self.hbox_layout = QHBoxLayout()
        # self.hbox_layout.setContentsMargins(0,0,0,0)
        self.hbox_layout.addWidget(self.list_string)
        self.hbox_layout.addWidget(self.icon_dropdown)
        self.hbox_layout.setStretch(0, 8)
        self.hbox_layout.setStretch(1, 2)
        self.vbox_layout.addLayout(self.hbox_layout)
        self.group_box.setLayout(self.vbox_layout)
        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.addWidget(self.group_box)

        self.setLayout(self.main_layout)

    def icon_dropdown_clicked(self, event):
        if event.button() == Qt.LeftButton:
            self.close()

class MultiDropDownDialog(QDialog):
    item_selected_signal = Signal(dict)

    def __init__(self, parent=None, controller=None, model=None, tree_view_type=TreeViewType.GROUP, list_id_checked=None):
        super().__init__(parent)
        # self.setAttribute(Qt.WA_TransparentForMouseEvents)
        self.controller = controller
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.model = model
        self.list_id_checked = list_id_checked
        self.tree_view_type = tree_view_type
        self.search_widget = SearchBar(parent=self)
        self.search_widget.search_items_signal.connect(self.search_items)
        self.treeview_widget = TreeViewBase()
        self.treeview_widget.tree_view.setHeaderHidden(False)
        self.treeview_widget.on_tree_view_clicked = self.on_tree_view_clicked
        self.treeview_widget.on_tree_view_pressed = self.on_tree_view_pressed
        self.btn_ok = QPushButton(self.tr("Close"))
        self.btn_ok.setFixedWidth(100)
        self.btn_ok.setStyleSheet(Style.StyleSheet.button_positive)
        self.btn_ok.clicked.connect(self.ok_clicked)
        self.hbox = QHBoxLayout()
        self.hbox.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.hbox.addWidget(self.btn_ok)
        self.widget_dialog = QWidget()
        self.widget_dialog.setObjectName("widget_dialog")
        
        self.dialog_layout = QVBoxLayout()
        self.dialog_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.dialog_layout.addWidget(self.search_widget)
        self.dialog_layout.addWidget(self.treeview_widget)
        self.dialog_layout.addLayout(self.hbox)    
        self.widget_dialog.setLayout(self.dialog_layout)    

        self.main_layout = QVBoxLayout()
        self.main_layout.addWidget(self.widget_dialog)
        self.setLayout(self.main_layout)
        self.item_selected = {'type':None,'data':{}}
        self.set_dynamic_stylesheet()
        self.create_tree_view()

    def on_tree_view_pressed(self, index):
        # logger.debug(f'on_tree_view_pressed {self.item_selected["data"]}')

        item = self.treeview_widget.model.itemFromIndex(index)
        if item.checkState() == Qt.CheckState.Checked:
            item.setCheckState(Qt.CheckState.Unchecked)
            for idx, name in self.item_selected['data'].items():
                if item.text() == name:
                    del self.item_selected['data'][idx]
                    break
        else:
            item.setCheckState(Qt.CheckState.Checked)
            item_data = item.data(Qt.UserRole)
            self.item_selected['data'][item_data] = item.text()

    def on_tree_view_clicked(self, index):
        pass
        # logger.debug(f'on_tree_view_clicked')
        # item = self.treeview_widget.model.itemFromIndex(index)
        # if item.checkState() == Qt.CheckState.Checked:
        #     item_data = item.data(Qt.UserRole)
        #     self.item_selected['data'][item_data] = item.text()
            
        # else:
        #     for id, name in self.item_selected['data'].items():
        #         if item.text() == name:
        #             del self.item_selected['data'][id]
        #             break
        # self.item_selected_signal.emit(self.item_selected)

    def create_search_tree_view(self):
        # return if items_location and items_camera is empty
        model = QStandardItemModel()
        root_tree_view = model.invisibleRootItem()

        if self.tree_view_type == TreeViewType.GROUP:
            model.setHorizontalHeaderLabels('Group List')
            self.item_selected['type'] = TreeViewType.GROUP
            group_list = group_model_manager.get_group_list(server_ip=main_controller.current_controller.server.data.server_ip)
            camera_model: CameraModel = self.model
            for group_model in group_list.values():
                root_item = QStandardItem(group_model.data.name)
                font = QFont()
                font.setPixelSize(Style.Size.body)
                root_item.setFont(font)
                root_item.setEditable(False)
                root_item.setCheckable(True)
                root_item.setData(group_model.data.id, Qt.UserRole)
                if group_model.data.id in self.item_selected['data']:
                    root_item.setCheckState(Qt.Checked)
                root_tree_view.appendRow(root_item)

        elif self.tree_view_type == TreeViewType.CAMERA:
            model.setHorizontalHeaderLabels('Camera List')
            self.item_selected['type'] = TreeViewType.CAMERA
            camera_list = camera_model_manager.get_camera_list(server_ip=main_controller.current_controller.server.data.server_ip)
            group_model: GroupModel = self.model
            for camera_model in camera_list.values():
                root_item = QStandardItem(camera_model.data.name)
                font = QFont()
                font.setPixelSize(Style.Size.body)
                root_item.setFont(font)
                root_item.setEditable(False)
                root_item.setCheckable(True)
                root_item.setData(camera_model.data.id, Qt.UserRole)
                if camera_model.data.id in self.item_selected['data']:
                    root_item.setCheckState(Qt.Checked)
                root_tree_view.appendRow(root_item)

        elif self.tree_view_type == TreeViewType.USER_ROLE:
            model.setHorizontalHeaderLabels('User Role List')
            self.item_selected['type'] = TreeViewType.USER_ROLE
            role_list = role_model_manager.get_role_list(server_ip=main_controller.current_controller.server.data.server_ip)
            for role_model in role_list.values():
                root_item = QStandardItem(role_model.data.name)
                font = QFont()
                font.setPixelSize(Style.Size.body)
                root_item.setFont(font)
                root_item.setEditable(False)
                root_item.setCheckable(True)
                root_item.setData(role_model.data.id, Qt.UserRole)
                if role_model.data.id in self.item_selected['data']:
                    root_item.setCheckState(Qt.Checked)
                root_tree_view.appendRow(root_item)

        elif self.tree_view_type == TreeViewType.USER:
            model.setHorizontalHeaderLabels('User List')
            self.item_selected['type'] = TreeViewType.USER
            user_list = user_model_manager.get_user_list(
                server_ip=main_controller.current_controller.server.data.server_ip)
            for user_model in user_list.values():
                root_item = QStandardItem(user_model.data.fullName)
                font = QFont()
                font.setPixelSize(Style.Size.body)
                root_item.setFont(font)
                root_item.setEditable(False)
                root_item.setCheckable(True)
                root_item.setData(user_model.data.id, Qt.UserRole)
                if user_model.data.id in self.item_selected['data']:
                    root_item.setCheckState(Qt.Checked)
                root_tree_view.appendRow(root_item)

        model.itemChanged.connect(lambda item: self.handle_item_changed(item))
        self.treeview_widget.set_model(model = model)
        self.treeview_widget.tree_view.expandAll()
        self.treeview_widget.tree_view.clearFocus()

    def create_tree_view(self):
        # return if items_location and items_camera is empty
        model = QStandardItemModel()
        root_tree_view = model.invisibleRootItem()

        if self.tree_view_type == TreeViewType.GROUP:
            model.setHorizontalHeaderLabels('Group List')
            self.item_selected['type'] = TreeViewType.GROUP
            group_list = group_model_manager.get_group_list(server_ip=main_controller.current_controller.server.data.server_ip)
            camera_model: CameraModel = self.model
            for group_model in group_list.values():
                root_item = QStandardItem(group_model.data.name)
                font = QFont()
                font.setPixelSize(Style.Size.body)
                root_item.setFont(font)
                root_item.setEditable(False)
                root_item.setCheckable(True)
                root_item.setData(group_model.data.id, Qt.UserRole)
                if self.list_id_checked is None or len(self.list_id_checked) == 0:
                    if camera_model is not None:
                        if group_model.data.id in camera_model.data.cameraGroupIds:
                            root_item.setCheckState(Qt.Checked)
                            self.item_selected['data'][group_model.data.id] = group_model.data.name
                else:
                    if group_model.data.id in self.list_id_checked:
                        root_item.setCheckState(Qt.Checked)
                        self.item_selected['data'][group_model.data.id] = group_model.data.name
                root_tree_view.appendRow(root_item)

        elif self.tree_view_type == TreeViewType.CAMERA:
            model.setHorizontalHeaderLabels('Camera List')
            self.item_selected['type'] = TreeViewType.CAMERA
            camera_list = camera_model_manager.get_camera_list(server_ip=main_controller.current_controller.server.data.server_ip)
            group_model: GroupModel = self.model
            for camera_model in camera_list.values():
                root_item = QStandardItem(camera_model.data.name)
                font = QFont()
                font.setPixelSize(Style.Size.body)
                root_item.setFont(font)
                root_item.setEditable(False)
                root_item.setCheckable(True)
                root_item.setData(camera_model.data.id, Qt.UserRole)
                if self.list_id_checked is None or len(self.list_id_checked) == 0:
                    if group_model is not None:
                        if camera_model.data.id in group_model.data.cameraIds:
                            root_item.setCheckState(Qt.Checked)
                            self.item_selected['data'][camera_model.data.id] = camera_model.data.name
                else:
                    if camera_model.data.id in self.list_id_checked:
                        root_item.setCheckState(Qt.Checked)
                        self.item_selected['data'][camera_model.data.id] = camera_model.data.name
                root_tree_view.appendRow(root_item)

        elif self.tree_view_type == TreeViewType.USER_ROLE:
            model.setHorizontalHeaderLabels('Role List')
            self.item_selected['type'] = TreeViewType.USER_ROLE
            role_list = role_model_manager.get_role_list(server_ip=main_controller.current_controller.server.data.server_ip)
            for role_model in role_list.values():
                root_item = QStandardItem(role_model.data.name)
                font = QFont()
                font.setPixelSize(Style.Size.body)
                root_item.setFont(font)
                root_item.setEditable(False)
                root_item.setCheckable(True)
                root_item.setData(role_model.data.id, Qt.UserRole)
                if self.list_id_checked is None or len(self.list_id_checked) == 0:
                    pass
                else:
                    if role_model.data.id in self.list_id_checked:
                        root_item.setCheckState(Qt.Checked)
                        self.item_selected['data'][role_model.data.id] = role_model.data.name
                root_tree_view.appendRow(root_item)

        elif self.tree_view_type == TreeViewType.USER:
            model.setHorizontalHeaderLabels('Role List')
            self.item_selected['type'] = TreeViewType.USER
            user_list = user_model_manager.get_user_list(server_ip=main_controller.current_controller.server.data.server_ip)
            for user_model in user_list.values():
                root_item = QStandardItem(user_model.data.fullName)
                font = QFont()
                font.setPixelSize(Style.Size.body)
                root_item.setFont(font)
                root_item.setEditable(False)
                root_item.setCheckable(True)
                root_item.setData(user_model.data.id, Qt.UserRole)
                if self.list_id_checked is None or len(self.list_id_checked) == 0:
                    pass
                else:
                    if user_model.data.id in self.list_id_checked:
                        root_item.setCheckState(Qt.Checked)
                        self.item_selected['data'][user_model.data.id] = user_model.data.fullName
                root_tree_view.appendRow(root_item)
        # logger.debug(f'create_tree_view {self.item_selected["data"]}')
        model.itemChanged.connect(lambda item: self.handle_item_changed(item))
        self.treeview_widget.set_model(model = model)
        self.treeview_widget.tree_view.expandAll()

    def handle_item_changed(self, item):
        if item.isCheckable() and item.checkState() == Qt.Checked:
            item_data = item.data(Qt.UserRole)
            self.item_selected['data'][item_data] = item.text()
        elif item.isCheckable() and item.checkState() == Qt.Unchecked:
            for idx, name in self.item_selected['data'].items():
                if item.text() == name:
                    del self.item_selected['data'][idx]
                    break
        self.item_selected_signal.emit(self.item_selected)

    def search_items(self,text):
        self.create_search_tree_view()
        # Convert the search text to its unaccented form
        unaccented_text = unidecode(text)

        # Filter the tree view
        self.filter_recursive(self.treeview_widget.model.invisibleRootItem(), text, unaccented_text)

    def filter_recursive(self, item: QStandardItem, text: str, unaccented_text: str):
        logger.debug(f'filter_recursive = {text} {unaccented_text}')
        try:
            # Loop through the children of the item in reverse order
            for row in range(item.rowCount() - 1, -1, -1):
                child_item = item.child(row)

                # Check if the child item matches the search string or its unaccented form
                pattern: re.Pattern = re.compile(text, re.IGNORECASE | re.UNICODE)
                if (
                    pattern.search(child_item.text())
                    # or unaccented_text.lower() in unidecode(child_item.text()).lower()
                ):
                    # If the child item matches the search string, call the recursive filter method on it
                    self.filter_recursive(child_item, text, unaccented_text)
                else:
                    # If the child item does not match the search string, remove it and all its non-matching children recursively
                    self.filter_recursive(child_item, text, unaccented_text)
                    if not self.item_has_matching_child(child_item, text, unaccented_text):
                        item.removeRow(row)
        except Exception as e:
            logger.critical(f"filter_recursive: {e}")

    def item_has_matching_child(
        self, item: QStandardItem, text: str, unaccented_text: str
    ):
        # Check if any of the item's children match the search string or its unaccented form
        for row in range(item.rowCount()):
            child_item = item.child(row)
            pattern: re.Pattern = re.compile(text, re.IGNORECASE | re.UNICODE)
            if (
                pattern.search(child_item.text())
                or unaccented_text.lower() in unidecode(child_item.text()).lower()
            ):
                return True
            elif self.item_has_matching_child(child_item, text, unaccented_text):
                return True
        return False


    def ok_clicked(self):
        self.close()

    def get_item_selected(self):
        return self.item_selected

    def set_dynamic_stylesheet(self):
        scroll_bar_ver = self.treeview_widget.tree_view.verticalScrollBar()
        scroll_bar_hor = self.treeview_widget.tree_view.horizontalScrollBar()
        scroll_bar_ver.setStyleSheet(Style.StyleSheet.scrollbar_vertical_style1)
        scroll_bar_hor.setStyleSheet(Style.StyleSheet.scrollbar_horizontal_style1)
        self.setStyleSheet(
            f"""
            QDialog {{
                background-color: transparent;
            }}
            QWidget#widget_dialog {{
                background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                padding: 20px; 
                border-radius: 4px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "dialog_text")};
            }}
            
            """
        )

        self.treeview_widget.tree_view.setStyleSheet(
            f"""
            QTreeView {{
                        background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                        alternate-background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                        color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                        border: None;
                        border-radius: 4px;
                    }}
                    QTreeView::item:disabled {{
                        background-color: {Style.PrimaryColor.button_disable_background};
                        alternate-background-color: {Style.PrimaryColor.button_disable_background};
                        color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                        border: None;
                        border-radius: 4px;
                    }}
                    QTreeView::item {{
                        padding: 4px;
                        color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                        background-color: transparent;
                    }}
                    QTreeView::item:selected {{
                        color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                        background-color: transparent;
                    }}
                    QTreeView::branch:has-children:closed {{
                        image:  url({None});
                    }}
                    QTreeView::branch:has-children:open {{
                        image: url({None});
                    }}
                    QTreeView::indicator:checked {{
                        border: none;
                        image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                        background-color: transparent;
                        width: 16px;
                        height: 16px;
                    }}
                    QTreeView::indicator:unchecked {{
                        border: none;
                        image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                        background-color: transparent;
                        width: 16px;
                        height: 16px;
                    }}
        """)

        self.search_widget.search_bar.setStyleSheet(
            f"""
            QLineEdit {{
                background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                border-radius: 4px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "dialog_text")};
                padding-left: 10px;
                padding-right: 10px;
            }}
            QLineEdit:focus {{
                border: 1px solid {Style.PrimaryColor.primary};
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
            }}
            """
        )

class IOGroupWidget(QWidget):


    def __init__(self, parent=None, icon_bottom=None):
        super().__init__(parent)
        self.setContentsMargins(0,0,0,0)
        self.setup_ui()
    def setup_ui(self):
        group_box = QGroupBox(self)
        group_box.setContentsMargins(0,0,0,0)
        icon_bottom = Style.PrimaryImage.expand_bottom
        pixmap_bottom = QPixmap(icon_bottom)
        #self.icon_dropdown = IconDropDown()
        self.hbox = QHBoxLayout()
        self.hbox.setContentsMargins(0,0,0,0)
        #self.widget = QWidget()
        self.widget = QLabel('select')
        self.icon = QLabel()
        self.icon.setPixmap(pixmap_bottom)
        self.icon.setFixedSize(pixmap_bottom.width(),
                          pixmap_bottom.height())
        self.hbox1 = QHBoxLayout()
        self.hbox1.setContentsMargins(0, 0, 0, 0)
        self.widget.setLayout(self.hbox1)
        self.hbox.addWidget(self.widget)
        self.hbox.addWidget(self.icon)

        vbox = QVBoxLayout()
        vbox.setContentsMargins(0,0,0,0)
        vbox.addLayout(self.hbox)

        # Đặt QVBoxLayout cho QGroupBox
        group_box.setLayout(vbox)
        main = QVBoxLayout()
        main.setContentsMargins(0, 0, 0, 0)
        main.addWidget(group_box)
        #self.setLayout(group_box)
class IOGroupWidget1(QWidget):

    def __init__(self, parent=None, icon_bottom=None):
        super().__init__(parent)
        self.widget = QWidget()
        self.widget.setObjectName('Custom_Widget')
        self.setStyleSheet('''
            QWidget#Custom_Widget {
                border-radius: 4px;
                border: 1px solid #5C687F;
            }
        ''')
        #self.setFixedSize(150, 80)
        layout = QHBoxLayout()
        layout.setSpacing(1)
        layout.setContentsMargins(0,0,0,0)
        label = QLabel('select')
        icon_bottom = Style.PrimaryImage.expand_bottom
        pixmap_bottom = QPixmap(icon_bottom)
        self.icon = QLabel()
        self.icon.setPixmap(pixmap_bottom)
        self.icon.setFixedSize(pixmap_bottom.width(),
                          pixmap_bottom.height())
        button = QPushButton()
        button.setIcon(QIcon(icon_bottom))
        layout.addWidget(button)
        layout.addWidget(label)
        self.widget.setLayout(layout)
        self.v = QHBoxLayout(self)
        self.v.setContentsMargins(0, 0, 0, 0)
        self.v.addWidget(self.widget)


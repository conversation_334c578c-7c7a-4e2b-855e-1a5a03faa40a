import os
from src.common.server.server_info import ServerInfoModel
import sys
import subprocess
from src.common.camera.vlc_instance import get_vlc_instance, get_vlc_module
from src.common.controller.main_controller import main_controller
from PySide6.QtGui import QImage, QPixmap
import numpy as np
import time
import av
from src.common.model.camera_model import CameraModel
from src.common.widget.button_state import ButtonState
from pydantic import AnyUrl
import cv2
from PySide6.QtCore import Signal, QThread,QObject
import ctypes
import subprocess
import sys
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import List
from PySide6.QtWidgets import QWidget
from queue import Queue
import threading
from typing import Callable
import time
import uuid
from src.presentation.device_management_screen.widget.ai_state import AIFlowType
import logging
from src.common.camera.pyav_wrapper import PyAVWrapper
logger = logging.getLogger(__name__)
logging.basicConfig()
logging.getLogger('libav').setLevel(level=logging.CRITICAL)
av.logging.set_level(av.logging.PANIC)

class StreamCameraType:
    main_stream = "Main Stream"
    sub_stream = "Sub Stream"
    recognition_stream = "Recognition Stream"
    protection_stream = "Protection Stream"
    frequency_stream = "Frequency Stream"
    access_stream = "Access Stream"
    motion_stream = "Motion Stream"
    traffic_stream = "Traffic Stream"
    weapon_stream = "Weapon Stream"
    ufo_stream = "UFO Stream"
    video_stream = "video_stream"

    @staticmethod
    def get_stream_type(ai_type):
        if ai_type == AIFlowType.RECOGNITION:
            return StreamCameraType.recognition_stream
        elif ai_type == AIFlowType.PROTECTION:
            return StreamCameraType.protection_stream
        elif ai_type == AIFlowType.FREQUENCY:
            return StreamCameraType.frequency_stream
        elif ai_type == AIFlowType.ACCESS:
            return StreamCameraType.access_stream
        elif ai_type == AIFlowType.MOTION:
            return StreamCameraType.motion_stream
        elif ai_type == AIFlowType.TRAFFIC:
            return StreamCameraType.traffic_stream
        elif ai_type == AIFlowType.UFO:
            return StreamCameraType.ufo_stream
        return StreamCameraType.recognition_stream

    @staticmethod
    def get_ai_type(ai_stream):
        if ai_stream == StreamCameraType.recognition_stream:
            return AIFlowType.RECOGNITION
        elif ai_stream == StreamCameraType.protection_stream:
            return AIFlowType.PROTECTION
        elif ai_stream == StreamCameraType.frequency_stream:
            return AIFlowType.FREQUENCY
        elif ai_stream == StreamCameraType.access_stream:
            return AIFlowType.ACCESS
        elif ai_stream == StreamCameraType.motion_stream:
            return AIFlowType.MOTION
        elif ai_stream == StreamCameraType.traffic_stream:
            return AIFlowType.TRAFFIC
        elif ai_stream == StreamCameraType.weapon_stream:
            return AIFlowType.WEAPON
        elif ai_stream == StreamCameraType.ufo_stream:
            return AIFlowType.UFO
        return None

class CameraState:
    paused = 'paused'
    started = 'started'
    stopped = 'stopped'
    connecting = 'connecting'
    unauthorized = 'unauthorized'

# Định nghĩa kiểu callback
VideoLockCB = ctypes.CFUNCTYPE(ctypes.c_void_p, ctypes.c_void_p, ctypes.POINTER(ctypes.POINTER(ctypes.c_uint8)))
VideoUnlockCB = ctypes.CFUNCTYPE(None, ctypes.c_void_p, ctypes.c_void_p, ctypes.POINTER(ctypes.POINTER(ctypes.c_uint8)))
VideoDisplayCB = ctypes.CFUNCTYPE(None, ctypes.c_void_p, ctypes.c_void_p)

class VideoCapture(QThread):
    share_frame_signal = Signal(tuple)
    camera_state_signal = Signal(str)
    buffering_signal = Signal(float)
    media_loaded_signal = Signal()
    next_chunk_signal = Signal(str)
    
    MAX_WIDTH_VIDEO_DECODE = 1280
    MAX_HEIGHT_VIDEO_DECODE = 720
    ENABLE_DEBUG = False

    def __init__(self, camera_id=None, camera_model: CameraModel = None, stream_type=StreamCameraType.main_stream, height=0, width=0, uuid = ''):
        super().__init__()
        logger.debug(f'Khởi tạo VideoCapture: Camera ID={camera_id}, Stream={stream_type}')
        
        # Các thuộc tính cơ bản
        self.list_resize_frame = {}
        self.camera_id = camera_id
        self.camera_model = camera_model
        self.stream_type = stream_type
        self.stream_link = None
        self.camera_state = CameraState.connecting
        self.connect_status = True
        self.fullscreen_state = ButtonState.FullscreenType.EXIT_FULLSCREEN
        self.current_stream_link = self.stream_link
        self.start_time = None
        self.is_send_mat_frame = False
        self.max_width_preview_available = 0
        self.max_height_preview_available = 0
        
        # # Các thuộc tính cho cơ chế kết nối lại
        # self.max_retry_count = 5
        # self.current_retry_count = 0
        # self.base_retry_delay = 30  # 30 giây
        # self.max_retry_delay = 300  # 5 phút
        # self.metrics = {
        #     'connection_attempts': 0,
        #     'successful_connections': 0,
        #     'failed_connections': 0
        # }
        
        # Các thuộc tính khác
        if width is not None and height is not None and width > 0 and height > 0:
            self.update_resize(width, height, uuid)
        self.registered_widgets = []
        self.is_pause_live = False
        self.is_pause_video = False
        self.pyav_wrapper = None
	    
        ############################ Media Player #############################
        vlc_instance = get_vlc_instance()
        logger.info(f'vlc_instance: {vlc_instance}')
        vlc = get_vlc_module()
        if vlc_instance is not None and vlc is not None:
            self.media_list_player = vlc_instance.media_list_player_new()
            self.media_player = self.media_list_player.get_media_player()
            self.media_player.audio_set_mute(True)
            self.media_player.video_set_mouse_input(False)
            self.media_player.video_set_key_input(False)
            self.event_manager = self.media_player.event_manager()
            self.event_manager.event_attach(vlc.EventType.MediaPlayerPositionChanged, self.position_changed)
            self.event_manager.event_attach(vlc.EventType.MediaPlayerOpening, self.media_player_opening)
            self.event_manager.event_attach(vlc.EventType.MediaPlayerBuffering, self.media_player_buffering)
            self.event_manager.event_attach(vlc.EventType.MediaPlayerPlaying, self.media_player_started)
            self.event_manager.event_attach(vlc.EventType.MediaPlayerPaused, self.media_player_paused)
            self.event_manager.event_attach(vlc.EventType.MediaPlayerStopped, self.media_player_stopped)
            self.event_manager.event_attach(vlc.EventType.MediaPlayerEncounteredError, self.media_player_error)
        else:
            logger.error("Không thể khởi tạo VLC instance")
            self.media_list_player = None
            self.media_player = None
            self.event_manager = None
        self.media_path = None
        self.media_width = None
        self.media_height = None
        self.time_start = None
        self.seek_time = None
        self.start_duration = None
        self.end_duration = None
        self.current_duration = 0
        self.timelinecontroller = None

        # Cấp phát bộ đệm cho các khung hình video
        self.frame_buffer = None
        self.original_width_video_playback = None
        self.original_height_video_playback = None
        self.frame_size = None

        # HiepTran: Sử dụng warp để không lỗi khi gọi callback với ctypes
        self.video_lock_callback_wrapped = VideoLockCB(self.video_lock_callback)
        self.video_unlock_callback_wrapped = VideoUnlockCB(self.video_unlock_callback)
        self.video_display_callback_wrapped = VideoDisplayCB(self.video_display_callback)
        ##########################################################

        self.media_loaded = False
        self.pending_seek = None
        self.initial_position_received = False  # Thêm cờ này
        self.last_frame_time = time.time()
        self.target_fps = 10
        self._cleanup_lock = threading.Lock()  # Add thread-safe cleanup lock

    def on_stream_link_changed(self, new_stream_link):
        self.stream_link = new_stream_link
        logger.info(f'Camera {self.camera_id}: Stream link changed, restarting pipeline: check thread {self.isRunning()} - {threading.current_thread()} - qthread {self.thread()}')
        # Bắt đầu pipeline mới
        self.camera_state = CameraState.connecting
        self.connect_status = True
        self.start_thread()

    def register_signal(self,widget = None):
        if widget is not None:
            logger.debug(f'Registering signals for widget {widget.objectName()} on camera {self.camera_id}')
            if hasattr(widget, 'share_frame_signal'):
                self.share_frame_signal.connect(widget.share_frame_signal)
            if hasattr(widget, 'camera_state_signal'):
                self.camera_state_signal.connect(widget.camera_state_signal) 
            if hasattr(widget, 'next_chunk_signal'):
                self.next_chunk_signal.connect(widget.next_chunk_signal) 
            if hasattr(widget, 'on_buffering'):
                self.buffering_signal.connect(widget.on_buffering)
            self.registered_widgets.append(widget)

    def unregister_signal(self,widget = None):
        """Safely unregister widget signals and cleanup if needed"""
        if widget is not None:
            try:
                # Disconnect signals
                if hasattr(widget, 'share_frame_signal'):
                    try:
                        self.share_frame_signal.disconnect(widget.share_frame_signal)
                    except Exception as e:
                        logger.warning(f'Error disconnecting share_frame_signal: {e}')
                        
                if hasattr(widget, 'camera_state_signal'):
                    try:
                        self.camera_state_signal.disconnect(widget.camera_state_signal)
                    except Exception as e:
                        logger.warning(f'Error disconnecting camera_state_signal: {e}')
                        
                if hasattr(widget, 'on_buffering'):
                    try:
                        self.buffering_signal.disconnect(widget.on_buffering)
                    except Exception as e:
                        logger.warning(f'Error disconnecting buffering_signal: {e}')
                
                # Handle widget list cleanup
                if len(self.registered_widgets) > 1:
                    for item in self.registered_widgets:
                        if item == widget:
                            self.registered_widgets.remove(item)
                            break
                elif len(self.registered_widgets) == 1:
                    logger.debug(f'Last widget being unregistered - cleanup everything')
                    self.registered_widgets.remove(widget)
                    if hasattr(self, 'pyav_wrapper') and self.pyav_wrapper is not None:
                        self.pyav_wrapper.close_stream()
                        self.pyav_wrapper = None
                    self.is_pause_video = False
                    threading.Thread(target=self.media_list_player.stop).start()
                    # del self.registered_widgets[widget.objectName()]
                    return True
                
                # Clear any widget-specific cached data
                if hasattr(self, 'list_resize_frame'):
                    self.list_resize_frame.pop(widget.uuid, None)
                    
            except Exception as e:
                logger.error(f'Error during widget unregistration: {e}')
                
        return False

    def connect_camera_pyav(self):
        try:
            logger.debug(f'Camera {self.camera_id}: Attempting PyAV connection to {self.current_stream_link}')
            
            # Initialize PyAV wrapper with hardware acceleration enabled
            self.pyav_wrapper = PyAVWrapper()
            
            # Open the stream - PyAVWrapper will handle hardware acceleration internally
            if not self.pyav_wrapper.open_stream(self.current_stream_link):
                logger.debug(f'Camera {self.camera_id}: Failed to open stream')
                if len(self.registered_widgets) > 0:
                    self.camera_state = CameraState.stopped
                    self.camera_state_signal.emit(self.camera_state)
                return False
                
            # Get stream info for logging
            stream_info = self.pyav_wrapper.stream_info
            logger.debug(f'Camera {self.camera_id}: Stream opened successfully - Codec: {stream_info["codec"]}, '
                        f'Resolution: {stream_info["width"]}x{stream_info["height"]}, '
                        f'FPS: {stream_info["fps"]}, '
                        f'HW Accel: {stream_info["hw_accel"]}')
            
            self.camera_state = CameraState.connecting
            self.camera_state_signal.emit(self.camera_state)
            return True
            
        except Exception as e:
            logger.debug(f'Camera {self.camera_id}: PyAV connection failed: {str(e)}')
            if len(self.registered_widgets) > 0:
                self.camera_state = CameraState.stopped
                self.camera_state_signal.emit(self.camera_state)
            return False

    def run(self):
        self.start_time = time.time()
        logger.info(f'Camera {self.camera_id}: Starting video capture thread')
        self.use_pyav_pipeline()

    def play_live(self):
        self.is_pause_live = False
        logger.debug(f'Camera {self.camera_id}: Resuming live stream')
        pass

    def pause_live(self):
        logger.debug(f'Camera {self.camera_id}: Pausing live stream')
        self.is_pause_live = True

    def stop_live(self):
        pass

    def use_pyav_pipeline(self):
        logger.info(f'Camera {self.camera_id}: Bắt đầu pipeline PyAV - {self.current_stream_link} - {self.stream_type}')
        self.start_time = time.time()
                
        if self.stream_link is None:
            logger.error(f'Camera {self.camera_id}: Không có stream link')
            return
            
        try:
            if self.current_stream_link != self.stream_link or self.pyav_wrapper is None:
                self.current_stream_link = self.stream_link
                self.camera_state = CameraState.connecting
                self.camera_state_signal.emit(self.camera_state)
                
                connection_successful = self.connect_camera_pyav()
                
                if not connection_successful:
                    logger.error('Kết nối thất bại')
                    if len(self.registered_widgets) > 0:
                        self.camera_state = CameraState.stopped
                        self.camera_state_signal.emit(self.camera_state)
                    self.clear_pyav_container()
                    return

            logger.info(f'Camera {self.camera_id}: Kết nối thành công')

            frame_retrieval_start = time.time()
            frame_retrieval_timeout = 10
            frame_received = False
            
            # Định nghĩa hàm callback để xử lý frame
            def frame_callback(frame_array, timestamp):
                nonlocal frame_received
                frame_received = True
                
                # check if any widget is visible
                has_widget_visible = False
                for widget in self.registered_widgets:
                    from src.common.widget.camera_widget import CameraWidget
                    if isinstance(widget, CameraWidget):
                        # logger.info(f'frame_callback: is_actually_visible: {widget.get_is_actually_visible()}')
                        if widget.get_is_actually_visible():
                            has_widget_visible = True
                            break

                if self.connect_status and has_widget_visible:
                    if self.list_resize_frame is None or len(self.list_resize_frame) == 0 or self.is_pause_live:
                        return
                    self.camera_state = CameraState.started
                    self.camera_state_signal.emit(self.camera_state)
                    
                    pixmap_resized = self.mat_to_q_pixmap(frame_array)

                    if not self.is_send_mat_frame:
                        frame_array = None
                    data = (True, frame_array, pixmap_resized)
                    self.share_frame_signal.emit(data)
                elif not self.connect_status:
                    data = (True, None, None)
                    self.share_frame_signal.emit(data)
            
            # Tính toán kích thước mục tiêu với tỷ lệ khung hình
            target_width = None
            target_height = None
            target_format = None
            if self.max_width_preview_available > 0 and self.max_height_preview_available > 0:
                # Sử dụng thông tin stream từ PyAVWrapper để xác định tỷ lệ khung hình
                stream_info = self.pyav_wrapper.stream_info
                if stream_info and 'width' in stream_info and 'height' in stream_info:
                    # Tính toán tỷ lệ khung hình
                    aspect_ratio = stream_info['width'] / float(stream_info['height'])
                    original_frame_width = self.max_width_preview_available
                    original_frame_height = self.max_height_preview_available
                    
                    # Giữ nguyên tỷ lệ khung hình
                    if aspect_ratio > 1:
                        original_frame_height = int(self.max_width_preview_available / aspect_ratio)
                    else:
                        original_frame_width = int(self.max_height_preview_available * aspect_ratio)
                        
                    target_width = original_frame_width
                    target_height = original_frame_height
                    
                    # Đặt định dạng dựa trên cấu hình
                    target_format = 'bgr24'
            
            # Sử dụng PyAVWrapper để lấy frame với callback và cờ is_running
            logger.info(f'use_pyav_pipeline: target_width = {target_width} - target_height = {target_height} - target_format = {target_format}')
            frame, _ = self.pyav_wrapper.get_frame(
                width=target_width,
                height=target_height,
                format=target_format,
                callback=frame_callback,
            )
            logger.info(f'use_pyav_pipeline: frame = {frame}')

            if not frame_received and frame is None:
                # Không nhận được frame, kiểm tra timeout
                if time.time() - frame_retrieval_start > frame_retrieval_timeout:
                    logger.debug(f'Không nhận được frame trong {frame_retrieval_timeout} giây')
                    raise TimeoutError("Không nhận được frame trong thời gian chờ")
                    
            if self.current_stream_link != self.stream_link:
                self.clear_pyav_container()
                return
                    
        except TimeoutError as te:
            logger.debug(f'Lỗi timeout stream: {te}')
            if len(self.registered_widgets) > 0:
                self.camera_state = CameraState.stopped
                self.camera_state_signal.emit(self.camera_state)
            self.clear_pyav_container()
        except Exception as e:
            logger.debug(f'Lỗi stream: {e}')
            self.clear_pyav_container()
        finally:
            logger.info(f'Camera {self.camera_id}: Stream đã dừng')
            
        # Dọn dẹp sau khi vòng lặp kết thúc
        logger.debug(f'Stream đã dừng. Camera ID: {self.camera_id}')
        if len(self.registered_widgets) > 0:
            self.camera_state = CameraState.stopped
            self.camera_state_signal.emit(self.camera_state)
            
    def stop_capture(self):
        """Safely stop capture and cleanup resources"""
        self.is_pause_video = False
        threading.Thread(target=self.media_list_player.stop).start()
        self.clear_pyav_container()

    def clear_pyav_container(self):
        """Safely clean up PyAV container and associated resources"""
        logger.debug(f'Camera {self.camera_id}: PyAV cleanup started')
        # Clear all referencesRF
        if hasattr(self, 'pyav_wrapper') and self.pyav_wrapper is not None:
            self.pyav_wrapper.close_stream()
            self.pyav_wrapper = None
        
        if hasattr(self, 'frame_buffer') and self.frame_buffer is not None:
            self.frame_buffer = None
        # Log completion
        logger.debug(f'Camera {self.camera_id}: PyAV cleanup completed')

    def move_to_trash(self):
        if hasattr(self, 'pyav_wrapper') and self.pyav_wrapper is not None:
            self.pyav_wrapper.close_stream()
            self.pyav_wrapper = None

    def reset_thread(self):
        """Reset all thread state, timers and flags for a clean restart"""
        logger.debug(f'Camera {self.camera_id}: Resetting thread state')
        
        # Reset basic thread state
        self.start_time = None
        
        # Reset connection state
        self.connect_status = True
        
        # Reset stream state
        self.is_pause_live = False
        
        # Reset error flags
        self.error_decode_gpu_need_re_run_with_cpu = False
        
        # Reset camera state
        self.camera_state = CameraState.connecting
        
        logger.debug(f'Camera {self.camera_id}: Thread state reset completed')

    def start_thread(self):
        logger.info(f'Camera {self.camera_id}: Starting video capture thread')
        if not self.isRunning():
            self.start()
        else:
            logger.info(f'Camera {self.camera_id}: Thread đang chạy rồi')
            # Create a new thread to handle the restart
            def restart_thread():
                self.quit()
                self.wait()
                self.start()
            
            threading.Thread(target=restart_thread, daemon=True).start()

    def avframe_to_ndarray_and_flip(self, frame: av.VideoFrame):
        frame_ndarray = frame.to_ndarray()
        flip_frame = np.flipud(frame_ndarray)
        return flip_frame

    def update_resize(self, width=None, height=None, uuid = '', remove=False, is_fullscreen = False):
        logger.debug(f'Camera {self.camera_id}: Updating resize - Width={width}, Height={height}, Widget={uuid}, Remove={remove} - is_fullscreen: {is_fullscreen}')
        if remove and uuid in self.list_resize_frame:
            self.list_resize_frame.pop(uuid)
        else:
            self.list_resize_frame[uuid] = (width, height)
        max_width = 0
        max_height = 0
        # find biggest width and height in list_resize_frame
        logger.debug(f'update_resize: list_resize_frame: {self.list_resize_frame}')
        for key, value in self.list_resize_frame.items():
            w, h = value
            if w > max_width:
                max_width = w
            if h > max_height:
                max_height = h
        # if max_width > self.MAX_WIDTH_VIDEO_DECODE: and max_height > self.MAX_HEIGHT_VIDEO_DECODE: 
        # -> resize to MAX_WIDTH_VIDEO_DECODE and MAX_HEIGHT_VIDEO_DECODE
        if max_width > self.MAX_WIDTH_VIDEO_DECODE:
            max_width = self.MAX_WIDTH_VIDEO_DECODE
        if max_height > self.MAX_HEIGHT_VIDEO_DECODE:
            max_height = self.MAX_HEIGHT_VIDEO_DECODE
        
        logger.debug(f'update_resize: max_width: {max_width} - max_height: {max_height}')
        self.max_width_preview_available = max_width
        self.max_height_preview_available = max_height
        
        # Cập nhật độ phân giải Media Player - Tạm thời chưa xử lý tối ưu sau
        # self.update_media_player_resolution(is_fullscreen)

        logger.debug(f'Camera {self.camera_id}: New dimensions - Max Width={self.max_width_preview_available}, Max Height={self.max_height_preview_available}')

    def set_disconnect_fullscreen(self):
        logger.debug(f'set_disconnect_fullscreen of camera_id = {self.camera_id}')
        self.fullscreen_width = 0
        self.fullscreen_height = 0

    def make_divisible_by_eight(self, number):
        remainder = number % 8
        if remainder != 0:
            number += 8 - remainder
        return number

    @staticmethod
    def mat_to_q_pixmap(mat):
        h, w, c = mat.shape
        d = mat.dtype.itemsize
        s = c * w * d
        img = QImage(
            mat, w, h, s, QImage.Format_RGB888).rgbSwapped()
        return QPixmap.fromImage(img)


    ###################### Media Player #############################
    def video_lock_callback(self, opaque, planes):
        # Ensure planes[0] points to the allocated buffer
        planes[0] = ctypes.cast(ctypes.addressof(self.frame_buffer), ctypes.POINTER(ctypes.c_uint8))
        return opaque

    def video_unlock_callback(self, opaque, picture, planes):
        # Optional: Handle unlocking if needed
        pass

    def video_display_callback(self, opaque, picture):
        if self.is_pause_video:
            now = time.time()
            if now - self.last_frame_time < 1 / self.target_fps:
                return  # Bỏ qua frame nếu xử lý quá nhanh
            else:
                self.last_frame_time = now
                # Convert the frame buffer to a NumPy array
                frame_array = np.ctypeslib.as_array(self.frame_buffer, shape=(self.original_height_video_playback, self.original_width_video_playback, 3))
                frame_rgb = frame_array.reshape((self.original_height_video_playback, self.original_width_video_playback, 3))  # Reshape to image dimensions

                # resize with aspect ratio and max_width_preview_available and max_height_preview_available
                target_width, target_height = self.calculate_target_dimensions(self.original_width_video_playback, self.original_height_video_playback, self.max_width_preview_available, self.max_height_preview_available)
                frame_rgb = cv2.resize(frame_rgb, (target_width, target_height))

                # Convert RGB to BGR for OpenCV display
                frame_bgr = cv2.cvtColor(frame_rgb, cv2.COLOR_RGB2BGR)
                pixmap_resized = self.mat_to_q_pixmap(frame_bgr)
                data = (True, frame_bgr, pixmap_resized)
                self.share_frame_signal.emit(data)

    def media_player_opening(self, event):
        logger.debug(f"media_player_opening = {event.u.new_status}")
        self.camera_state = CameraState.connecting
        self.camera_state_signal.emit(
            self.camera_state)
        # self.media_loaded = False
        # logger.debug(f'media_player_opening: duration {self.get_duration()} - self.get_time() {self.get_time()}')

    def media_player_buffering(self, event):
        buffering_percent = event.u.new_cache
        # if buffering_percent == 0.0:
        #     logger.debug(f'media_player_buffering: {buffering_percent}% buffered - duration {self.get_duration()} - time {self.get_time()}')
        
        # if buffering_percent == 100.0:
        #     self.media_loaded = True
        #     self.media_loaded_signal.emit()

        if hasattr(self, 'buffering_signal'):
            self.buffering_signal.emit(buffering_percent)

    def media_player_started(self, event):
        logger.debug(f"media_player_started START = {event.u.new_status}")
        self.camera_state = CameraState.started
        self.camera_state_signal.emit(
            self.camera_state)
        if self.seek_time is not None and self.start_duration is not None:
            duration = self.get_duration()
            logger.debug(f'Seeking to position {self.seek_time}ms / {duration}ms')
            if 0 < self.seek_time < duration:
                # Calculate position and seek
                pos = self.seek_time / duration
                logger.debug(f'Seeking to position {pos} ({self.seek_time}ms / {duration}ms)')
                self.set_position(pos)
        logger.debug(f'media_player_started:')

    def position_changed(self, event):
        # new_position = event.u.new_position
        current_time = self.get_time()
        # duration = self.get_duration()
        try:
            if self.timelinecontroller is not None:
                self.current_duration = self.start_duration + current_time
                self.timelinecontroller.onPositionChanged(self.current_duration)

        except Exception as e:
            logger.debug(f'position_changed = {e}')

    def media_player_paused(self, event):
        logger.debug(f"media_player_paused = {event.u.new_status}")
        self.camera_state = CameraState.paused
        self.camera_state_signal.emit(self.camera_state)

    def media_player_stopped(self, event):
        logger.debug(f"media_player_stopped = {event.type}-{self.media_list_player.get_state()}")
        if len(self.registered_widgets) > 0:
            self.camera_state = CameraState.stopped
            self.camera_state_signal.emit(self.camera_state)
        # if event.u.new_status != 0:
        #     self.next_chunk_signal.emit('ahihi')
    def media_player_error(self, event):
        logger.debug(f"media_player_error = {event.type}-{self.media_list_player.get_state()}")

    def load_media(self, media_path, vlc_options=None, seek_time=None,start_duration = None,end_duration = None):
        logger.debug(f'load_media ===== {media_path} - seek_time: {seek_time}')
        self.seek_time = seek_time
        self.start_duration = start_duration
        self.current_duration = start_duration
        self.end_duration = end_duration
        self.media_path = media_path

        # self.media_loaded = False
        # self.pending_seek = None
        # self.initial_position_received = False  # Reset flag when loading new media

        url = AnyUrl(media_path)
        if url.scheme == 'rtsp':
            vlc_options = 'rtsp-tcp'

        # Enhanced caching and buffering options
        default_options = [
            '--network-caching=1000',
            '--file-caching=1000',
            '--live-caching=1000',
            '--sout-mux-caching=1000',
            '--clock-jitter=0',
            '--clock-synchro=0'
        ]
        
        if vlc_options:
            vlc_options = f"{vlc_options} {' '.join(default_options)}"
        else:
            vlc_options = ' '.join(default_options)

        vlc_instance = get_vlc_instance()
        if vlc_instance is not None:
            media = vlc_instance.media_new(media_path, vlc_options)
            media_list = vlc_instance.media_list_new([media])
            self.media_list_player.set_media_list(media_list)
            self.media_list_player.play_item_at_index(0)
        else:
            logger.error("VLC instance không khả dụng để load media")

    def get_current_url(self):
        return self.media_path

    def get_xwindow(self):
        return self.media_player.get_xwindow()

    def get_size_from_curl(self, url: str) -> tuple[int, int]:
        """
        Get video dimensions from an MPD manifest file.
        
        Args:
            url (str): URL of the MPD manifest file
            
        Returns:
            tuple[int, int]: Width and height of the video. Returns (0, 0) if dimensions cannot be determined.
        """
        try:
            result = subprocess.run(['curl', url], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            if result.returncode != 0:
                logger.error(f'Failed to fetch MPD file: {result.stderr.decode()}')
                return 0, 0
            
            xml_content = result.stdout.decode('utf-8')
            root = ET.fromstring(xml_content)
            
            # First try to get dimensions from AdaptationSet
            adaptation_sets = root.findall('.//{urn:mpeg:dash:schema:mpd:2011}AdaptationSet[@contentType="video"]')
            if adaptation_sets:
                adapt_set = adaptation_sets[0]
                width = int(adapt_set.get('maxWidth', 0))
                height = int(adapt_set.get('maxHeight', 0))
                if width > 0 and height > 0:
                    return width, height
                
            # If not found in AdaptationSet, try Representation
            representations = root.findall('.//{urn:mpeg:dash:schema:mpd:2011}Representation')
            if representations:
                rep = representations[0]
                width = int(rep.get('width', 0))
                height = int(rep.get('height', 0))
                return width, height
            
            logger.warning(f'No video dimensions found in MPD file')
            return 0, 0
        
        except ET.ParseError as e:
            logger.error(f'Failed to parse MPD XML: {e}')
            return 0, 0
        except Exception as e:
            logger.error(f'Error getting video dimensions: {e}')
            return 0, 0

    def play_video(self):
        self.is_pause_video = True
        logger.debug(f'play_video:')
        if self.get_current_url() is not None:
            size = self.get_size_from_curl(self.get_current_url())
            # div by 2 to scale down resolution -> increase performance
            target_width, target_height = self.calculate_target_dimensions(size[0] / 2, size[1] / 2, self.MAX_WIDTH_VIDEO_DECODE, self.MAX_HEIGHT_VIDEO_DECODE)
            
            # update target_width and target_height did aspect ratio to original_width_video_playback and original_height_video_playback
            self.original_width_video_playback = target_width
            self.original_height_video_playback = target_height

            self.update_frame_buffer(target_width, target_height)
            self.media_player.video_set_callbacks(self.video_lock_callback_wrapped, self.video_unlock_callback_wrapped,
                                                self.video_display_callback_wrapped, None)
            logger.debug(f'play_video: target_width: {target_width} - target_height: {target_height}')
            self.media_player.video_set_format("RV24", target_width, target_height, target_width * 3)
            self.media_list_player.play()

    def start_video(self):
        logger.debug("play")
        self.is_pause_video = True
        self.media_list_player.play()

    def pause_video(self):
        # play: 0
        # pause: 1
        self.is_pause_video = False
        logger.debug("pause_video: pause")
        # self.media_list_player.set_pause(1)
        self.media_list_player.pause()

    def stop_video(self):
        logger.debug(f'Camera {self.camera_id}: Stopping video')
        self.media_list_player.stop()

    def set_volume(self, volume: int):
        """Set video volume (0-100)"""
        if hasattr(self, 'media_player'):
            # Convert 0-100 range to 0-200 for VLC
            vlc_volume = min(200, volume * 2)
            self.media_player.audio_set_volume(vlc_volume)

    def get_volume(self) -> int:
        """Get current volume (0-100)"""
        if hasattr(self, 'media_player'):
            # Convert VLC's 0-200 range to 0-100
            return self.media_player.audio_get_volume() // 2
        return 0

    def mute(self):
        """Mute audio"""
        if hasattr(self, 'media_player'):
            self.media_player.audio_set_mute(True)

    def unmute(self):
        """Unmute audio"""
        if hasattr(self, 'media_player'):
            self.media_player.audio_set_mute(False)

    def set_position(self, position):
        self.media_player.set_position(position)

    def get_position(self):
        return self.media_player.get_position()

    def get_time(self):
        return self.media_player.get_time()

    def set_time(self, time):
        self.media_player.set_time(time)

    def get_duration(self):
        return self.media_player.get_media().get_duration()

    def is_playing(self):
        return self.media_player.is_playing()

    def get_length(self):
        return self.media_player.get_length()

    def set_window_id(self, videoframe):
        platform_setters = {
            'linux': lambda: self.media_player.set_xwindow(videoframe.winId()),
            'win32': lambda: self.media_player.set_hwnd(videoframe.winId()),
            'darwin': lambda: self.media_player.set_nsobject(int(videoframe.winId()))
        }
        platform_setters.get(sys.platform, lambda: None)()

    def set_speed(self, speed):
        self.media_player.set_rate(speed)

    def get_speed(self):
        return self.media_player.get_rate()

    def fetch_mpd_and_calculate_duration(self, url: str):
        try:
            result = subprocess.run(['curl', url], stdout=subprocess.PIPE)
            xml_content = result.stdout.decode('utf-8')
            root = ET.fromstring(xml_content)
            availability_start = datetime.fromisoformat(root.attrib['availabilityStartTime'].replace("Z", "+00:00"))
            publish = datetime.fromisoformat(root.attrib['publishTime'].replace("Z", "+00:00"))
            duration = publish - availability_start
            return duration.total_seconds() * 1000
        except Exception as e:
            logger.error(f'Error: {e}')
            return -1

    def get_publish_time(self, url: str):
        try:
            result = subprocess.run(['curl', url], stdout=subprocess.PIPE)
            xml_content = result.stdout.decode('utf-8')
            root = ET.fromstring(xml_content)
            publish = datetime.fromisoformat(root.attrib['publishTime'].replace("Z", "+00:00"))
            return publish
        except Exception as e:
            logger.error(f'Error: {e}')
            return -1

    def set_send_mat_frame(self, allow):
        self.is_send_mat_frame = allow

    def __del__(self):
        """Destructor to ensure cleanup when object is destroyed"""
        try:
            logger.debug(f'Camera {self.camera_id}: Destructor called')
            self.stop_capture()
        except Exception as e:
            logger.error(f'Error during VideoCapture destruction: {e}')

    # def calculate_next_retry_time(self):
    #     """Tính toán thời gian chờ cho lần retry tiếp theo sử dụng exponential backoff."""
    #     delay = min(self.base_retry_delay * (2 ** self.current_retry_count), self.max_retry_delay)
    #     return time.time() + delay

    # def log_connection_attempt(self, success):
    #     """Ghi lại thông tin về lần kết nối."""
    #     self.metrics['connection_attempts'] += 1
    #     if success:
    #         self.metrics['successful_connections'] += 1
    #     else:
    #         self.metrics['failed_connections'] += 1

    def calculate_target_dimensions(self, video_width, video_height, max_width_preview_available, max_height_preview_available):
        """Tính toán kích thước mục tiêu dựa trên aspect ratio của video"""
        if video_width <= 0 or video_height <= 0:
            # Sử dụng aspect ratio mặc định 16:9 nếu không lấy được kích thước video
            aspect_ratio = 16/9
            target_width = max_width_preview_available
            target_height = int(self.target_width / aspect_ratio)
            # Đảm bảo kích thước chia hết cho 4 để tránh lỗi padding
            target_width = self.make_divisible_by_four(target_width)
            target_height = self.make_divisible_by_four(target_height)
            logger.debug(f'Camera {self.camera_id}: Sử dụng aspect ratio mặc định - {target_width}x{target_height}')
            return target_width, target_height

        # Tính toán aspect ratio gốc
        original_aspect_ratio = float(video_width) / float(video_height)
        logger.debug(f'calculate_target_dimensions: original_aspect_ratio: {original_aspect_ratio}')
        
        # Tính toán kích thước mới dựa trên aspect ratio
        if original_aspect_ratio > 1:  # Landscape
            target_width = max_width_preview_available
            target_height = int(max_width_preview_available / original_aspect_ratio)
        else:  # Portrait
            target_height = max_height_preview_available
            target_width = int(target_height * original_aspect_ratio)
        
        # Đảm bảo kích thước chia hết cho 4 để tránh lỗi padding
        target_width = self.make_divisible_by_four(target_width)
        target_height = self.make_divisible_by_four(target_height)
            
        logger.debug(f'Camera {self.camera_id}: Aspect ratio gốc: {original_aspect_ratio:.2f} - Kích thước mới: {target_width}x{target_height}')
        return target_width, target_height

    def make_divisible_by_four(self, number):
        """Đảm bảo số chia hết cho 4"""
        return number + (4 - (number % 4)) % 4

    def update_frame_buffer(self, target_width, target_height):
        """Cập nhật frame buffer nếu cần thiết"""
        if not (target_width and target_height and self.stream_type == StreamCameraType.video_stream):
            return

        logger.debug(f'update_frame_buffer: target_width: {target_width} - target_height: {target_height}')
        # Tính toán frame size với padding phù hợp
        frame_size = target_width * target_height * 3  # RGB (3 byte mỗi pixel)
        logger.debug(f'update_frame_buffer: frame_size: {frame_size}')
        # Đảm bảo kích thước frame chia hết cho 4
        self.frame_size = self.make_divisible_by_four(frame_size)
        logger.debug(f'update_frame_buffer: frame_size after make_divisible_by_four: {self.frame_size}')
        
        # if self.frame_size is None:
        self.frame_buffer = (ctypes.c_uint8 * self.frame_size)()
        logger.debug(f'Khởi tạo frame buffer mới - Kích thước: {self.frame_size}')

class VideoCaptureController(QObject):
    video_capture_created_signal = Signal(tuple)
    __instance = None
    def __init__(self, parent=None, target: Callable = None, args=()):
        super().__init__(parent)
        self._cleanup_lock = threading.Lock()
        self.target = target
        self.args = args
        self.input_queue = Queue()
        self.threads = None
        self.list_video_capture = {}
        self.list_widgets = {}
        self.threads = self.start_threads(64,self.process_data)
        self.temp = []

    @staticmethod
    def get_instance():
        if VideoCaptureController.__instance is None:
            VideoCaptureController.__instance = VideoCaptureController()
        return VideoCaptureController.__instance

    def process_data(self)-> None:
        while True:
            target = self.input_queue.get()
            if target is None:
                break
            id,camera_model,stream_type = target
            video_capture = self.get_video_capture(camera_model=camera_model,stream_type=stream_type)
            widget = self.list_widgets[id]
            widget.process_video_capture(video_capture)
            del self.list_widgets[id]
            logger.debug(f'process_data = {self.list_video_capture}')
            self.input_queue.task_done()

    def register_video_capture(self,widget:QWidget = None,camera_model:CameraModel = None,stream_type = StreamCameraType.main_stream):
        id = uuid.uuid4()
        self.list_widgets[id] = widget
        self.input_queue.put((id,camera_model,stream_type))

    def unregister_video_capture(self,widget:QWidget):
        if widget is not None:
            video_capture:VideoCapture = widget.video_capture
            camera_model:CameraModel = video_capture.camera_model
            stream_type = video_capture.stream_type
            ok = video_capture.unregister_signal(widget=widget)
            if ok:
                if camera_model.data.id in self.list_video_capture:
                    if stream_type != StreamCameraType.video_stream:
                        # self.temp.append(self.list_video_capture[camera_model.data.id][stream_type])
                        del self.list_video_capture[camera_model.data.id][stream_type]
                    else:
                        # self.temp.append(video_capture)
                        self.list_video_capture[camera_model.data.id][stream_type].remove(video_capture)

    def create_video_capture(self,camera_model:CameraModel = None,stream_type = StreamCameraType.main_stream):
        start_time = time.time()
        video_capture = VideoCapture(camera_id=camera_model.data.id,camera_model=camera_model,stream_type=stream_type)
        end_time = time.time()
        logger.debug(f'end_time - start_time = {end_time-start_time}')
        # lưu video_capture vào self.list_video_capture nhằm có thể tái sử dụng frame trong trường camera được open ở 2 vị trí grid khác nhau
        if stream_type != StreamCameraType.video_stream:
            if camera_model.data.id in self.list_video_capture:
                ##################################################
                # Xử lý vấn đề bất đồng bộ
                # Case open 1 camera trên virtual window -> có 2 widget cần đăng ký video_capture, do quá trình xử lý việc đăng ký video_capture trong multi thread lên cần cập nhật registered_widgets để quá trình hủy đăng ký video_capture được đồng bộ
                if stream_type in self.list_video_capture[camera_model.data.id]:
                    temp_video_capture = self.list_video_capture[camera_model.data.id][stream_type]
                    video_capture.registered_widgets.extend(temp_video_capture.registered_widgets)
                ###################################################
                self.list_video_capture[camera_model.data.id][stream_type] = video_capture

            else:
                self.list_video_capture[camera_model.data.id] = {stream_type:video_capture}
        else:
            if camera_model.data.id in self.list_video_capture:
                if stream_type in self.list_video_capture[camera_model.data.id]:
                    self.list_video_capture[camera_model.data.id][stream_type].append(video_capture)
                else:
                    self.list_video_capture[camera_model.data.id][stream_type] = [video_capture]
            else:
                self.list_video_capture[camera_model.data.id] = {stream_type:[video_capture]}
        return video_capture


    def get_video_capture(self,camera_model:CameraModel = None, stream_type = StreamCameraType.main_stream):
        logger.debug(f'get_video_capture: stream_type = {stream_type}')
        if stream_type != StreamCameraType.video_stream:
            if camera_model is not None:
                if camera_model.data.id in self.list_video_capture:
                    if stream_type in self.list_video_capture[camera_model.data.id]:
                        return self.list_video_capture[camera_model.data.id][stream_type]
                    else:
                        return self.create_video_capture(camera_model=camera_model,stream_type=stream_type)
                else:
                    return self.create_video_capture(camera_model=camera_model,stream_type=stream_type)
        else:
            return self.create_video_capture(camera_model=camera_model,stream_type=stream_type)
    
    def remove_video_capture(self,server:ServerInfoModel):
        list_id = []
        for id,item in self.list_video_capture.items():
            for stream_type, video_capture in item.items():
                if video_capture.camera_model.data.server_ip == server.data.server_ip:
                    list_id.append(id)
        for id in list_id:
            del self.list_video_capture[id]

    def start_threads(self,number: int, target: Callable, *args) -> List[threading.Thread]:
        threads = []
        for _ in range(number):
            thread = threading.Thread(target=target, args=args)
            thread.daemon = True
            threads.append(thread)
            thread.start()
        return threads
    
    def cleanup(self):
        """Clean up all video captures and resources"""
        with self._cleanup_lock:
            try:
                # Stop all threads
                for _ in range(len(self.threads or [])):
                    self.input_queue.put(None)
                
                # Clean up all video captures
                for camera_id in list(self.list_video_capture.keys()):
                    for stream_type in list(self.list_video_capture[camera_id].keys()):
                        try:
                            video_capture = self.list_video_capture[camera_id][stream_type]
                            video_capture.stop_capture()
                        except Exception as e:
                            logger.error(f'Error cleaning up video capture {camera_id}: {e}')
                
                self.list_video_capture.clear()
                self.list_widgets.clear()
                
                # Clear queue
                while not self.input_queue.empty():
                    try:
                        self.input_queue.get_nowait()
                    except Queue.Empty:
                        break
                        
            except Exception as e:
                logger.error(f'Error during VideoCaptureController cleanup: {e}')

    def __del__(self):
        """Ensure cleanup on destruction"""
        try:
            self.cleanup()
        except Exception as e:
            logger.error(f'Error during VideoCaptureController destruction: {e}')

video_capture_controller = VideoCaptureController.get_instance()

# Add this function at module level (outside any class)
def _isolated_container_close(container_id):
    """Run in a separate process to safely close a PyAV container"""
    try:
        import logging
        logging.basicConfig(level=logging.debug)
        logger = logging.getLogger("isolated_cleanup")
        
        logger.debug(f"Isolated process starting cleanup for container {container_id}")
        
        # The container object itself can't be passed between processes,
        # so we're just acknowledging the cleanup request
        logger.debug(f"Container {container_id} resources released by separate process")
        
        return True
    except:
        return False

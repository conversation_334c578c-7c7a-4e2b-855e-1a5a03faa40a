from PySide6.QtCore import QThread, Signal
from PySide6.QtGui import QImage, QPixmap
import time
import socket
import platform
import av
import multiprocessing

class VideoWorker(QThread):
    frame_ready = Signal(QPixmap)
    error = Signal(str)
    stopped = Signal()
    
    MAX_CAMERAS = 144
    
    def __init__(self, url, use_gpu=False):
        super().__init__()
        self.url = url
        self.use_gpu = use_gpu
        self._running = False
        self._stopping = False
        self.container = None
        self.video_stream = None
        
        # Error handling
        self.retry_count = 0
        self.max_retries = 3
        self.retry_delay = 2
        self.frame_timeout = 10  # 5 seconds timeout
        self.last_frame_time = time.time()
        self.first_frame_received = False
        
        # Stream options
        self.options = {
            'rtsp_transport': 'tcp',
            'fflags': 'nobuffer+genpts+discardcorrupt',
            'flags': 'low_delay',
            'framedrop': '1',
            'threads': '1',
            'stimeout': '5000000',  # Socket timeout 5s
            'max_delay': '500000',  # Max delay 500ms
            'rtsp_flags': 'prefer_tcp',
            'enable_rtp_recovery': '1'
        }
        
        self.system = platform.system()
        
        # FPS control để giảm tải
        self._target_fps = 15
        self._frame_interval = 1.0 / self._target_fps
        self._last_frame_time = 0

    def stop(self):
        """Stop the worker thread"""
        print(f"Stopping worker for URL: {self.url} ##########")
        self._running = False
        self._stopping = True
        
        try:
            # Đảm bảo thread dừng lại
            if self.isRunning():
                self.wait(1000)  # Đợi tối đa 1 giây
                # Nếu vẫn chưa dừng, force quit
                if self.isRunning():
                    print(f"Worker for URL {self.url} is still running, force quit")
                    self.terminate()
                    self.wait()
            # Emit stopped signal
            self.stopped.emit()
            # Close video container if open
            if self.container:
                self.container.close()
                self.container = None
            print(f"Worker successfully stopped for URL: {self.url}")
            
        except Exception as e:
            print(f"Error during worker cleanup: {e}")
            # Still try to emit stopped signal
            self.stopped.emit()

    def force_stop(self):
        if not self.wait(1000):
            print(f"Worker for URL {self.url} is still running after timeout, force stopping")
            self.stopped.emit()
            self.stop()
            self.terminate()
            self.wait()

    def run(self):
        self._running = True
        
        try:
            self.container = av.open(self.url, options={
                'rtsp_transport': 'tcp',
                'fflags': 'nobuffer',  # Không buffer frame
                'flags': 'low_delay',   # Giảm độ trễ
                'framedrop': '1',       # Cho phép bỏ frame để theo kịp stream
                'threads': '1'          # Dùng 1 thread cho decode
            })
            
            self.video_stream = self.container.streams.video[0]
            
            for frame in self.container.decode(video=0):
                current_time = time.time()
                
                # FPS control
                if current_time - self._last_frame_time < self._frame_interval:
                    continue
                
                # Convert frame to RGB
                img = frame.to_ndarray(format='rgb24')
                
                # Create QImage and QPixmap
                height, width = img.shape[0:2]
                bytes_per_line = 3 * width
                q_image = QImage(img.data, width, height, bytes_per_line, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)
                
                # Emit frame and clear references
                self.frame_ready.emit(pixmap)
                self._last_frame_time = current_time
                
                # Clear references to allow garbage collection
                del img
                del q_image
                del pixmap
                
                if not self._running:
                    break
                
        except Exception as e:
            self.error.emit(str(e))
            
        finally:
            self.cleanup()
            self.stopped.emit()

    def cleanup(self):
        """Clean up resources"""
        if self.container:
            try:
                self.container.close()
            except:
                pass
        self.container = None
        self.video_stream = None

    def __del__(self):
        if not self._stopping:
            self.stop()
            self.wait(1000)  # Đợi tối đa 1 giây

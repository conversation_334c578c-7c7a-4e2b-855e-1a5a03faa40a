from PySide6.QtCore import QObject, Signal, Slot
from PySide6.QtGui import QImage
from src.common.qml.stream_item.video_worker import VideoWorker

class VideoController(QObject):
    frameReady = Signal(str, 'QPixmap')  # Đổi thành signal với URL
    error = Signal(str, str)  # (url, error_message)
    
    def __init__(self):
        super().__init__()
        self._workers = {}  # Dùng URL làm key thay vì position
        self._stopping_workers = set()  # Track workers that are being stopped
        
    @Slot(str)
    def startStream(self, url: str):
        """Start streaming for a URL"""
        print(f"Starting stream for URL: {url}")
        
        # Nếu stream đã tồn tại, không cần tạo mới
        if url in self._workers:
            print(f"Stream already running for URL: {url}")
            return
            
        # Tạo và bắt đầu worker mới
        self._workers[url] = VideoWorker(url)
        self._workers[url].frame_ready.connect(lambda frame: self.frameReady.emit(url, frame))
        self._workers[url].error.connect(lambda msg: self.error.emit(url, msg))
        self._workers[url].start()
        
        print(f"Stream started for URL: {url}")
        
    @Slot(str)
    def stopStream(self, url: str):
        """Stop streaming for a URL"""
        if not url:  # Skip if URL is empty
            print("Skipping stop stream for empty URL")
            return
            
        # Check if worker is already being stopped
        if url in self._stopping_workers:
            print(f"Worker for URL {url} is already being stopped")
            return
            
        print(f"Stopping worker for URL: {url}")
        try:
            if url in self._workers:
                self._stopping_workers.add(url)  # Mark worker as being stopped
                worker = self._workers[url]
                worker.stop()
                
                # Wait with timeout and force quit if needed
                worker.force_stop()
                
                # Clean up worker tracking
                if url in self._workers:
                    del self._workers[url]
                self._stopping_workers.remove(url)  # Remove from stopping set
                    
                print(f"Worker successfully stopped for URL: {url}")
            else:
                print(f"No worker found for URL: {url}")
        except Exception as e:
            print(f"Error stopping worker: {e}")
            # Try force cleanup even if error occurs
            if url in self._workers:
                try:
                    self._workers[url].force_stop()
                    del self._workers[url]
                finally:
                    if url in self._stopping_workers:
                        self._stopping_workers.remove(url)

    def cleanup(self):
        """Stop all streams"""
        worker_urls = list(self._workers.keys())
        for url in worker_urls:
            if url not in self._stopping_workers:  # Only stop workers that aren't already being stopped
                self.stopStream(url)

    @Slot(QImage, int)
    def handle_frame(self, frame, index):
        """Xử lý frame nhận được từ worker"""
        # Gửi frame đến QML để hiển thị
        # TODO: Implement frame display logic in QML
        pass
    
    @Slot(str, int)
    def handle_error(self, error_msg, index):
        """Xử lý lỗi từ worker"""
        self.error.emit(index, error_msg)
        self.stopStream(index)

    def cleanup(self):
        """Dọn dẹp tất cả workers khi đóng ứng dụng"""
        for index in list(self._workers.keys()):
            self.stopStream(index)
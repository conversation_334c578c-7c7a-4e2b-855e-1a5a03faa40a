from src.common.widget.menus.custom_menus import MenuPosition
from src.styles.style import Style
from src.common.controller.main_controller import main_controller
from unidecode import unidecode
import re
from src.common.widget.search_widget.search_bar import SearchBar
from typing import List
from src.common.model.group_tree_model import GroupTreeModel
from src.common.model.group_model import Group
from src.common.model.camera_model import Camera,CameraModel
from src.common.widget.custom_change_mode import ChangeModeButtonSideMenu
from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QScrollArea,
    QTreeView,
    QApplication,
    QMenu,
    QStyledItemDelegate,
    QHBoxLayout
)
from PySide6.QtGui import QStandardItemModel, QStandardItem, QIcon, QAction, QDrag, QCursor, QColor, QBrush,QFont
from PySide6.QtCore import Qt, Signal, QMimeData
import logging

from src.utils.theme_setting import theme_setting

logger = logging.getLogger(__name__)


#  create enum for tree view
class TreeViewType:
    CAMERA = 0
    GROUP = 1
    HYBRID = 2
    USER_ROLE = 3
    USER = 4


class ItemType:
    CAMERA = 0
    GROUP = 1
    ITEM = 2


class ViewTypePicker:
    NONE = 0,
    PICK_PARENT_GROUP = 1
    PICK_CHILD_GROUP_OR_CAMERA = 2
    PICK_CHILD_GROUP_FOR_CAMERA = 3
    PICK_GROUP_FOR_FILTER = 4
    PICK_SUB_GROUP_FOR_FILTER = 5
    PICK_AI_ZONE_TYPE = 6
    PICK_AI_ZONE_RESULT = 7


# Tạo 1 class để lưu trữ thông tin của 1 item bao gồm group name, item name và các child item của nó
# 1 Model có thể tạo ra 1 tree view vô hạn nhánh.
# class ItemTreeModel:
#     def __init__(
#         self,
#         parent=None,
#         parent_names: List[str] = [],
#         name=None,
#         child_names: List[str] = [],
#         camera_names: List[str] = [],
#     ):
#         self.parent = parent
#         self.parent_names = parent_names
#         self.name = name
#         self.child_names = child_names
#         self.camera_names = camera_names

# Create a custom delegate to handle icon rendering
class IconDelegate(QStyledItemDelegate):
    def paint(self, painter, option, index):
        if index.column() == 0:  # Check if it's the first column (icon column)
            icon = index.data(Qt.DecorationRole)
            if icon is not None and isinstance(icon, QIcon):
                icon.paint(painter, option.rect, Qt.AlignLeft,
                           QIcon.Normal, QIcon.On)
        else:
            super().paint(painter, option, index)


class TreeViewWidget(QWidget):
    # name_item_selected_signal = Signal(str)
    # index_item_selected_signal = Signal(int)
    # item_selected_signal_model = Signal(object)
    list_items_object_after_search_signal = Signal(list)
    item_selected_signal = Signal(list)
    camera_clicked_signal = Signal(int, str)
    drag_drop_item_signal = Signal(object)
    stop_live_camera_signal = Signal(str)
    stop_live_group_signal = Signal(str)
    open_camera_position_signal = Signal(object)
    open_camera_in_tab_signal = Signal(object)
    update_zone_signal = Signal(object)
    delete_zone_signal = Signal(tuple)
    edit_zone_signal = Signal(object)

    def __init__(
        self,
        parent=None,
        list_item=[],
        list_item_non_group=[],
        label_name="",
        enable_checkbox=False,
        tree_view_type=TreeViewType.GROUP,
        view_type_picker=ViewTypePicker.NONE,
        enable_only_item_checkbox=False,
        enable_multi_item_checkbox=False,
        enable_click=True,
        hide_filter=False,
        hide_change_mode=False,
        hide_search_bar=False,
        hide_dropdown_item=False,
        style_treeview_custom=None,
        hide_icon_item=False,
        enable_dragndrop=True,
        hide_header=False
    ):
        super().__init__(parent)
        self.hide_filter = hide_filter
        self.tree_view_type = tree_view_type
        self.view_type_picker = view_type_picker
        self.list_tree_view_items = list_item
        self.list_camera_non_group = list_item_non_group
        self.list_group = []
        self.list_camera = []
        self.enable_checkbox = enable_checkbox
        self.enable_only_item_checkbox = enable_only_item_checkbox
        self.enable_multi_item_checkbox = enable_multi_item_checkbox
        self.label_name = label_name
        self.enable_click = enable_click
        self.hide_header = hide_header
        self.hide_dropdown_item = hide_dropdown_item
        self.style_treeview_custom = style_treeview_custom
        self.hide_icon_item = hide_icon_item
        self.list_current_state_checkbox = {}

        self.item_selected_list: List[QStandardItem] = []
        self.setObjectName("tree_view_widget")
        # create layout for ControlBarWidget
        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(self.layout)

        self.menu_top = QWidget()
        self.layout_top = QHBoxLayout()
        self.layout_top.setSpacing(4)
        # create search widget
        # self.filter = FilterButtonSideMenu()
        if not hide_filter:
            self.search_widget = SearchBar(parent=self)
            self.search_widget.search_items_signal.connect(self.search_items)
            self.layout_top.addWidget(self.search_widget)

        if not hide_change_mode:
            self.change_mode = ChangeModeButtonSideMenu()
            self.change_mode.change_mode_signal.connect(self.change_mode_trigger)
            self.layout_top.addWidget(self.change_mode)

        if not hide_search_bar:
            # self.layout_top.addWidget(self.filter)
            pass
        self.layout_top.setContentsMargins(0, 0, 0, 0)

        if not hide_filter or not hide_change_mode or not hide_search_bar:
            self.menu_top.setLayout(self.layout_top)
            self.layout.addWidget(self.menu_top)

        # create a sidebar left 30% width of screen
        # add scroll view to sidebar
        self.scroll = QScrollArea()
        # self.scroll_bar = self.scroll.verticalScrollBar()
        self.scroll.setWidgetResizable(True)
        self.scroll.setMaximumWidth(500)
        self.scroll.setStyleSheet(
            f'''
            QScrollArea {{ background-color: transparent; }}
            QScrollBar:vertical {{
                background-color: transparent;
            }}

            QScrollBar::handle:vertical {{
                background-color: transparent;
            }}

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background-color: transparent;
            }}
            '''
        )

        # use TreeView
        self.tree_view = QTreeView()
        self.tree_view.setAlternatingRowColors(True)
        self.style_treeview_default = f"""
                    QTreeView {{
                        background-color: {main_controller.get_theme_attribute("Color", "ai_zone_background")};
                        alternate-background-color: {main_controller.get_theme_attribute("Color", "ai_zone_background")};
                        color: {Style.PrimaryColor.white};
                    }}
                    QTreeView::item {{
                        padding: 4px; 
                        color: {Style.PrimaryColor.white};
                    }}
                    QTreeView::item::selected {{
                        background-color: {Style.PrimaryColor.on_background};
                        color: {Style.PrimaryColor.white}; 
                    }}
                    QTreeView::branch:has-children:closed {{
                        image:  url({None if self.hide_dropdown_item else Style.PrimaryImage.expand_item_treeview});
                    }}
                    QTreeView::branch:has-children:open {{
                        image: url({None if self.hide_dropdown_item else Style.PrimaryImage.collapse_item_treeview});
                    }}
                    QHeaderView::section {{ 
                        background-color: {Style.PrimaryColor.background}; 
                        color: {Style.PrimaryColor.white}; 
                    }}
            """
        self.style_treeview_disable = f"""
                    QTreeView {{
                        background-color: {main_controller.get_theme_attribute("Color", "ai_zone_background")};
                        color: {Style.PrimaryColor.text_disable} ;
                        border: None;
                    }}
                    QTreeView::item {{
                        padding: 4px;
                        background-color: {main_controller.get_theme_attribute("Color", "ai_zone_background")};
                        color: {Style.PrimaryColor.text_disable};
                        border: None;
                    }}
                    QHeaderView::section {{ 
                        background-color: {Style.PrimaryColor.background_item};
                        color: {Style.PrimaryColor.text_disable};
                    }}
                    
                    QTreeView::indicator:checked {{
                        border: none;
                        image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                        background-color: transparent;
                        width: 16px;
                        height: 16px;
                    }}
                    QTreeView::indicator:unchecked {{
                        border: none;
                        image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                        background-color: transparent;
                        width: 16px;
                        height: 16px;
                    }}
            """

        if self.style_treeview_custom is not None:
            self.tree_view.setStyleSheet(self.style_treeview_custom)
        else:
            self.tree_view.setStyleSheet(self.style_treeview_default)
        self.tree_view.setMaximumWidth(500)
        self.scroll.setWidget(self.tree_view)
        if enable_dragndrop:
            self.tree_view.setSortingEnabled(True)
            self.tree_view.setAlternatingRowColors(True)
            self.tree_view.setDragEnabled(True)
            self.tree_view.setDragDropMode(QTreeView.DragDropMode.DragOnly)
            self.tree_view.setSelectionMode(
                QTreeView.SelectionMode.SingleSelection)
            self.tree_view.setSelectionBehavior(
                QTreeView.SelectionBehavior.SelectRows)
            self.tree_view.setDragDropOverwriteMode(False)
            self.tree_view.setDropIndicatorShown(True)

        # add data to tree view
        self.model = QStandardItemModel()
        if not self.hide_header:
            self.model.setHorizontalHeaderLabels([self.label_name])
        # set header label style
        self.tree_view.header().setStyleSheet(
            f"""
                QHeaderView::section {{ 
                    background-color: {Style.PrimaryColor.background}; 
                    color: {Style.PrimaryColor.white};
                }}
            """
        )
        if self.hide_header:
            self.tree_view.header().hide()

        self.tree_view.setModel(self.model)

        if tree_view_type == TreeViewType.HYBRID or tree_view_type == TreeViewType.GROUP:
            self.create_tree_view(list_tree_view=self.list_tree_view_items,
                                  list_camera_non_group=self.list_camera_non_group)
        elif tree_view_type == TreeViewType.CAMERA:
            self.create_tree_camera_view(
                list_tree_view=self.list_tree_view_items)
        else:
            self.create_tree_view(list_tree_view=[], list_camera_non_group=[])
        self.tree_view.pressed.connect(self.on_tree_view_clicked)
        self.tree_view.clicked.connect(self.on_tree_view_clicked)
        # add widget to layout
        self.layout.addWidget(self.scroll)

    def start_drag(self, index):
        item = self.model.itemFromIndex(index)
        if item is not None and item.isDragEnabled():
            drag = QDrag(self.tree_view)
            mime_data = QMimeData()
            mime_data.setText(item.text())
            drag.setMimeData(mime_data)
            drag.exec(Qt.CopyAction)

    def search_items(self, text):
        logger.debug(f"search_items: {text} - {self.tree_view_type}")
        if self.tree_view_type == TreeViewType.HYBRID or self.tree_view_type == TreeViewType.GROUP:
            # recreate tree view each time search
            self.create_tree_view(list_tree_view=self.list_tree_view_items,
                                  list_camera_non_group=self.list_camera_non_group)
        else:
            self.create_tree_camera_view(
                list_tree_view=main_controller.list_cameras)
            
        # Convert the search text to its unaccented form
        unaccented_text = unidecode(text)

        # Filter the tree view
        self.filter_recursive(self.model.invisibleRootItem(), text, unaccented_text)
        
        logger.debug(f"self.model.rowCount(): {self.model.rowCount()}")
        # Check if there are any items left after filtering
        if self.model.rowCount() == 0:
            logger.debug("No search results found")
            # Create and add "No results found" item
            no_results_item = QStandardItem(self.tr("No search results"))
            no_results_item.setEnabled(False)
            no_results_item.setTextAlignment(Qt.AlignCenter)
            font = QFont()
            font.setPixelSize(Style.Size.body)
            no_results_item.setFont(font)
            no_results_item.setForeground(QBrush(QColor(128, 128, 128)))  # Gray color
            self.model.appendRow(no_results_item)
        
        for item_name, state in self.list_current_state_checkbox.items():
            self.update_state_item_by_name(item_name, state)

    def change_mode_trigger(self, data):
        if data == "complex_action":
            self.tree_view_type = TreeViewType.HYBRID
            self.create_tree_view(list_tree_view=main_controller.list_tree_group,
                                  list_camera_non_group=main_controller.list_camera_non_group)
        elif data == "group_list":
            self.tree_view_type = TreeViewType.GROUP
            self.create_tree_view(list_tree_view=main_controller.list_tree_group,
                                  list_camera_non_group=main_controller.list_camera_non_group)
        elif data == "camera_list":
            self.tree_view_type = TreeViewType.CAMERA
            self.create_tree_camera_view(
                list_tree_view=main_controller.list_cameras)
        else:
            pass

    def get_list_object_type_result_after_search(self):
        list_object_type_result_after_search = []
        for row in range(self.model.rowCount()):
            item = self.model.item(row)
            item_tree_model = self.get_item_tree_model(item)
            list_object_type_result_after_search.append(item_tree_model)

        # print list list_object_type_result_after_search - CHECK LOG
        for item in list_object_type_result_after_search:
            if item is Group:
                item: Group
                logger.debug(
                    f'get_list_object_type_result_after_search: {item.name}')

            elif item is Camera:
                item: Camera
                logger.debug(
                    f'get_list_object_type_result_after_search: {item.name}')

        return list_object_type_result_after_search

    def get_item_tree_model(self, item: QStandardItem):
        if self.tree_view_type == TreeViewType.GROUP:
            # search item name in list group
            for group in self.list_group:
                if group.name == item.text():
                    return group
        elif self.tree_view_type == TreeViewType.CAMERA:
            # search item name in list camera
            for camera in self.list_camera:
                if camera.name == item.text():
                    return camera
        return None

    def filter_recursive(self, item: QStandardItem, text: str, unaccented_text: str):
        logger.debug(f'filter_recursive = {text} {unaccented_text}')
        try:
            # Loop through the children of the item in reverse order
            for row in range(item.rowCount() - 1, -1, -1):
                child_item = item.child(row)

                # Check if the child item matches the search string or its unaccented form
                pattern: re.Pattern = re.compile(text, re.IGNORECASE | re.UNICODE)
                if (
                    pattern.search(child_item.text())
                    or unaccented_text.lower() in unidecode(child_item.text()).lower()
                ):
                    # If the child item matches the search string, call the recursive filter method on it
                    self.filter_recursive(child_item, text, unaccented_text)
                else:
                    # If the child item does not match the search string, remove it and all its non-matching children recursively
                    self.filter_recursive(child_item, text, unaccented_text)
                    if not self.item_has_matching_child(child_item, text, unaccented_text):
                        item.removeRow(row)
        except Exception as e:
            logger.critical(f"filter_recursive: {e}")

    def item_has_matching_child(
        self, item: QStandardItem, text: str, unaccented_text: str
    ):
        # Check if any of the item's children match the search string or its unaccented form
        for row in range(item.rowCount()):
            child_item = item.child(row)
            pattern: re.Pattern = re.compile(text, re.IGNORECASE | re.UNICODE)
            if (
                pattern.search(child_item.text())
                or unaccented_text.lower() in unidecode(child_item.text()).lower()
            ):
                return True
            elif self.item_has_matching_child(child_item, text, unaccented_text):
                return True
        return False

    def create_tree_view(self, list_tree_view: List[GroupTreeModel] = [], list_camera_non_group: List[Camera] = [], enable_data_role=True):
        # return if items_location and items_camera is empty
        self.model.removeRows(0, self.model.rowCount())
        self.root_tree_view = self.model.invisibleRootItem()
        self.create_hybrid_tree_view(
            list_trees=list_tree_view,
            root_tree_view=self.root_tree_view,
            list_camera_non_group=list_camera_non_group,
            enable_data_role=enable_data_role
        )
        self.tree_view.expandAll()

    def create_tree_camera_view(self, list_tree_view: List[CameraModel] = []):
        # return if items_location and items_camera is empty
        self.model.removeRows(0, self.model.rowCount())
        self.root_tree_view = self.model.invisibleRootItem()
        self.create_camera_tree(
            list_trees=list_tree_view,
            root_tree_view=self.root_tree_view
        )
        self.tree_view.expandAll()

    def create_hybrid_tree_view(
        self,
        list_trees: List[GroupTreeModel] = [],
        list_camera_non_group: List[Camera] = [],
        root_tree_view: QStandardItem = None,
        enable_data_role=True
    ):
        # Tạo 1 function đệ quy để add item vào tree view
        # set data from items to self.model
        # logger.debug('list_trees: ', list_trees)
        for index, item in enumerate(list_trees):
            root_item = QStandardItem(item.name)
            font = QFont()
            if enable_data_role:
                # font.setPixelSize(14)
                # root_item.setFont(font)
                root_item.setData("item_group", Qt.UserRole)
            if not self.hide_icon_item:
                font.setPixelSize(Style.Size.body)
                root_item.setFont(font)
                root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'group_camera_treeview')))
            root_item.setEditable(False)
            root_item.setCheckable(self.enable_checkbox)
            if self.enable_checkbox:
                root_item.setCheckState(
                    Qt.Unchecked if not item.checked else Qt.Checked)
            # root_item.setBackground(QColor(255, 0, 0))
            root_tree_view.appendRow(root_item)
            root_tree_view.setCheckable(self.enable_checkbox)
            if self.enable_checkbox:
                root_tree_view.setCheckState(
                    Qt.Unchecked if not item.checked else Qt.Checked)
            # root_tree_view.setBackground(QColor(255, 0, 0))
            # if not item.cameras or item.childGroups:
            self.list_group.append(item)

            if item.childGroups:
                self.create_hybrid_tree_view(
                    list_trees=item.childGroups, root_tree_view=root_item)
            if item.cameras and self.tree_view_type == TreeViewType.HYBRID:
                font = QFont()
                for camera in item.cameras:
                    child_item = QStandardItem(camera.name)
                    # child_item.setForeground(QBrush(QColor(255, 0, 255)))
                    font.setPixelSize(Style.Size.caption)
                    child_item.setFont(font)
                    if enable_data_role:
                        child_item.setData("item_camera", Qt.UserRole)
                    if not self.hide_icon_item:
                        child_item.setIcon(
                            QIcon(Style.PrimaryImage.camera_inactive_icon))
                    child_item.setCheckable(self.enable_checkbox)
                    child_item.setEditable(False)
                    root_item.appendRow(child_item)
                    if camera not in self.list_camera:
                        self.list_camera.append(camera)

        if self.tree_view_type == TreeViewType.HYBRID:
            font = QFont()
            for idx, camera_non_group in enumerate(list_camera_non_group):
                non_root_item = QStandardItem(camera_non_group.name)
                non_root_item.setFont(font)
                if enable_data_role:
                    non_root_item.setData("item_camera", Qt.UserRole)
                if not self.hide_icon_item:
                    non_root_item.setIcon(
                        QIcon(Style.PrimaryImage.camera_inactive_icon))
                non_root_item.setEditable(False)
                non_root_item.setCheckable(self.enable_checkbox)
                # non_root_item.setForeground(QBrush(QColor(255, 0, 255)))
                root_tree_view.appendRow(non_root_item)
                root_tree_view.setCheckable(self.enable_checkbox)
                if camera_non_group not in self.list_camera:
                    self.list_camera.append(camera_non_group)

    def create_camera_tree(self, list_trees: List[CameraModel] = [], root_tree_view: QStandardItem = None):
        # Tạo 1 function đệ quy để add item vào tree view
        # set data from items to self.model
        # logger.debug('list_trees: ', list_trees)
        font = QFont()
        for index, item in enumerate(list_trees):
            root_item = QStandardItem(item.data.name)
            root_item.setFont(font)
            # if enable_data_role:
            root_item.setData("item_camera", Qt.UserRole)
            
            if not self.hide_icon_item:
                root_item.setIcon(QIcon(Style.PrimaryImage.camera_inactive_icon))
            root_item.setCheckable(self.enable_checkbox)
            root_item.setEditable(False)
            root_tree_view.appendRow(root_item)
            root_tree_view.setCheckable(self.enable_checkbox)

    def is_item_in_list(self, item_name: str, list_items: List[GroupTreeModel] = []):
        for index, item in enumerate(list_items):
            if item.name == item_name:
                return True
        return False

    def on_tree_view_clicked(self, index):
        # callback index when click item
        # self.index_item_selected_signal.emit(index)
        # click child item and change color border of camera widget
        # get item from index
        item = self.model.itemFromIndex(index)
        item_name = None
        if item:
            item_name = item.text()

        if item_name:
            self.callback_camera_item(item_name)

        # if item.checkState() == Qt.CheckState.Checked:
        #     item.setCheckState(Qt.CheckState.Unchecked)
        # else:
        #     item.setCheckState(Qt.CheckState.Checked)

        # Add check box to item
        if self.enable_checkbox:
            # check item is click checkbox
            if item.isCheckable():
                # enable_only_item_checkbox
                # if enable_only_item_checkbox is True, only check item when click checkbox
                if self.enable_only_item_checkbox:
                    # unchecked all item then only check item when click checkbox not check child and parent item
                    this_item_checked: bool = False
                    if item.checkState() == Qt.Checked:
                        this_item_checked = True
                    self.unchecked_all_item()
                    # check item when click checkbox
                    # if this_item_checked:
                    #     item.setCheckState(Qt.Checked)
                    if item.checkState() == Qt.CheckState.Checked:
                        item.setCheckState(Qt.CheckState.Unchecked)
                    else:
                        item.setCheckState(Qt.CheckState.Checked)
                elif self.enable_multi_item_checkbox:
                    if item.isEnabled():
                        # hide all child and child of
                        self.enable_disable_all_child_item(
                            item=item, check=item.checkState() == Qt.Checked)
                else:
                    # if check parent item, check all child item
                    if item.checkState() == Qt.Checked:
                        self.check_all_child_item(item=item, check=True)
                    # if uncheck parent item, uncheck all child item
                    elif item.checkState() == Qt.Unchecked:
                        self.check_all_child_item(item=item, check=False)

            if self.view_type_picker == ViewTypePicker.PICK_CHILD_GROUP_FOR_CAMERA:
                # Lấy tất cả child item cuối cùng của item được tick chọn
                self.item_selected_list = self.get_lastest_list_child_item()
            elif self.view_type_picker == ViewTypePicker.PICK_CHILD_GROUP_OR_CAMERA  \
                    or self.view_type_picker == ViewTypePicker.PICK_GROUP_FOR_FILTER \
                    or self.view_type_picker == ViewTypePicker.PICK_AI_ZONE_RESULT \
                    or self.view_type_picker == ViewTypePicker.PICK_AI_ZONE_TYPE:
                # Lấy tất cả item được tick chọn
                self.item_selected_list = self.get_all_item_checked()
            else:
                # Chỉ chọn và trả về 1 item
                self.item_selected_list = []
                if item.checkState() == Qt.Checked:
                    self.item_selected_list.append(item)

            # update to self.list_current_state_checkbox
            # check item name in self.list_current_state_checkbox if exist update state
            # if not exist add item name and state to self.list_current_state_checkbox
            self.list_current_state_checkbox[item_name] = item.checkState()

            # callback item when use checkbox
            self.item_selected_signal.emit(self.item_selected_list)
        else:
            # callback item when not use checkbox
            self.item_selected_list = []
            self.item_selected_list.append(item)
            self.item_selected_signal.emit(self.item_selected_list)

        if self.enable_click:
            item_data = item.data(Qt.UserRole)
            mouse_event = QApplication.instance().mouseButtons()
            if item_data == "item_group":
                if mouse_event == Qt.MouseButton.LeftButton and self.view_type_picker != ViewTypePicker.PICK_AI_ZONE_RESULT:
                    drag = QDrag(self.tree_view)
                    mime_data = QMimeData()
                    mime_data.setText(item_name)
                    mime_data.setObjectName("item_group")
                    drag.setMimeData(mime_data)
                    drag.exec()
                elif mouse_event == Qt.RightButton:
                    mouse_position = QCursor.pos()
                    if item_name == self.tr("All"):
                        return
                    if self.view_type_picker == ViewTypePicker.PICK_AI_ZONE_RESULT:
                        self.show_menu(
                            mouse_position, item_type=ItemType.ITEM, group_name=item_name, is_zones=True)
                    else:
                        print('right click B')
                        self.show_menu(
                            mouse_position, item_type=ItemType.GROUP, group_name=item_name, camera_name="", is_zones=True)
                else:
                    pass
            elif item_data == "item_camera":
                if mouse_event == Qt.MouseButton.LeftButton:
                    drag = QDrag(self)
                    mime_data = QMimeData()
                    mime_data.setText(item_name)
                    mime_data.setObjectName("item_camera")
                    drag.setMimeData(mime_data)
                    drag.exec()
                    # logger.debug("drag camera")
                elif mouse_event == Qt.RightButton:
                    print('right click C')
                    mouse_position = QCursor.pos()
                    logger.debug(f"self.view_type_picker: {self.view_type_picker}")
                    if self.view_type_picker != ViewTypePicker.PICK_AI_ZONE_RESULT:
                        self.show_menu(
                            mouse_position, item_type=ItemType.CAMERA, group_name="", camera_name=item_name)
                else:
                    pass

        # get name parent of item
        # item_parent_view = item.parent()

        # update state checked of item to self.list_tree_view_items
        if self.list_tree_view_items and self.enable_checkbox:
            self.update_state_checked_item_to_list_tree_view_items(
                list_tree_view=self.list_tree_view_items)

    def update_state_checked_item_to_list_tree_view_items(self, list_tree_view: List[GroupTreeModel] = []):
        # update state checked of item to self.list_tree_view_items
        for index, item in enumerate(list_tree_view):
            # get item from list_tree_view_items
            item_tree_view = self.get_item_from_item_name(
                item_name=item.name, except_all_item=False)
            if item_tree_view:
                # update state checked of item
                item.checked = item_tree_view.checkState() == Qt.Checked
                self.update_zone_signal.emit(item_tree_view)
            if item.childGroups:
                self.update_state_checked_item_to_list_tree_view_items(
                    list_tree_view=item.childGroups)

    def get_all_item_checked(self):
        # get all item checked
        list_item_checked = []
        list_item = self.get_all_item()
        for index, item in enumerate(list_item):
            if item.checkState() == Qt.Checked and item.text() != self.tr("All"):
                list_item_checked.append(item)
        return list_item_checked

    def get_lastest_list_child_item(self):
        # Lấy item nếu item đó không có child item và item đó được tick chọn
        list_item: List[QStandardItem] = self.get_all_item()
        list_item_selected = []
        for index, item in enumerate(list_item):
            if item.checkState() == Qt.Checked:
                if item.hasChildren() is False:
                    # add item if item not exist in list_item_selected
                    if item not in list_item_selected:
                        list_item_selected.append(item)
        return list_item_selected

    def unchecked_all_item(self):
        # get all item in model tree view
        list_item = self.get_all_item()
        # logger.debug('unchecked_all_item: list_item', len(list_item))
        # unchecked all item
        for item in list_item:
            item.setCheckState(Qt.Unchecked)

    def get_all_item(self) -> List[QStandardItem]:
        # get all item in model tree view and child item
        list_item = []
        for index in range(self.model.rowCount()):
            item = self.model.item(index)
            list_item.append(item)
            list_item.extend(self.get_all_child_item(item=item))
        return list_item

    def check_all_child_item(self, item: QStandardItem, check: bool):
        # check all child item
        # get all child item
        list_child_item = self.get_all_child_item(item=item)

        # check all child item
        for child_item in list_child_item:
            child_item.setCheckState(Qt.Checked if check else Qt.Unchecked)

        # get parent item
        parent_item = item.parent()
        # if all child item is checked, check parent item
        # if 1 in child items is unchecked, uncheck parent item
        if parent_item:
            list_child_item = self.get_all_child_item(item=parent_item)
            is_all_checked = True
            for child_item in list_child_item:
                if child_item.checkState() == Qt.Unchecked:
                    is_all_checked = False
                    break
            parent_item.setCheckState(
                Qt.Checked if is_all_checked else Qt.Unchecked)

    def enable_disable_all_child_item(self, item: QStandardItem, check: bool):
        # check all child item
        # get all child item
        list_child_item = self.get_all_child_item(item=item)

        # disable all child item
        for child_item in list_child_item:
            # if check is True, disable child item else enable child item
            child_item.setEnabled(not check)
            child_item.setCheckState(Qt.Unchecked)
            # disable child item
            self.enable_disable_all_child_item(item=child_item, check=check)

    def get_all_child_item(self, item: QStandardItem) -> List[QStandardItem]:
        # get all child item
        list_child_item = []
        for index in range(item.rowCount()):
            child_item = item.child(index)
            list_child_item.append(child_item)
            list_child_item += self.get_all_child_item(item=child_item)
        return list_child_item

    def update_tree_view_camera(self, list_camera: List[CameraModel] = []):
        # logger.debug(f'self.tree_view_type = {self.tree_view_type}')
        if not self.tree_view_type == TreeViewType.CAMERA:
            logger.debug(
                "tree view type is not camera, need set tree view type to camera when init tree view")
            return
        if len(list_camera) == 0:
            if self.model.rowCount() > 0:
                for row in range(self.model.rowCount() - 1, -1, -1):
                    self.model.removeRow(row)
        else:
            self.list_camera = list_camera
            self.create_tree_camera_view(list_tree_view=list_camera)

    def update_tree_view_hybrid(self, list_trees: List[GroupTreeModel] = [],
                                list_camera_non_group: List[Camera] = [],
                                list_camera: List[Camera] = [],
                                enable_data_role=True):
        if self.tree_view_type == TreeViewType.HYBRID or self.tree_view_type == TreeViewType.GROUP:
            # Thêm logic remove để khi clear hết camera sẽ không cập nhật treeview, khi các list camera và group rỗng
            # thì remove treeview
            if self.tree_view_type == TreeViewType.GROUP and len(list_trees) == 0 \
                    or self.tree_view_type == TreeViewType.HYBRID and len(list_trees) == 0 and len(list_camera_non_group) == 0:
                if self.model.rowCount() > 0:
                    for row in range(self.model.rowCount() - 1, -1, -1):
                        self.model.removeRow(row)
            if list_trees or list_camera_non_group:
                self.list_tree_view_items = list_trees
                self.list_camera_non_group = list_camera_non_group
                self.create_tree_view(list_tree_view=list_trees, list_camera_non_group=list_camera_non_group,
                                      enable_data_role=enable_data_role)
        else:
            if len(list_camera) == 0:
                if self.model.rowCount() > 0:
                    for row in range(self.model.rowCount() - 1, -1, -1):
                        self.model.removeRow(row)
            else:
                self.list_camera = list_camera
                self.create_tree_camera_view(list_tree_view=list_camera)

    # Hàm để call back khi click vào item của tree view -> và trả về đúng loại model item
    def callback_object_item(self, item_name):
        model = None
        if self.tree_view_type == TreeViewType.CAMERA:
            model = self.callback_camera_item(item_name)
        elif self.tree_view_type == TreeViewType.GROUP:
            model = self.callback_group_item(item_name)

        # self.item_selected_signal_model.emit(model)

    def callback_camera_item(self, item_name) -> Camera:
        # get camera widget from item name
        return self.get_camera_widget_from_item_name(item_name)

    def get_camera_widget_from_item_name(self, item_name: str) -> Camera:
        # get camera widget from item name
        for index, camera in enumerate(self.list_camera):
            if camera.data.name == item_name:
                self.camera_clicked_signal.emit(camera.data.id, camera.data.name)
                return camera.data
        return None

    def callback_group_item(self, item_name) -> Group:
        # get group widget from item name
        return self.get_group_widget_from_item_name(item_name)

    def get_group_widget_from_item_name(self, item_name: str) -> Group:
        # get group widget from item name
        for index, group in enumerate(self.list_group):
            if group.name == item_name:
                return group
        return None

    def setLabelName(self, label):
        self.label_name = label
        if not self.hide_header:
            self.model.setHorizontalHeaderLabels([self.label_name])
        if not self.hide_filter:
            self.search_widget.retranslateUi_searchbar()

    def show_menu(self, position, item_type, group_name="", camera_name="", is_zones = False):
        self.main_menu = QMenu()
        # self.sub_menu = SubMenuOpenInTab()
        # self.sub_menu.open_in_tab_signal.connect(self.get_tab_to_open)
        # self.sub_menu.open_new_tab_signal.connect(self.get_tab_to_open)
        if item_type == ItemType.CAMERA:
            self.sub_menu_position = MenuPosition(camera_name, is_zones=is_zones)
            self.sub_menu_position.row_col_signal.connect(self.get_rc_position)
            self.exit_stream_camera = QAction(icon=QIcon(Style.PrimaryImage.exit_stream_treeview),
                                              text=self.tr("Exit Streaming Camera ")+f"{camera_name}", parent=self.main_menu)
            self.exit_stream_camera.triggered.connect(
                lambda: (self.stop_live_camera_signal.emit(camera_name)))
            self.choose_position = QAction(icon=QIcon(Style.PrimaryImage.choose_position_icon),
                                           text=self.tr("Choose position to stream on grid"), parent=self.main_menu)
            self.choose_position.setMenu(self.sub_menu_position)
            # self.open_in_tab = QAction(icon=QIcon(Style.PrimaryImage.open_in_tab), text=self.tr("Open in tab"),
            #                            parent=self.sub_menu)
            # self.open_in_tab.setMenu(self.sub_menu)
            self.main_menu.addAction(self.exit_stream_camera)
            self.main_menu.addAction(self.choose_position)
            # self.main_menu.addAction(self.open_in_tab)
        elif item_type == ItemType.GROUP:
            self.exit_stream_group = QAction(icon=QIcon(Style.PrimaryImage.exit_stream_treeview),
                                             text=self.tr("Exit Streaming Group ")+f"{group_name}", parent=self.main_menu)
            self.exit_stream_group.triggered.connect(
                lambda: self.stop_live_group_signal.emit(group_name))
            self.main_menu.addAction(self.exit_stream_group)
            pass
        elif item_type == ItemType.ITEM:
            self.edit_item_action = QAction(icon=QIcon(Style.PrimaryImage.edit_zones),
                                            text=self.tr("Edit"), parent=self.main_menu)
            self.delete_item_action = QAction(icon=QIcon(Style.PrimaryImage.trash),
                                              text=self.tr("Delete"), parent=self.main_menu)
            self.edit_item_action.triggered.connect(
                lambda: self.edit_item(item_name=group_name))
            self.delete_item_action.triggered.connect(
                lambda: self.delete_item(item_name=group_name))
            self.main_menu.addAction(self.edit_item_action)
            self.main_menu.addAction(self.delete_item_action)
            pass
        self.main_menu.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.main_menu.setAttribute(
            Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.main_menu.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        self.main_menu.exec_(position)

    def get_rc_position(self, data):
        self.open_camera_position_signal.emit(data)

    def get_tab_to_open(self, data):
        self.open_camera_in_tab_signal.emit(data)

    def delete_item(self, item_name):
        # find_item_by_name
        item = self.find_item_by_name(item_name=item_name)
        if item:
            
            def callback_delete_item():
                # remove item
                item.parent().removeRow(item.row())
                # remove item from list
                self.remove_item_from_list(item_name=item_name)
            data = (item_name, callback_delete_item)
            self.delete_zone_signal.emit(data)

    def edit_item(self, item_name):
        # item = self.find_item_by_name(item_name=item_name)
        # if item:
        self.edit_zone_signal.emit(item_name)

    def remove_item_from_list(self, item_name):
        # remove item from list
        for index, item in enumerate(self.list_tree_view_items):
            if item.name == item_name:
                self.list_tree_view_items.pop(index)
                break

    def find_item_by_name(self, item_name):
        # get item from item name
        item = self.get_item_from_item_name(item_name=item_name)
        if item:
            return item
        else:
            return None

    def get_item_from_item_name(self, item_name, except_all_item=True):
        # get item from item name
        for index in range(self.model.rowCount()):
            item = self.model.item(index)
            # Nếu item có name là all thì tìm child item và check name của child item trùng với item_name thì return child item
            if item.text() == self.tr("All") and except_all_item:
                # get child
                for child_index in range(item.rowCount()):
                    child_item = item.child(child_index)
                    if child_item.text() == item_name:
                        return child_item
            else:
                if item.text() == item_name:
                    return item
                else:
                    # get child
                    for child_index in range(item.rowCount()):
                        child_item = item.child(child_index)
                        if child_item.text() == item_name:
                            return child_item
        return None

    def disable_item_by_name(self, item_name):
        # find_item_by_name
        item = self.find_item_by_name(item_name=item_name)
        if item:
            # disable item
            item.setEnabled(False)
            # set color gray for item
            item.setForeground(QBrush(QColor(128, 128, 128)))

    def hide_checkbox_by_name(self, item_name):
        # find_item_by_name
        item: QStandardItem = self.find_item_by_name(item_name=item_name)
        logger.debug("hide_checkbox_by_name: item: ", item)
        if item:
            # get name item
            item_name = item.text()
            # hide checkbox
            item.setCheckable(False)
            # Notify the view about changes
            self.model.dataChanged.emit(item.index(), item.index())

    def show_checkbox_by_name(self, item_name):
        # find_item_by_name
        item: QStandardItem = self.find_item_by_name(item_name=item_name)
        if item:
            # get name item
            item_name = item.text()
            # hide checkbox
            item.setCheckable(True)
            # Notify the view about changes
            self.model.dataChanged.emit(item.index(), item.index())

    def get_current_check_item(self) -> QStandardItem:
        # get current check item
        list_item = self.get_all_item()
        for item in list_item:
            if item.checkState() == Qt.Checked and item.text() != self.tr("All"):
                return item
        return None

    def set_current_check_item(self, item_name):
        # set current check item
        item = self.find_item_by_name(item_name=item_name)
        if item:
            item.setCheckState(Qt.Checked)
            # Notify the view about changes
            self.model.dataChanged.emit(item.index(), item.index())

    def setEnabledCustom(self, enable):
        # set opacity of view
        self.tree_view.setEnabled(enable)
        # set style sheet for view
        if enable:
            if self.style_treeview_custom:
                self.tree_view.setStyleSheet(self.style_treeview_custom)
            else:
                self.tree_view.setStyleSheet(self.style_treeview_default)
        else:
            self.tree_view.setStyleSheet(self.style_treeview_disable)

    # update state of item in tree view through name of item
    def update_state_item_by_name(self, item_name, state):
        # find_item_by_name
        item = self.find_item_by_name(item_name=item_name)
        if item:
            # update state of item
            if state:
                item.setCheckState(Qt.Checked)
            else:
                item.setCheckState(Qt.Unchecked)
            
            # set to self.list_current_state_checkbox
            self.list_current_state_checkbox[item_name] = state
            # Notify the view about changes
            self.model.dataChanged.emit(item.index(), item.index())


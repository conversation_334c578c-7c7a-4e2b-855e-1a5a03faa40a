import QtQuick 
import QtQuick.Controls 
import QtQuick.Layouts

Canvas {
    id: root
    parent: _hoverArea
    anchors.fill: parent
    property alias iconItem: icon

    property var camData: null
    property var _hoverArea: null
    property var _mapState: null
    property var _item: null
    property var cameraId: ""
    // Tính posCoord dựa theo _hoverArea và icon.size
    property var posCoord: {
        if (!camData || !camData.fovData) return { x: 0, y: 0 };
        try {
            var parsed = JSON.parse(camData.fovData);
            var position = parsed.position || "0.5,0.5";
            var parts = position.split(",");
            if (parts.length < 2) return { x: 0, y: 0 };
            var relX = parseFloat(parts[0]);
            var relY = parseFloat(parts[1]);
            if (isNaN(relX) || isNaN(relY)) return { x: 0, y: 0 };

            return {
                x: relX * _hoverArea.width - icon.width / 2,
                y: relY * _hoverArea.height - icon.height / 2
            };
        } catch(e) {
            return { x: 0, y: 0 };
        }
    }

    // property real iconSize:{

    // }

    property int iconType: {
        var parsed = camData && camData.fovData ? JSON.parse(camData.fovData) : {};
        return (parsed.icon_type !== undefined) ? parsed.icon_type : 0;
    }

    function updateCamData(prop, value) {
        var newData = Object.assign({}, camData)
        newData[prop] = value
        camData = newData
        camDataUpdated(camData)
    }

    signal clicked()
    signal camDataUpdated(var newData)
    property bool isHovered: false

    property real rawRadius: {
        var ratio = (_hoverArea ? _hoverArea.scaleRatio : 1);
        if (!camData || !camData.fovData || !JSON.parse(camData.fovData).radius) return 80 / ratio;
        return JSON.parse(camData.fovData).radius;
    }

    property real radius: rawRadius * _hoverArea.scaleRatio
    
    property var startAngle: {
        if (!camData) return -45;
        var startAngle = JSON.parse(camData.fovData).arc_start_angle;
        return startAngle
    }

    property var spanAngle: {
        if (!camData) return 90;
        var spanAngle = JSON.parse(camData.fovData).arc_range_angle;
        return spanAngle
    }
    
    property var controlSize: 10
    property var prevRadius: 0

    function angleDifference(a, b) {
        var diff = a - b;
        while (diff < -180) diff += 360;
        while (diff > 180) diff -= 360;
        return diff;
    }

    // Hàm chuyển tọa độ theo góc và bán kính,
    // sử dụng trung tâm được tính lại từ posCoord (vị trí icon)
    function polarToCanvas(angle, r) {
        var centerX = posCoord.x + icon.width / 2;
        var centerY = posCoord.y + icon.height / 2;
        var rad = angle * Math.PI / 180;
        return Qt.point(centerX + r * Math.cos(rad),
                        centerY + r * Math.sin(rad));
    }

    function midAngle() {
        return startAngle + spanAngle / 2;
    }

    Item{
        id: icon
        visible: _item.isImageReady
        width: _hoverArea.width / 20 + 15 * (camData ? camData.size : 1) - 20
        height: _hoverArea.width / 20 + 15 * (camData ? camData.size : 1) - 20
        x: posCoord.x
        y: posCoord.y

        Rectangle{
            id: iconContainer
            anchors.fill: parent
            border.width: 5 * Math.min(_hoverArea.scaleRatio, 1)
            border.color: "#EEEEEE"
            color: camData.color
            radius: width/2
            Image {
                anchors.centerIn: parent
                width: iconContainer.width * 0.6
                height: iconContainer.height * 0.6
                source: "qrc:/src/assets/map/iconCamera" + (iconType + 1) +".svg"
                fillMode: Image.PreserveAspectFit
                sourceSize: Qt.size(width, height)
            }
        }
        
        MouseArea {
            id: hoverDetector
            anchors.fill: parent
            hoverEnabled: true
            onEntered: {
                root.isHovered = true
            }

            onExited: {
                root.isHovered = false
            }
        }

        MouseArea {
            enabled: _mapState ? _mapState.editMode : false
            anchors.fill: parent
            drag.target: parent
            drag.minimumX: 0
            drag.maximumX: _hoverArea.width - icon.width
            drag.minimumY: 0
            drag.maximumY: _hoverArea.height - icon.height

            onReleased: {
                var newRelX = (icon.x + icon.width / 2) / _hoverArea.width;
                var newRelY = (icon.y + icon.height / 2) / _hoverArea.height;
                var newFovData = JSON.parse(camData.fovData);
                newFovData['position'] = `${newRelX},${newRelY}`;
                newFovData = JSON.stringify(newFovData);
                updateCamData('fovData', newFovData);
            }

            onPositionChanged: {
                posCoord = { x: icon.x, y: icon.y };
                requestPaint();
            }

            onClicked: {
                root.clicked()
            }
        }
        
        ToolTip{
            visible: hoverDetector.containsMouse && camData.nameEnable
            contentItem: Text {
                text: camData ? camData.name : ""
                color: "white"
                font.pixelSize: 2 * camData.size + 10
            }

            background: Rectangle {
                color: "#181824"
                radius: 5
            }
        }
    }

    onPaint: {
        var ctx = getContext("2d");
        ctx.clearRect(0, 0, width, height);
        if (camData && camData.fovEnable === false) {
            ctx.clearRect(0, 0, width, height);
            return;
        }

        // Tính trung tâm dựa trên posCoord của icon
        var centerX = posCoord.x + icon.width / 2;
        var centerY = posCoord.y + icon.height / 2;
        var startRad = startAngle * Math.PI / 180;
        var endRad = (startAngle + spanAngle) * Math.PI / 180;

        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, startRad, endRad, false);
        ctx.closePath();

        var baseColor = Qt.rgba(0, 0.5, 1, 0.4); // fallback
        if (camData && camData.color) {
            try {
                var tmpColor = Qt.color(camData.color);
                baseColor = `rgba(${Math.round(tmpColor.r * 255)}, ${Math.round(tmpColor.g * 255)}, ${Math.round(tmpColor.b * 255)}, 0.4)`;
            } catch(e) {
                console.warn("Invalid camData.color:", camData.color);
            }
        }
        ctx.fillStyle = baseColor;
        ctx.fill();

        ctx.strokeStyle = camData ? camData.color : "blue";
        ctx.lineWidth = 2;
        ctx.stroke();
    }

    Component.onCompleted: requestPaint();
    onStartAngleChanged: requestPaint()
    onSpanAngleChanged: requestPaint()
    onRadiusChanged: requestPaint()
    onPosCoordChanged: requestPaint()

    // ===== START HANDLE =====
    Rectangle {
        id: startHandle
        width: root.controlSize
        height: root.controlSize
        color: "white"
        border.color: camData ? camData.color : "blue"
        radius: root.controlSize / 2
        visible: _mapState && camData ? _mapState.editMode && camData.fovEnable : false

        // Vị trí tính dựa trên polarToCanvas (sử dụng center tính từ posCoord)
        x: root.polarToCanvas(root.startAngle, root.radius).x - width/2
        y: root.polarToCanvas(root.startAngle, root.radius).y - height/2

        MouseArea {
            anchors.fill: parent
            // Không sử dụng drag.target để tránh phá vỡ binding của x, y
            property real initialAngle: 0

            onPressed: (mouse) => {
                // Sử dụng trung tâm tính dựa trên posCoord của icon
                var center = Qt.point(root.posCoord.x + icon.width/2, root.posCoord.y + icon.height/2);
                var posInCanvas = mapToItem(root, mouse.x, mouse.y);
                var dx = posInCanvas.x - center.x;
                var dy = posInCanvas.y - center.y;
                initialAngle = Math.atan2(dy, dx) * 180 / Math.PI;
            }

            onPositionChanged: (mouse) => {
                var center = Qt.point(root.posCoord.x + icon.width/2, root.posCoord.y + icon.height/2);
                var posInCanvas = mapToItem(root, mouse.x, mouse.y);
                var dx = posInCanvas.x - center.x;
                var dy = posInCanvas.y - center.y;
                var newAngle = Math.atan2(dy, dx) * 180 / Math.PI;
                var currentMid = root.midAngle();

                // Tính hiệu số góc chính xác
                var diff = root.angleDifference(newAngle, currentMid);
                // Với startHandle, newAngle không được vượt qua currentMid (phải <=)
                if(diff > 0)
                    newAngle = currentMid;

                var halfSpan = Math.abs(root.angleDifference(newAngle, currentMid));
                if(root.spanAngle === 180 && halfSpan >= 90)
                    return;

                if(2 * halfSpan < 10)
                    halfSpan = 5;
                else if(2 * halfSpan > 180)
                    halfSpan = 90;

                root.startAngle = currentMid - halfSpan;
                root.spanAngle = 2 * halfSpan;
            }

            onReleased: () => {
                var newFovData = JSON.parse(camData.fovData);
                newFovData['arc_start_angle'] = root.startAngle;
                newFovData['arc_range_angle'] = root.spanAngle;
                newFovData = JSON.stringify(newFovData);
                updateCamData('fovData', newFovData);
            }
        }
    }

    // ===== END HANDLE =====
    Rectangle {
        id: endHandle
        width: root.controlSize
        height: root.controlSize
        color: "white"
        border.color: camData ? camData.color : "blue"
        radius: root.controlSize / 2
        visible: _mapState && camData ? _mapState.editMode && camData.fovEnable : false

        x: root.polarToCanvas(root.startAngle + root.spanAngle, root.radius).x - width/2
        y: root.polarToCanvas(root.startAngle + root.spanAngle, root.radius).y - height/2

        MouseArea {
            anchors.fill: parent
            property real initialAngle: 0

            onPressed: (mouse) => {
                var center = Qt.point(root.posCoord.x + icon.width/2, root.posCoord.y + icon.height/2);
                var posInCanvas = mapToItem(root, mouse.x, mouse.y);
                var dx = posInCanvas.x - center.x;
                var dy = posInCanvas.y - center.y;
                initialAngle = Math.atan2(dy, dx) * 180 / Math.PI;
            }

            onPositionChanged: (mouse) => {
                var center = Qt.point(root.posCoord.x + icon.width/2, root.posCoord.y + icon.height/2);
                var posInCanvas = mapToItem(root, mouse.x, mouse.y);
                var dx = posInCanvas.x - center.x;
                var dy = posInCanvas.y - center.y;
                var newAngle = Math.atan2(dy, dx) * 180 / Math.PI;
                var currentMid = root.midAngle();

                // Với endHandle, newAngle không được nhỏ hơn currentMid
                var diff = root.angleDifference(newAngle, currentMid);
                if(diff < 0)
                    newAngle = currentMid;

                var halfSpan = Math.abs(root.angleDifference(newAngle, currentMid));
                if(root.spanAngle === 180 && halfSpan >= 90)
                    return;

                if(2 * halfSpan < 10)
                    halfSpan = 5;
                else if(2 * halfSpan > 180)
                    halfSpan = 90;

                root.startAngle = currentMid - halfSpan;
                root.spanAngle = 2 * halfSpan;
            }

            onReleased: () => {
                var newFovData = JSON.parse(camData.fovData);
                newFovData['arc_start_angle'] = root.startAngle;
                newFovData['arc_range_angle'] = root.spanAngle;
                newFovData = JSON.stringify(newFovData);
                updateCamData('fovData', newFovData);
            }

        }
    }

    // ===== ROTATION HANDLE =====
    Rectangle {
        id: rotationHandle
        width: root.controlSize
        height: root.controlSize
        color: "white"
        border.color: camData ? camData.color : "blue"
        radius: root.controlSize / 2
        visible: _mapState && camData ? _mapState.editMode && camData.fovEnable : false

        x: root.polarToCanvas(root.midAngle(), root.radius + 20).x - width/2
        y: root.polarToCanvas(root.midAngle(), root.radius + 20).y - height/2

        MouseArea {
            anchors.fill: parent
            property real initialAngle: 0
            property real initialStartAngle: 0

            onPressed: (mouse) => {
                var center = Qt.point(root.posCoord.x + icon.width/2, root.posCoord.y + icon.height/2);
                var posInCanvas = mapToItem(root, mouse.x, mouse.y);
                initialAngle = Math.atan2(posInCanvas.y - center.y, posInCanvas.x - center.x) * 180 / Math.PI;
                initialStartAngle = root.startAngle;
            }
            onPositionChanged: (mouse) => {
                var center = Qt.point(root.posCoord.x + icon.width/2, root.posCoord.y + icon.height/2);
                var posInCanvas = mapToItem(root, mouse.x, mouse.y);
                var newAngle = Math.atan2(posInCanvas.y - center.y, posInCanvas.x - center.x) * 180 / Math.PI;
                var delta = newAngle - initialAngle;
                root.startAngle = initialStartAngle + delta;
            }

            onReleased: () => {
                var newFovData = JSON.parse(camData.fovData);
                newFovData['arc_start_angle'] = root.startAngle;
                newFovData = JSON.stringify(newFovData);
                updateCamData('fovData', newFovData);
            }
        }
    }

    // ===== MID HANDLE =====
    Rectangle {
        id: midHandle
        width: root.controlSize
        height: root.controlSize
        color: "white"
        border.color: camData ? camData.color : "blue"
        radius: root.controlSize / 2
        visible: _mapState && camData ? _mapState.editMode && camData.fovEnable : false

        x: root.polarToCanvas(root.midAngle(), root.radius).x - width/2
        y: root.polarToCanvas(root.midAngle(), root.radius).y - height/2

        MouseArea {
            anchors.fill: parent
            onPositionChanged: (mouse) => {
                var center = Qt.point(root.posCoord.x + icon.width/2, root.posCoord.y + icon.height/2);
                var posInCanvas = mapToItem(root, mouse.x, mouse.y);
                var dx = posInCanvas.x - center.x;
                var dy = posInCanvas.y - center.y;
                var newRadius = Math.sqrt(dx*dx + dy*dy);
                var raw = newRadius / _hoverArea.scaleRatio;
                if (raw < 10)
                    raw = 10;

                root.rawRadius = raw;
            }

            onReleased: () => {
                var newFovData = JSON.parse(camData.fovData);
                newFovData['radius'] = root.rawRadius;
                newFovData = JSON.stringify(newFovData);
                updateCamData('fovData', newFovData);
            }
        }
    }
}

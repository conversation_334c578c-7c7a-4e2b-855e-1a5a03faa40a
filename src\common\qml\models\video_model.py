from PySide6.QtQuick import QQuickPaintedItem
from PySide6.QtCore import Property, Signal, Slot, Qt,QObject,QEvent
from PySide6.QtQml import QJSValue
from PySide6.QtGui import QPainter, QPixmap
from src.common.camera.video_capture import <PERSON>CameraT<PERSON>,VideoCapture,CameraState,video_capture_controller
from src.common.controller.controller_manager import controller_manager,Controller
from src.common.model.camera_model import Camera, CameraModel, camera_model_manager
import uuid
import logging
logger = logging.getLogger(__name__)

class VideoModel(QQuickPaintedItem):
    clicked = Signal()
    doubleClicked = Signal()
    rightClicked = Signal()
    frameCountChanged = Signal(int)
    cameraStateChanged = Signal(str)
    visibilityChanged = Signal(bool)  # New signal for visibility changes
    def __init__(self, parent=None):
        super().__init__(parent)
        self.uuid = uuid.uuid4()
        self._model = None
        self._pixmap = QPixmap()
        self.video_capture = None
        self.controller = None
        # self.register_video_capture()
        self._position = -1
        self._is_playing = False
        self._frame_count = 0
        self._state = self.tr("No Data")
        self._is_actually_visible = False  # Track actual visibility state
        # Enable mouse events
        self.setAcceptedMouseButtons(Qt.LeftButton | Qt.RightButton)
        self.setAcceptHoverEvents(True)
        # Install event filter for visibility tracking
        self.installEventFilter(self)

    def eventFilter(self, obj, event):
        if obj == self:
            if event.type() in [QEvent.Show, QEvent.Hide, QEvent.WindowStateChange]:
                self.check_visibility()
        return super().eventFilter(obj, event)

    def check_visibility(self):
        """Check if widget is actually visible and emit signal if changed"""
        new_visibility = self.is_actually_visible()
        if new_visibility != self._is_actually_visible:
            self._is_actually_visible = new_visibility
            self.visibilityChanged.emit(new_visibility)

    def get_is_actually_visible(self):
        """Get current visibility state"""
        return self._is_actually_visible

    def is_actually_visible(self):
        """Check if widget is actually visible to user"""
        # 1. Check if widget itself is visible
        if not self.isVisible():
            return False
            
        # 2. Check if visible to parent
        if not self.isVisibleTo(self.parentWidget()):
            return False
            
        # 3. Check if window is minimized
        if self.window().windowState() & Qt.WindowMinimized:
            return False
            
        # # 4. Check if in stacked widget and is current widget
        # if isinstance(self.parentWidget(), QStackedWidget):
        #     stacked_widget = self.parentWidget()
        #     if stacked_widget.currentWidget() != self:
        #         return False
                
        return True

    def showEvent(self, event):
        super().showEvent(event)
        self.check_visibility()

    def hideEvent(self, event):
        super().hideEvent(event)
        self.check_visibility()

    def changeEvent(self, event):
        super().changeEvent(event)
        if event.type() == QEvent.WindowStateChange:
            self.check_visibility()

    @Slot("QVariant")
    def register_video_capture(self,model):
        if model is not None:
            if isinstance(model,CameraModel):
                self.controller:Controller = controller_manager.get_controller(server_ip=model.data.server_ip)
                video_capture_controller.register_video_capture(self,model,StreamCameraType.main_stream)
            elif isinstance(model, QJSValue):
                camera_id = model.toVariant().get("id")
                camera_model = camera_model_manager.get_camera_model(id = camera_id)
                if camera_model is not None:
                    self.controller:Controller = controller_manager.get_controller(server_ip=camera_model.data.server_ip)
                    video_capture_controller.register_video_capture(self,camera_model,StreamCameraType.main_stream)
            elif isinstance(model, str):
                camera_model = camera_model_manager.get_camera_model(id = model)
                if camera_model is not None:
                    self.controller:Controller = controller_manager.get_controller(server_ip=camera_model.data.server_ip)
                    video_capture_controller.register_video_capture(self,camera_model,StreamCameraType.sub_stream)
    @Slot()
    def unregister_video_capture(self):
        video_capture_controller.unregister_video_capture(self)
        logger.debug(f'unregister_video_capture')     

    def paint(self, painter: QPainter):
        if self._pixmap.isNull():
            return
            
        # Scale pixmap to fit while maintaining aspect ratio
        target_rect = self.boundingRect()
        scaled_pixmap = self._pixmap.scaled(
            int(target_rect.width()),
            int(target_rect.height()),
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )
        
        # Center the pixmap
        x = (target_rect.width() - scaled_pixmap.width()) / 2
        y = (target_rect.height() - scaled_pixmap.height()) / 2
        painter.drawPixmap(x, y, scaled_pixmap)

    @Slot(QPixmap)
    def updateFrame(self, pixmap):
        self._pixmap = pixmap
        self._frame_count += 1
        self.frameCountChanged.emit(self._frame_count)
        self.update()

    def mousePressEvent(self, event):
        if event.button() == Qt.RightButton:
            self.rightClicked.emit()
        else:
            self.clicked.emit()

    def mouseDoubleClickEvent(self, event):
        self.doubleClicked.emit()

    @Property(int)
    def position(self):
        return self._position

    @position.setter
    def position(self, pos):
        print(f"CustomVideo position changed from {self._position} to {pos}")
        self._position = pos

    @Property(str,notify=cameraStateChanged)
    def state(self):
        return self._state
    
    @state.setter
    def state(self, value:str):
        if self._state != value:
            self._state = value
            self.cameraStateChanged.emit(value)


    # @Property(bool,notify=viewModeChanged)
    # def viewMode(self):
    #     return self._viewMode
    
    # @viewMode.setter
    # def viewMode(self, value: bool):
    #     if self._viewMode != value:
    #         self._viewMode = value
    #         self.viewModeChanged.emit()

    @Property("QVariant")
    def model(self):
        return self._model
    
    @model.setter
    def model(self, value):
        if self._model != value:
            self._model = value

    @Property(bool)
    def isPlaying(self):
        return self._is_playing

    @isPlaying.setter
    def isPlaying(self, playing):
        if self._is_playing != playing:
            self._is_playing = playing
            if not playing:
                self._pixmap = QPixmap()
                self._frame_count = 0
                self.update()

    @Property(int, notify=frameCountChanged)
    def frameCount(self):
        return self._frame_count
    
    def process_video_capture(self,video_capture:VideoCapture):
        self.video_capture = video_capture
        self.video_capture.connect_status = True
        # self.video_capture.set_send_mat_frame(self.use_post_process_image and self.callback_post_process_image is not None)
        self.video_capture.register_signal(self)
        self.video_capture.update_resize(width = 1280, height = 720,uuid = self.uuid)
        logger.debug(f'video_capture ========== START = {self.video_capture.stream_type}')
        if not self.video_capture.isRunning():
            logger.debug(f'video_capture ========== START')
            if self.video_capture.stream_type != StreamCameraType.video_stream:
                self.video_capture.start_thread()

        def update_stream_url(reponse, streamIndex):
            if self.video_capture is not None:
                # [
                #     {
                #         "url": "https://implacable-ai.ai-vlab.com/camera/fb2c9da1-8ccf-4ade-9745-b7733d236c5e_0.flv",
                #         "index": 0,
                #         "state": "CONNECTED"
                #     },
                #     {
                #         "url": "https://implacable-ai.ai-vlab.com/camera/fb2c9da1-8ccf-4ade-9745-b7733d236c5e_1.flv",
                #         "index": 1,
                #         "state": "CONNECTED"
                #     }
                # ]
                # Giải thích logic:
                # - Nếu streamIndex trùng với index của item và state là CONNECTED thì lấy url của item đó
                # - Nếu không thì lấy url của item có state là CONNECTED
                data = reponse.json()
                logger.debug(f'update_stream_url data = {data}')
                # Lọc chỉ các luồng có trạng thái CONNECTED or None
                connected_streams = [item for item in data if item["state"] == "CONNECTED" or item["state"] is None]
                
                if connected_streams:
                    # Đầu tiên tìm luồng phù hợp với chỉ số được yêu cầu
                    target_stream = next((stream for stream in connected_streams if stream["index"] == streamIndex), None)
                    
                    # Nếu không tìm thấy luồng phù hợp với chỉ số, sử dụng luồng kết nối đầu tiên có sẵn
                    if target_stream is None:
                        target_stream = connected_streams[0]
                        
                    self.video_capture.on_stream_link_changed(target_stream["url"])
                    logger.debug(f'update_stream_url: {target_stream["url"]}')
                else:
                    logger.debug(f'update_stream_url: Không có url nào được lấy')
                        
        self.controller.get_stream_url_thread(cameraId=self.video_capture.camera_model.data.id, streamIndex=0, callback=update_stream_url)

    def share_frame_signal(self, data):
        grab, mat_frame_default, pixmap_resized = data
        # if self.use_post_process_image and self.callback_post_process_image is not None:
        #     self.callback_post_process_image(mat_frame_default)
        if grab and pixmap_resized is not None and self.get_is_actually_visible():
            # self.camera_state_signal(CameraState.started)
            pixmap_resized: QPixmap
            mat_frame_default: QPixmap
            h, w = pixmap_resized.height(), pixmap_resized.width()
            # self.set_widget_size([self.root_width, self.root_height, w, h])
            # self.updateFrame(pixmap_resized)
            try:
                self.updateFrame(pixmap_resized)
            except RuntimeError:
                logger.debug("RuntimeError")

    def camera_state_signal(self, camera_state):
        # logger.debug(f'camera_state_signal = {camera_state}')
        if camera_state == CameraState.connecting:
            self.state = "connecting"
        elif camera_state == CameraState.stopped:
            self.state = "stopped"
        elif camera_state == CameraState.started:
            self.state = "started"
        elif camera_state == CameraState.paused:
            self.state = "paused"

        # logger.debug(f'camera_state_signal = {self.state}')
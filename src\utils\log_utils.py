import os
import logging.config
import tempfile
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime
import sys
import platform

def create_dir_if_not_exists(directory: str):
    print(f"Creating directory {directory} if it does not exist")
    if not os.path.exists(directory):
        os.makedirs(directory)

# Set up logging directory
temp_dir = tempfile.gettempdir()
logs_dir = os.path.join(temp_dir, "GPSLogs")
print(f"logs_dir: {logs_dir}")
create_dir_if_not_exists(logs_dir)

# Log file path
log_file = os.path.join(logs_dir, f"log_{datetime.now().strftime('%Y-%m-%d')}.txt")

# Logging configuration
logging_schema = {
    "version": 1,
    "formatters": {
        "standard": {
            "class": "logging.Formatter",
            "format": "%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s",
        },
        "detailed": {
            "class": "logging.Formatter",
            "format": "%(asctime)s - %(levelname)s - %(name)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s",
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "standard",
            "level": "INFO",
            "stream": sys.stdout,
        },
        "file": {
            "class": "logging.handlers.TimedRotatingFileHandler",
            "formatter": "detailed",
            "level": "INFO",
            "filename": log_file,
            "when": "midnight",  # Rotate at midnight
            "backupCount": 4,
            "encoding": "utf-8"  # Ensure UTF-8 encoding for log file
        }
    },
    "root": {
        "level": "INFO",
        "handlers": ["console", "file"],
        "propagate": False
    }
}

logging.config.dictConfig(logging_schema)

# Get the root logger
logger = logging.getLogger()

# Function to log application initialization
def log_app_init(app_name, version, platform_info=None):
    """
    Log application initialization information
    
    Args:
        app_name: Name of the application
        version: Version of the application
        platform_info: Optional dictionary with platform information
    """
    if platform_info is None:
        platform_info = {
            "os": platform.system(),
            "os_version": platform.version(),
            "python_version": platform.python_version(),
            "machine": platform.machine(),
            "processor": platform.processor()
        }
    
    logger.debug(f"=== {app_name} v{version} Initialization ===")
    logger.debug(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.debug(f"OS: {platform_info['os']} {platform_info['os_version']}")
    logger.debug(f"Python: {platform_info['python_version']}")
    logger.debug(f"Machine: {platform_info['machine']}")
    logger.debug(f"Processor: {platform_info['processor']}")
    logger.debug(f"Log file: {log_file}")
    logger.debug("=" * 50)

# Function to log unhandled exceptions
def log_exception(exc_type, exc_value, exc_traceback):
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    logger.critical("Unhandled exception", exc_info=(exc_type, exc_value, exc_traceback))

# Set the exception hook to log unhandled exceptions
sys.excepthook = log_exception

# Test logging with UTF-8 characters
# logger.debug("This is a test log message with special characters: ệ, ü, é, ñ, å")

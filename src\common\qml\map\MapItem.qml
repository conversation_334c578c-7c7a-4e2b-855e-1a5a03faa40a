import QtQuick
import QtQuick.Controls.Material 
import QtQuick.Layouts
import QtPositioning
import QtLocation
import CustomComponents 1.0
MapQuickItem {
    id: root
    property var model
    property var itemType: "Camera"
    property var rtsp: ""
    property var itemId: ""
    property var itemName: ""
    property var camLocation: ""
    property int iconSize: 50
    signal untrackSignal(var model)
    signal buttonSignal(var model)

    anchorPoint.x: width / 2
    anchorPoint.y: height / 2
    z: 1
    
    onCoordinateChanged: {
        if (mapModel.isPositionChanged(itemId)) {
            cameraDetailLoader.active = true
        }
    }

    sourceItem: Item {
        id: cameraItem
        objectName: "Camera"
        width: 60
        height: 60
        RoundButton {
            id: idButton
            implicitWidth: iconSize
            implicitHeight: iconSize
            contentItem: Item {
                width: idButton.implicitWidth
                height: idButton.implicitHeight

                Image {
                    id: img
                    anchors.fill: parent
                    fillMode: Image.PreserveAspectFit
                    sourceSize: Qt.size(idButton.implicitWidth, idButton.implicitHeight)
                    source: itemType === "Camera"
                            ? (model.state === "CONNECTED"
                            ? "qrc:/src/assets/map/camera_on_map_on.svg"
                            : "qrc:/src/assets/map/camera_on_map_off.svg")
                            : "qrc:/src/assets/camera_stream/building_item.svg"
                }

                Rectangle {
                    visible: itemType === "Camera"
                    width: 8; height: 8
                    radius: width/2
                    anchors {
                        right: parent.right; bottom: parent.bottom
                        margins: 2
                    }
                    color: model && model.state === "CONNECTED" ? "lime" : "tomato"
                }
            }

            onClicked: function() {
                if(mapState.lockMode){
                    return
                }

                if (itemType !== "Camera"){
                    console.log("idButton ",model.floorIds)
                } 
                buttonSignal(model)
            }

            antialiasing: true
            anchors.centerIn: parent

            ToolTip{
                id: idToolTip
                text: itemName
                delay: 300
                visible: idButton.hovered
                contentItem: Text {
                    text: "• " + qsTr("Name:") + " " + itemName + 
                            (camLocation ? "\n• " + qsTr("Location:") + " " + camLocation : "")
                    color: "white"
                    wrapMode: Text.Wrap
                    font.pixelSize: 12
                    width: parent.width
                }

                background: Rectangle {
                    color: "#181824"
                    radius: 5
                }

            }
        }
        Menu {
            id: contextMenu
            MenuItem {
                action: Action {
                    id: untrackAction
                    text: itemType === "Camera" ? qsTr("Remove Camera from Map") : qsTr("Remove Building from Map")
                    onTriggered: function(){
                        untrackSignal(model)
                    }
                }
            }
        }
        Loader {
            id: cameraDetailLoader
            active: mapModel ? mapModel.isPositionChanged(itemId) : false
            z: 999
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.bottom  // Đặt dialog bên dưới icon
            anchors.topMargin: 8
            sourceComponent: ConfirmDialog{
                onConfirmSignal: function(confirmed){
                    mapModel.handleConfirmLocation(itemId, confirmed)
                    cameraDetailLoader.active = false
                }
            }
        }
        MouseArea {
            anchors.fill: parent
            acceptedButtons: Qt.RightButton
            onClicked: {
                if (mapState.editMode) {
                    contextMenu.popup()
                }
            }
        }

        DragHandler {
            id: dragHandler
            enabled: (mapState && !mapState.lockMode) ? true : false
            target: (mapState && mapState.editMode) ? root : null
        }

        Drag.source: cameraItem
        Drag.active: dragHandler.active

        Drag.mimeData: {
            "text/plain": itemName,
            "application/data": JSON.stringify({
                id: itemId,
                tree_type: "Camera"
            })
        }
        Drag.dragType: Drag.Automatic
        Drag.supportedActions: Qt.MoveAction
    }

}

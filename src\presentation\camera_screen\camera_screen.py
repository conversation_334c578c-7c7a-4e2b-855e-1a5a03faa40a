import logging
from PySide6.QtCore import QPoint, <PERSON><PERSON>imer, Qt, QEvent,Signal, QUrl, QObject, QThreadPool
from PySide6.QtPositioning import QGeoCoordinate
from src.common.widget.camera_widget import CameraWidget
from src.common.model.item_grid_model import ItemGridModel
from src.common.widget.button_status_sidebar import ButtonStatusSidebar
from src.common.widget.custom_tab_widget.new_custom_tab_widget import NewCustomTabWidget
from src.common.widget.custom_titlebar.actionable_title_bar import ActionableTitleBar
from src.common.widget.custom_titlebar.custom_component.widget_button_system import WidgetButtonSystem
from src.common.widget.custom_titlebar.custom_component.widget_search_title import WidgetSearchTitle
from src.common.widget.notifications.notify import Notifications
from src.presentation.camera_screen.camera_bottom_toolbar import <PERSON>BottomToolbarWidget
from src.presentation.camera_screen.tracking_camera_grid_widget import TrackingCameraGridWidget
from src.common.controller.main_controller import main_controller, connect_slot
from src.common.model.event_data_model import EventAI
from src.styles.style import Style, Theme
from src.utils.config import Config
from src.common.widget.event_bar import EventBar
from PySide6.QtGui import QGuiApplication, QIcon, QColor,QCursor
from src.presentation.device_management_screen.widget.list_custom_widgets import LabelWidget
from src.presentation.camera_screen.camera_grid_widget import CameraGridWidget
from src.presentation.camera_screen.calendar_dialog import CalendarDialog
from src.presentation.camera_screen.main_tree_view_widget import MainTreeViewWidget
from src.common.model.camera_model import CameraModel,camera_model_manager
from src.common.model.group_model import group_model_manager, GroupModel
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QApplication,QStyle, QSizePolicy, QSplitter, \
    QFrame, QStackedWidget, QPushButton,QMenu
from src.common.model.device_models import TabType
from src.common.model.main_tree_view_model import TreeType,Status
from src.common.widget.dialogs.camera_info_dialog import CameraInfoDialog
from src.common.camera.grid_item_selected import grid_item_selected
from src.common.model.tab_model import TabModel,tab_model_manager,GridItem,ItemType,Tab,SignalType
from src.common.model.event_data_model import event_manager
from src.common.widget.button_state import GridButtonModel,original_list_data_grid
from src.common.controller.controller_manager import Controller,controller_manager
import random
from src.common.model.record_model import Record,RecordModel,record_model_manager
from src.common.key_board.key_board_manager import KeyPressFilter
from src.common.key_board.shortcut_key import shortcut_key_model_manager
from src.common.joystick.joystick_manager import joystick_manager,WidgetType
from pyjoystick.sdl2 import Key
from src.utils.utils import Utils
from src.common.key_board.key_board_manager import key_board_manager
import time
from queue import Queue
from threading import Thread
from PySide6.QtQuickWidgets import QQuickWidget
from PySide6.QtQml import qmlRegisterType
from src.common.qml.models.ruler_context import RulerContext
from src.common.qml.models.timestep2 import TimeStep2
from src.common.qml.models.durationpresent import DurationPresent
from src.common.qml.models.timelinecontroller import TimeLineController,SpeedStatus,TimeLineManager
from src.common.controller.controller_manager import Controller, controller_manager
from src.common.qml.models.map_controller import floor_manager
from src.common.model.screen_model import ScreenModel, screenModelManager
import uuid
import logging
# src/presentation/camera_screen/map/workers/map_cam_search.py
from src.presentation.camera_screen.map.workers.map_cam_search import sortByEuclideanDistance
logger = logging.getLogger(__name__)

class WorkerSignal(QObject):
    updateCameraListSignal = Signal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)

class EventProcessor(Thread):
    def __init__(self, callback = None):
        super().__init__()
        self.callback = callback
        self.event_queue = Queue()
        self.is_running = True
        self.daemon = True

        self.signal_worker = WorkerSignal()
        self.signal_worker.updateCameraListSignal.connect(self.updateCameraList)
        self.MAX_CAMERA_COUNT = 6
        self.MAX_PADDING_COUNT = 80
        self.routeList = []
        self.thread_pool = QThreadPool()
        # Limit concurrent requests
        self.thread_pool.setMaxThreadCount(3)

    def run(self):
        while self.is_running:
            try:
                event_id = self.event_queue.get()
                if event_id is None:
                    break

                event: EventAI = event_manager.get_event(id=event_id)
                # Cấu hình hiển thị thông báo lên camera stream ở đây
                # camera_model = camera_model_manager.get_camera_model(id=event.cameraId)
                cam_id = event.cameraId
                current_camera = camera_model_manager.get_camera_model(id=cam_id)
                if current_camera.data.coordinateLong is not None and current_camera.data.coordinateLat is not None:
                    current_camera_lon = float(current_camera.data.coordinateLong)
                    current_camera_lat = float(current_camera.data.coordinateLat)
                    current_camera_coordinate = QGeoCoordinate(current_camera_lat, current_camera_lon)
                    logger.debug(f'run: current_camera.data.serverUrl: {current_camera.data.server_ip}')
                    sorted_cam_list = sortByEuclideanDistance(cam_id, current_camera_coordinate, server_url=current_camera.data.server_ip)
                    self.callback((event_id, sorted_cam_list[:self.MAX_CAMERA_COUNT]))
                # start_time = time.time()
                # try:
                #     iterating_cam_list = sorted_cam_list[:self.MAX_CAMERA_COUNT + self.MAX_PADDING_COUNT]
                #     for cameraDistanceItem in iterating_cam_list:
                #         if not self.is_running:
                #             break
                #         worker = RouteThreadWorker(cameraDistanceItem["camera"].data, current_camera_coordinate, self.signal_worker.updateCameraListSignal)
                #         self.thread_pool.start(worker)

                #     self.thread_pool.waitForDone()
                #     end_time = time.time()
                #     print(f"Tổng thời gian xử lý route: {(end_time - start_time):.2f} giây")
                #     if self.is_running:
                #         self.routeList.sort(key=lambda x: x.get('distance', float('inf')))
                #         print("final result: ", self.routeList[:self.MAX_CAMERA_COUNT] , " with current camera: ", current_camera.data.name, "and coordinate: ", current_camera_coordinate)
                #         self.callback((event_id,self.routeList[:self.MAX_CAMERA_COUNT]))


                # except Exception as e:
                #     print(f"Error in route thread: {e}")
                #     end_time = time.time()
                #     print(f"Tổng thời gian xử lý route lỗi: {(end_time - start_time):.2f} giây")

                # # Xử lý event
                # if self.callback is not None:
                #     self.callback(event_id)
                # time.sleep(3)
                # Đánh dấu task đã hoàn thành

                time.sleep(2)
                self.event_queue.task_done()
            except Exception as e:
                logger.error(f"Error processing event: {e}")
        
    def stop(self):
        self.is_running = False
        self.event_queue.put(None)

    def updateCameraList(self, routeData):
        if self.is_running:
            self.routeList.append(routeData)

class CameraScreen(QWidget):
    update_ui_signal = Signal(tuple)
    def __init__(self, parent=None, window_parent=None):
        super(CameraScreen, self).__init__(parent)
        self.mouse_pressed = False
        self.splitter = None
        self.is_sidebar_visible = True
        self.is_eventbar_visible = True
        self.parent = parent
        self.window_parent = window_parent
        self.list_screens = QApplication.screens()
        self.tracing_screen_index = None
        self.tab_model_tracking = None
        self.toggle_timeline_button = None
        self.list_tracing_camera = []
        self.record_data = None
        self.calculate_layout()
        self.create_controller()
        main_controller.list_parent['CameraScreen'] = self
        self.calendar_dialog = None
        self.setup_ui()
        self.connect_slot()
        self.event_processor = EventProcessor(callback=self.process_event)
        self.event_processor.start()
        self.restyle_camera_screen()
        if main_controller.key_filter is None:
            main_controller.key_filter = KeyPressFilter()
        QApplication.instance().installEventFilter(main_controller.key_filter)
        joystick_manager.register(WidgetType.CameraScreen,self.key_received_signal)
        
    def connect_slot(self):
        connect_slot(
            (main_controller.complete_fetching_data,self.complete_fetching_data),
            (event_manager.add_event_signal,self.add_event_signal),
            (record_model_manager.add_records_signal,self.add_records_signal),
            (main_controller.open_map_in_tab_signal,self.open_map_in_tab),
            (main_controller.open_floor_in_tab_signal,self.open_floor_in_tab_signal),
            (main_controller.open_camera_in_tab_signal,self.open_camera_in_tab_from_map),
            (main_controller.camera_clicked_signal,self.handle_color_preview),
            (main_controller.stop_live_camera_signal,self.stop_live_handle),
            (main_controller.stop_live_group_signal,self.stop_live_group_handle),
            (main_controller.open_camera_position_signal,self.open_camera_in_position),
            (main_controller.open_map_position_signal,self.open_map_in_position),
            (shortcut_key_model_manager.add_shortcut_key_list_signal, self.add_shortcut_key_list_signal),
            (shortcut_key_model_manager.notification_signal, self.notification_signal),
            (main_controller.edit_floor_signal,self.edit_floor_signal),
            (main_controller.edit_map_signal,self.edit_map_handle),
            (self.update_ui_signal,self.update_ui_slot),
            (grid_item_selected.widget_changed,self.grid_item_changed),
            )

    def setup_ui(self):
        self.max_width_sidebar_default = int(self.screen_available_width * 0.16)
        self.min_width_sidebar_default = int(self.screen_available_width * 0.1)
        self.init_min_width_sidebar = int(self.screen_available_width * 0.08)
        self.min_height_timeline = int(self.screen_available_height * 0.12)
        self.main_treeview_widget = MainTreeViewWidget(parent=self)
        self.filter_text = LabelWidget(icon_clicked=self.icon_clicked)
        self.filter_text.hide()
        self.side_bar_widget = QWidget()
        self.side_bar_widget.setObjectName("side_bar_widget")
        self.side_bar_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.side_bar_layout = QVBoxLayout()
        self.side_bar_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.side_bar_layout.setContentsMargins(4, 4, 4, 0)
        self.side_bar_layout.setSpacing(0)
        self.widget_search_title_bar = WidgetSearchTitle(parent=self, window_parent=self.window_parent)
        self.widget_search_title_bar.search_widget.search_items_signal.connect(self.search_items)
        self.widget_search_title_bar.change_mode.change_mode_signal.connect(self.change_mode_trigger)
        self.side_bar_layout.addWidget(self.widget_search_title_bar)
        self.side_bar_layout.addWidget(self.filter_text)
        self.side_bar_layout.addWidget(self.main_treeview_widget)
        self.side_bar_widget.setLayout(self.side_bar_layout)
        self.new_custom_tab_widget = NewCustomTabWidget(parent=self, window_parent=self.window_parent)
        self.new_custom_tab_widget.callback_current_changed = self.callback_tab_changed
        self.new_custom_tab_widget.signal_add_tab_widget.connect(
            self.add_tab_widget)
        self.create_title_bar()
        self.center_view_widget = QWidget()
        self.center_view_widget.setObjectName("center_view_widget")
        self.center_view_layout = QHBoxLayout()
        self.center_view_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.center_view_layout.setSpacing(0)
        self.center_view_layout.setContentsMargins(0, 2, 0, 2)
        self.center_view_widget.setLayout(self.center_view_layout)
        self.center_view_widget.setSizePolicy(
            QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.center_stacked_widget = QStackedWidget()
        self.center_view_layout.addWidget(self.center_stacked_widget)
        self.load_tab_widget()
        if Config.ENABLE_EVENT_BAR:
            self.widget_event_bar = QWidget()
            self.layout_event_bar = QVBoxLayout()
            self.layout_event_bar.setContentsMargins(0, 0, 0, 0)
            self.event_bar = EventBar(parent=self)
            self.event_bar.setObjectName("event_bar")
            self.event_bar.setSizePolicy(
                QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
            self.widget_event_bar.setMaximumWidth(self.max_width_sidebar_default)
            self.widget_event_bar.setMinimumWidth(self.init_min_width_sidebar)
            self.layout_event_bar.addWidget(self.event_bar)
            self.widget_event_bar.setLayout(self.layout_event_bar)

            self.event_bar.combobox_hover_signal.connect(self.combobox_hover)
        self.splitter = QSplitter()
        self.splitter.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.splitter.setChildrenCollapsible(True)
        self.splitter.splitterMoved.connect(self.handle_splitter_moved)
        self.splitter.setStyleSheet(f"QSplitter::handle {{ background-color: {main_controller.get_theme_attribute('Color', 'main_background_splitter')}; }}")
        self.splitter.setContentsMargins(0, 0, 0, 0)
        self.splitter.setHandleWidth(10)
        self.side_bar_widget.setMaximumWidth(self.max_width_sidebar_default)
        self.side_bar_widget.setMinimumWidth(self.init_min_width_sidebar)
        self.splitter.addWidget(self.side_bar_widget)
        self.splitter.addWidget(self.center_view_widget)
        if Config.ENABLE_EVENT_BAR:
            self.splitter.addWidget(self.widget_event_bar)
        self.splitter.setStretchFactor(0, 1)  # 20%
        self.splitter.setStretchFactor(1, 8)
        if Config.ENABLE_EVENT_BAR:
            self.splitter.setStretchFactor(2, 1)
        handle_sidebar = self.splitter.handle(1)
        handle_eventbar = self.splitter.handle(2)
        self.toggle_sidebar_button = ButtonStatusSidebar(handle_sidebar, init_svg_path=main_controller.get_theme_attribute('Image', 'icon_status_sidebar'))
        self.toggle_sidebar_button.installEventFilter(self)
        self.toggle_sidebar_button.setObjectName("toggle_sidebar_button")
        self.toggle_sidebar_button.clicked.connect(lambda: self.toggle_sidebar(True))
        layout_status_active_sidebar = QVBoxLayout()
        layout_status_active_sidebar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_status_active_sidebar.setContentsMargins(0, 0, 0, 0)
        layout_status_active_sidebar.addWidget(self.toggle_sidebar_button)
        self.toggle_eventbar_button = ButtonStatusSidebar(handle_eventbar, main_controller.get_theme_attribute('Image', 'icon_status_sidebar'))
        self.toggle_eventbar_button.installEventFilter(self)
        self.toggle_eventbar_button.setObjectName("toggle_eventbar_button")
        self.toggle_eventbar_button.clicked.connect(lambda: self.toggle_event_bar(True))
        layout_status_active_eventbar = QVBoxLayout()
        layout_status_active_eventbar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_status_active_eventbar.setContentsMargins(0, 0, 0, 0)
        layout_status_active_eventbar.addWidget(self.toggle_eventbar_button)
        handle_sidebar.setLayout(layout_status_active_sidebar)
        handle_eventbar.setLayout(layout_status_active_eventbar)
        self.main_splitter = QSplitter(self)
        self.main_splitter.setOrientation(Qt.Orientation.Vertical)
        self.main_splitter.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.main_splitter.setChildrenCollapsible(True)
        self.main_splitter.splitterMoved.connect(self.handle_splitter_moved)
        self.main_splitter.setStyleSheet(
            f"""QSplitter::handle {{ 
                            background-color: {main_controller.get_theme_attribute('Color', 'main_background')};
                            border-top: 1px solid {main_controller.get_theme_attribute('Color', 'main_border')};
                            border-bottom: 1px solid {main_controller.get_theme_attribute('Color', 'main_border')};
                        }}"""
        )
        self.main_splitter.setContentsMargins(0, 0, 0, 0)
        self.main_splitter.setHandleWidth(10)
        self.main_splitter.addWidget(self.splitter)
        self.timelinecontrol_qml = self.create_timeline_widget()
        self.main_splitter.addWidget(self.timelinecontrol_qml)
        self.main_splitter.setStretchFactor(0, 70)
        self.main_splitter.setStretchFactor(1, 30)
        self.timelinecontrol_qml.setMaximumHeight(self.min_height_timeline + 50)
        self.timelinecontrol_qml.setMinimumHeight(self.min_height_timeline)
        handle_timeline = self.main_splitter.handle(1)
        image = main_controller.get_theme_attribute('Image', 'icon_status_timeline')
        self.toggle_timeline_button = ButtonStatusSidebar(handle_timeline, image)
        self.toggle_timeline_button.setFixedSize(20,10)
        self.toggle_timeline_button.installEventFilter(self)
        self.toggle_timeline_button.setObjectName("toggle_timeline_button")
        self.toggle_timeline_button.clicked.connect(lambda: self.toggle_timeline(False))
        layout_status_active_timeline = QVBoxLayout()
        layout_status_active_timeline.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_status_active_timeline.setContentsMargins(0, 0, 0, 0)
        layout_status_active_timeline.addWidget(self.toggle_timeline_button)
        handle_timeline.setLayout(layout_status_active_timeline)
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        margin_size = 0
        self.main_layout.setContentsMargins(
            margin_size, margin_size, margin_size, margin_size)
        self.main_layout.setSpacing(margin_size)
        self.main_layout.addWidget(self.widget_title_bar)
        self.main_layout.addWidget(self.main_splitter)
        self.setLayout(self.main_layout)
        self.showEvent = self.on_show_event
        self.resizeEvent = self.on_resize_event
        sizes = self.main_splitter.sizes()
        if Utils.is_windows():
            sizes[1] = 0
        self.main_splitter.setSizes(sizes)

    def handle_joystick_button_press(self,key):
        key_event = joystick_manager.get_key_event(key)
        logger.debug(f'handle_joystick_button_press = {key.keytype} {key.number} {key.value} {key_event} {key_event}')
        if key_event == Qt.Key.Key_Minus:
            self.func_zoom_out(key)
        elif key_event == Qt.Key.Key_Equal:
            self.func_zoom_in(key)
        else:
            # Xử lý các sự kiện phím bấm khác trên Joystick
            # Tạm thời chỉ nhận sự kiến ấn Phím từ Joystick, bỏ qua sự kiện nhả phím trước
            if key.value == 1:
                if int(key_event) in shortcut_key_model_manager.shortcut_key_list:
                    key_board_manager.keys.clear()
                    key_board_manager.keys[str(key_event)] = {'key_list': []}
                elif str(key_event) in key_board_manager.shortcut_keys:
                    if key_event == Qt.Key.Key_Alt:
                        key_board_manager.stop_timer_list()
                        key_board_manager.keys.clear()
                        key_board_manager.keys[str(key_event)] = {'screen_number': [],'number_camera':[],'screen_selected': None}
                        # show number man hinh chinh
                        custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                        widget = custom_tab_widget.getCurrentWidget()
                        widget.show_screen_index(0)
                        widget.show_item_index()
                        ########################
                        key_board_manager.keys[str(key_event)][0] = custom_tab_widget
                        screen_list = key_board_manager.shortcut_keys[str(Qt.Key.Key_Alt)]['func_start_key']()
                        if len(screen_list) > 0:
                            for index,screen in enumerate(screen_list):
                                key_board_manager.keys[str(key_event)][index + 1] = screen
                                virtual_window_widget = screen[1]
                                virtual_window_widget.show_screen_index(index + 1)
                                virtual_window_widget.show_item_index()     
                else:
                    start_key = key_board_manager.get_start_key()
                    if start_key is not None:
                        if start_key == str(Qt.Key.Key_Slash) or start_key == str(Qt.Key.Key_Asterisk):
                            if key_event != Qt.Key.Key_Return and key_event != Qt.Key.Key_Enter:
                                for idx,value in key_board_manager.keys.items():
                                    value['key_list'].append(key_event)
                                    break
                            else:
                                for idx,value in key_board_manager.keys.items():
                                    if len(value['key_list']) > 0:
                                        number = key_board_manager.keys_to_number(number_list=value['key_list'])
                                        ok = shortcut_key_model_manager.run_func(start_key=int(idx),id = number)
                                        if not ok:
                                            shortcut_key_model_manager.notification_signal.emit("This ShortcutID does not exist.")  
                                        break
                                key_board_manager.keys = {}
                        elif start_key == str(Qt.Key.Key_Alt):
                            if key_event != Qt.Key.Key_Return and key_event != Qt.Key.Key_Enter:
                                for idx,value in key_board_manager.keys.items():
                                    if key_board_manager.is_number(key=key_event):
                                        # Chỉ xử lý key là ký tự number
                                        value['screen_number'].append(key_event)
                                        number = key_board_manager.keys_to_number(number_list=value['screen_number'])
                                        if number in key_board_manager.keys[start_key] and value['screen_selected'] is None:
                                            value['screen_selected'] = number
                                            # tim thấy số màn hình mà nguoi dùng đã chọn
                                            if number == 0:
                                                custom_tab_widget = key_board_manager.keys[start_key][0]
                                                widget = custom_tab_widget.getCurrentWidget()
                                                widget.change_screen_index_color()
                                                widget_selected = grid_item_selected.data['widget']
                                                if widget_selected is not None:
                                                    # bỏ focus camera item trước đó
                                                    if hasattr(widget_selected.stack_item, 'grid_item_unclicked'):
                                                        widget_selected.stack_item.grid_item_unclicked()
                                            else:
                                                virtual_window = key_board_manager.keys[start_key][number]
                                                screen_index = virtual_window[0]
                                                virtual_window_widget = virtual_window[1]
                                                virtual_window_widget.change_screen_index_color()
                                                widget_selected = grid_item_selected.data['widget']
                                                if widget_selected is not None:
                                                    # bỏ focus camera item trước đó
                                                    if hasattr(widget_selected.stack_item, 'grid_item_unclicked'):
                                                        widget_selected.stack_item.grid_item_unclicked()
                                        else:
                                            # Trường hợp ko tìm thấy mình hình thì check xem phím tắt trước đã tìm thấy chưa 
                                            if value['screen_selected'] is not None:
                                                # Tiếp tục nhận key để tìm camera item trong grid
                                                if value['screen_selected'] == 0:
                                                    value['number_camera'].append(key_event)
                                                    number = key_board_manager.keys_to_number(number_list=value['number_camera'])
                                                    screen_widget = key_board_manager.keys[start_key][value['screen_selected']]
                                                    widget = screen_widget.getCurrentWidget()
                                                    widget.find_item_index(number)
                                                else:
                                                    # man hinh virtual
                                                    value['number_camera'].append(key_event)
                                                    number = key_board_manager.keys_to_number(number_list=value['number_camera'])
                                                    vitual_window_widget = key_board_manager.keys[start_key][value['screen_selected']]
                                                    vitual_window_widget[1].find_item_index(number)
                                        break
                    else:
                        
                        widget_selected = grid_item_selected.data['widget']
                        screen = grid_item_selected.data['screen']
                        if widget_selected is not None:
                            if key_event == Qt.Key.Key_6:
                                screen =  grid_item_selected.data['screen']
                                type = grid_item_selected.data['type']
                                if screen == 'Main':
                                    custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                                    widget = custom_tab_widget.getCurrentWidget()
                                    widget.process_change_item(action = 6)
                                elif screen is not None and screen in main_controller.list_parent:
                                    camera_grid_base = main_controller.list_parent[screen][1]
                                    camera_grid_base.process_change_item(action = 6)
                            elif key_event == Qt.Key.Key_4:
                                screen =  grid_item_selected.data['screen']
                                type = grid_item_selected.data['type']
                                if screen == 'Main':
                                    custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                                    widget = custom_tab_widget.getCurrentWidget()
                                    widget.process_change_item(action = 4)
                                elif screen is not None and screen in main_controller.list_parent:
                                    camera_grid_base = main_controller.list_parent[screen][1]
                                    camera_grid_base.process_change_item(action = 4)
                            elif key_event == Qt.Key.Key_2:
                                screen =  grid_item_selected.data['screen']
                                type = grid_item_selected.data['type']
                                if screen == 'Main':
                                    custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                                    widget = custom_tab_widget.getCurrentWidget()
                                    widget.process_change_item(action = 2)
                                elif screen is not None and screen in main_controller.list_parent:
                                    camera_grid_base = main_controller.list_parent[screen][1]
                                    camera_grid_base.process_change_item(action = 2)
                            elif key_event == Qt.Key.Key_8:
                                screen =  grid_item_selected.data['screen']
                                type = grid_item_selected.data['type']
                                if screen == 'Main':
                                    custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                                    widget = custom_tab_widget.getCurrentWidget()
                                    widget.process_change_item(action = 8)
                                elif screen is not None and screen in main_controller.list_parent:
                                    camera_grid_base = main_controller.list_parent[screen][1]
                                    camera_grid_base.process_change_item(action = 8)


    def key_received_signal(self,key):
        logger.debug(f'key_received_signal = {key.keytype} {key.number} {key.value}')
        try:
            if joystick_manager.widget_type == WidgetType.CameraScreen:
                camera_widget:CameraWidget = grid_item_selected.data.get('widget', None)
                if key.keytype == Key.BUTTON:
                    self.handle_joystick_button_press(key)
                elif key.keytype == Key.AXIS:
                    if camera_widget is not None:
                        if key.value < 0.01 and key.value > -0.01:
                            self.key_pressed = False
                            if key.number == 2:
                                if camera_widget.camera_model.data.type == "AVIGILON":
                                    data = {
                                        "cameraId": camera_widget.camera_model.data.id,
                                        "endPoint": "/camera/commands/pan-tilt-zoom",
                                        "requestData": {
                                            "continuous": {
                                                "panAmount": 0,
                                                "tiltAmount": 0,
                                                "zoomAmount": 0,
                                                "action": "STOP"
                                                }
                                        }
                                    }
                                    camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                                else:
                                    camera_widget.current_joystick_msg = {"type": 'button_zoom','speed': 0}
                                    if not camera_widget.process_joystick_queue_thread.isRunning():
                                        camera_widget.process_joystick_queue_thread.start()
                            elif key.number == 0 or key.number == 1:
                                if camera_widget.camera_model.data.ptzCap is not None and len(camera_widget.camera_model.data.ptzCap) >0:
                                    if camera_widget.camera_model.data.type == "AVIGILON":
                                        data = {
                                            "cameraId": camera_widget.camera_model.data.id,
                                            "endPoint": "/camera/commands/pan-tilt-zoom",
                                            "requestData": {
                                                "continuous": {
                                                    "panAmount": 0,
                                                    "tiltAmount": 0,
                                                    "zoomAmount": 0,
                                                    "action": "STOP"
                                                    }
                                            }
                                        }
                                        camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                                    else:
                                        camera_widget.current_joystick_msg = {"type": 'axis',"x": 0,"y": 0}
                                        if not camera_widget.process_joystick_queue_thread.isRunning():
                                            camera_widget.process_joystick_queue_thread.start()
                                    
                                
                        else:
                            if key.number == 0:
                                if camera_widget.camera_model.data.ptzCap is not None and len(camera_widget.camera_model.data.ptzCap) >0:
                                    if camera_widget.camera_model.data.type == "AVIGILON":
                                        data = {
                                            "cameraId": camera_widget.camera_model.data.id,
                                            "endPoint": "/camera/commands/pan-tilt-zoom",
                                            "requestData": {
                                                "continuous": {
                                                    "panAmount": key.value,
                                                    "tiltAmount": 0,
                                                    "zoomAmount": 0,
                                                    "action": "START"
                                                    }
                                            }
                                        }
                                        camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                                    else:
                                        camera_widget.current_joystick_msg = {"type": 'axis',"x": key.value,"y": 0}
                                        if not camera_widget.process_joystick_queue_thread.isRunning():
                                            camera_widget.process_joystick_queue_thread.start()
                            elif key.number == 1:
                                if camera_widget.camera_model.data.ptzCap is not None and len(camera_widget.camera_model.data.ptzCap) >0:
                                    y = -key.value
                                    if camera_widget.camera_model.data.type == "AVIGILON":
                                        data = {
                                            "cameraId": camera_widget.camera_model.data.id,
                                            "endPoint": "/camera/commands/pan-tilt-zoom",
                                            "requestData": {
                                                "continuous": {
                                                    "panAmount": 0,
                                                    "tiltAmount": -y,
                                                    "zoomAmount": 0,
                                                    "action": "START"
                                                    }
                                            }
                                        }
                                        camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                                    else:
                                        camera_widget.current_joystick_msg = {"type": 'axis',"x": 0,"y": y}
                                        if not camera_widget.process_joystick_queue_thread.isRunning():
                                            camera_widget.process_joystick_queue_thread.start()
                            elif key.number == 2:
                                    if camera_widget.camera_model.data.ptzCap is not None and len(camera_widget.camera_model.data.ptzCap) >0:
                                        if camera_widget.camera_model.data.type == "AVIGILON":
                                            data = {
                                                "cameraId": camera_widget.camera_model.data.id,
                                                "endPoint": "/camera/commands/pan-tilt-zoom",
                                                "requestData": {
                                                    "continuous": {
                                                        "panAmount": 0,
                                                        "tiltAmount": 0,
                                                        "zoomAmount": key.value,
                                                        "action": "START"
                                                        }
                                                }
                                            }
                                            camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                                        else:
                                            camera_widget.current_joystick_msg = {"type": 'button_zoom','speed': key.value}
                                            if not camera_widget.process_joystick_queue_thread.isRunning():
                                                camera_widget.process_joystick_queue_thread.start()
        
        except Exception as e:
            logger.debug(f'key_received_signal error: {e}')

    def func_zoom_in(self,key):
        camera_widget:CameraWidget = grid_item_selected.data.get('widget', None)
        if camera_widget is not None:
            logger.debug(f'func_zoom_in')
            if camera_widget.camera_model.data.ptzCap is not None and len(camera_widget.camera_model.data.ptzCap) >0:
                if camera_widget.camera_model.data.type == "AVIGILON":
                    data = {
                        "cameraId": camera_widget.camera_model.data.id,
                        "endPoint": "/camera/commands/pan-tilt-zoom",
                        "requestData": {
                            "continuous": {
                                "panAmount": 0,
                                "tiltAmount": 0,
                                "zoomAmount": key.value,
                                "action": "START"
                                }
                        }
                    }
                    camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                else:
                    camera_widget.current_joystick_msg = {"type": 'button_zoom','speed': key.value}
                    if not camera_widget.process_joystick_queue_thread.isRunning():
                        camera_widget.process_joystick_queue_thread.start()

    def func_zoom_out(self,key): # Button zoomout
        camera_widget:CameraWidget = grid_item_selected.data.get('widget', None)
        if camera_widget is not None:
            logger.debug(f'func_zoom_out')
            if camera_widget.camera_model.data.ptzCap is not None and len(camera_widget.camera_model.data.ptzCap) >0:
                speed = 0 - key.value
                if camera_widget.camera_model.data.type == "AVIGILON":
                    data = {
                        "cameraId": camera_widget.camera_model.data.id,
                        "endPoint": "/camera/commands/pan-tilt-zoom",
                        "requestData": {
                            "continuous": {
                                "panAmount": 0,
                                "tiltAmount": 0,
                                "zoomAmount": speed,
                                "action": "START"
                                }
                        }
                    }
                    camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                else:
                    camera_widget.current_joystick_msg = {"type": 'button_zoom','speed': speed}
                    if not camera_widget.process_joystick_queue_thread.isRunning():
                        camera_widget.process_joystick_queue_thread.start()
            
    def add_records_signal(self,data):
        logger.debug(f'add_records_signal = {data}')
        camera_widget:CameraWidget = grid_item_selected.data['widget']
        if camera_widget is not None and isinstance(camera_widget, CameraWidget):
            camera_id = camera_widget.camera_model.data.id
            camera_model = camera_model_manager.get_camera_model(id=camera_id)
            server_ip = camera_model.data.server_ip
            camera_widget.timelinecontroller.setCameraName(camera_model.data.name)
            self.controller: Controller = controller_manager.get_controller(server_ip=server_ip)
            # camera_widget.record_data: RecordData = record_model_manager.record_data.get(camera_id, None)
            # try:
            #     if camera_widget.record_data is not None:
            #         camera_widget.record_data.recordDataChanged.disconnect(camera_widget.recordDataChanged)
                
            # except Exception as e:
            #     logger.debug(f'error record_data = {e}')
            if camera_widget.record_data is not None:
                camera_widget.record_data.recordDataChanged.connect(camera_widget.recordDataChanged)
                
            if camera_widget.record_data is None:
                camera_widget.timelinecontroller.setIsTimeLine(False)
                sizes = self.main_splitter.sizes()
                if sizes[1] == 0:
                    sizes[1] = self.min_height_timeline
                    sizes[0] = sizes[0] - self.min_height_timeline
                self.main_splitter.setSizes(sizes)
                return  # Early exit if no record data is found
        
            camera_data = {
                record.data.id: record
                for record in camera_widget.record_data.data
                if record.data.cameraId == camera_id
            }
            if camera_data:
                camera_widget.timelinecontroller.setIsTimeLine(False)
                camera_widget.timelinecontroller.setIsTimeLine(True)
            else:
                camera_widget.timelinecontroller.setIsTimeLine(False)
            if camera_data:
                camera_widget.timelinecontroller.initData(camera_widget.record_data.start_duration, camera_widget.record_data.end_duration)
                list_record = {}
                camera_widget.timelinecontroller.updateRecordDuration(camera_widget.record_data)
        sizes = self.main_splitter.sizes()
        if sizes[1] == 0:
            sizes[1] = self.min_height_timeline
            sizes[0] = sizes[0] - self.min_height_timeline
        self.main_splitter.setSizes(sizes)

    def timeout(self):
        self.timer = QTimer(self)
        self.timer.setInterval(2000)
        self.timer.timeout.connect(self.apply_auto_next)
        self.timer.start()

    def send_auto_next(self):
        self.timeout()

    def apply_auto_next(self):
        random_number = random.randint(1, 3)
        if random_number == 1:
            self.send_ai_event()
        elif random_number == 2:
            self.send_ai_event2()
        elif random_number == 3:
            self.send_ai_event3()

    def send_ai_event(self):
        data = {
            "id": "d4547c0c-bf00-4f38-a8da-524eb872ae5c",
            "createdAtLocalDate": "2025-05-13 07:13:37.041160",
            "event": "",
            "imageUrl": "https://minio.gpstech.vn/images/images/human/0b1375e1-126c-4d29-863f-d3c1fedb219a/2024_12_20/11_37/1734669479733/7fe0c10e-555c-4ad5-9bff-8b425f1db30e.jpg",
            "name": "Tùng",
            "type": "HUMAN",
            "status": "UNKNOWN",
            "ioId": "2824bfc8-52d3-451d-9434-a5adc30ff049",
            "ioGroups": [],
            "warningConfig": [
                59
            ],
            "cameraId": "0b1375e1-126c-4d29-863f-d3c1fedb219a",
            "cameraLocation": "Quốc lộ 3, Huyện Đông Anh, Hà Nội, Việt Nam",
            "cameraName": "CHECK-OUT T2 MAP",
            "directionType": "",
            "humanInfo": {
                "face": {
                    "age": "20-29",
                    "gender": "male",
                    "race": "east_asian"
                },
                "person": {
                    "hair": "",
                    "upper": {
                        "color": None,
                        "style": ""
                    },
                    "lower": {
                        "color": None,
                        "style": ""
                    },
                    "hat": False,
                    "glasses": False,
                    "bag": "",
                    "direction": "",
                    "holdObjectsInFront": False
                }
            },
            "imageCropUrl": "https://minio.gpstech.vn/images/images/human/0b1375e1-126c-4d29-863f-d3c1fedb219a/2024_12_20/11_37/1734669479733/7fe0c10e-555c-4ad5-9bff-8b425f1db30e.jpg",
            "imageFullUrl": "https://minio.gpstech.vn/images/images/human/0b1375e1-126c-4d29-863f-d3c1fedb219a/2024_12_20/11_38/1734669480048/a0b445ee-c62e-476c-9239-eafe77dd9830.jpg",
            "imageLicensePlateUrl": ""}
        self.event_ai = EventAI.from_dict(data)
        event_manager.add_event(self.event_ai)

    def send_ai_event2(self):
        data = {
            "id": "b39f47e7-dce9-4a78-96b6-7a9039b89df8",
            "createdAtLocalDate": "2025-05-13 07:13:37.041160",
            "event": "",
            "imageUrl": "https://minio.gpstech.vn/images/human/36cb66d3-ff17-4a1a-b924-54e4be560ab1/2024_05_23/15_18/1716452321714/cfe13270-0fca-454e-847a-83884418d4b5.jpg",
            "name": "Tùng",
            "type": "HUMAN",
            "status": "UNKNOWN",
            "ioId": "2824bfc8-52d3-451d-9434-a5adc30ff049",
            "ioGroups": [],
            "warningConfig": [
                59
            ],
            "cameraId": "0d5c3c70-878a-4766-91b8-487f2ed894b2",
            "cameraLocation": "Quốc lộ 3, Huyện Đông Anh, Hà Nội, Việt Nam",
            "cameraName": "CHECK-OUT T3 MAP",
            "directionType": "",
            "humanInfo": {
                "face": {
                    "age": "20-29",
                    "gender": "male",
                    "race": "east_asian"
                },
                "person": {
                    "hair": "",
                    "upper": {
                        "color": None,
                        "style": ""
                    },
                    "lower": {
                        "color": None,
                        "style": ""
                    },
                    "hat": False,
                    "glasses": False,
                    "bag": "",
                    "direction": "",
                    "holdObjectsInFront": False
                }
            },
            "imageFullUrl": "https://minio.gpstech.vn/images/human/36cb66d3-ff17-4a1a-b924-54e4be560ab1/2024_05_23/15_18/1716452321627/7bb9d087-715f-4de6-b0c6-60a21676a945.jpg",
            "imageLicensePlateUrl": ""}
        self.event_ai = EventAI.from_dict(data)
        event_manager.add_event(self.event_ai)

    def send_ai_event3(self):
        data = {
            "id": "b39f47e7-dce9-4a78-96b6-7a9039b89df7",
            "createdAtLocalDate": "2025-05-13 07:13:37.041160",
            "event": "",
            "imageUrl": "https://minio.gpstech.vn/images/human/36cb66d3-ff17-4a1a-b924-54e4be560ab1/2024_05_23/15_18/1716452321714/cfe13270-0fca-454e-847a-83884418d4b5.jpg",
            "name": "Tùng",
            "type": "HUMAN",
            "status": "UNKNOWN",
            "ioId": "2824bfc8-52d3-451d-9434-a5adc30ff049",
            "ioGroups": [],
            "warningConfig": [
                59
            ],
            "cameraId": "6d275b2e-31cf-467d-9a73-923f6458673e",
            "cameraLocation": "Quốc lộ 3, Huyện Đông Anh, Hà Nội, Việt Nam",
            "cameraName": "CHECK-IN T3 MAP",
            "directionType": "",
            "humanInfo": {
                "face": {
                    "age": "20-29",
                    "gender": "male",
                    "race": "east_asian"
                },
                "person": {
                    "hair": "",
                    "upper": {
                        "color": None,
                        "style": ""
                    },
                    "lower": {
                        "color": None,
                        "style": ""
                    },
                    "hat": False,
                    "glasses": False,
                    "bag": "",
                    "direction": "",
                    "holdObjectsInFront": False
                }
            },
            "imageFullUrl": "https://minio.gpstech.vn/images/human/36cb66d3-ff17-4a1a-b924-54e4be560ab1/2024_05_23/15_18/1716452321627/7bb9d087-715f-4de6-b0c6-60a21676a945.jpg",
            "imageLicensePlateUrl": ""}
        self.event_ai = EventAI.from_dict(data)
        event_manager.add_event(self.event_ai)

    def add_shortcut_key_list_signal(self,data):
        pass

    def notification_signal(self,data):
        Notifications(parent=main_controller.list_parent['CameraScreen'],
                              title=self.tr('This ShortcutID does not exist.'), icon=Style.PrimaryImage.info_result)

    def create_divider(self):
        divider = QFrame()
        divider.setFixedSize(1, 20)
        divider.setFrameShape(QFrame.Shape.VLine)
        divider.setFrameShadow(QFrame.Shadow.Sunken)
        divider.setStyleSheet(f"background-color: {Style.PrimaryColor.on_background}")
        return divider

    def create_title_bar(self):
        self.widget_button_system = WidgetButtonSystem(parent=self, window_parent=self.window_parent)
        self.layout_title_bar = QHBoxLayout()
        self.layout_title_bar.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_title_bar.setContentsMargins(0, 0, 0, 0)
        self.layout_title_bar.setSpacing(0)
        self.camera_bottom_toolbar = CameraBottomToolbarWidget(self)
        self.button_widget = QWidget()
        self.button_layout = QHBoxLayout(self.button_widget)
        self.button_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.button_layout.setSpacing(0)
        self.button_layout.setContentsMargins(0, 0, 0, 0)
        divider1 = self.create_divider()
        divider2 = self.create_divider()
        if Config.ENABLE_WARNING_ALERT_CAMERA:
            self.layout_object_detect = QHBoxLayout()
            self.layout_object_detect.setContentsMargins(0, 0, 4, 0)
            self.layout_object_detect.setAlignment(Qt.AlignmentFlag.AlignLeft)
            if Config.CONFIG_TEST_WARNING:
                self.auto_next = QPushButton('Auto Next')
                self.auto_next.clicked.connect(self.send_auto_next)
                self.layout_object_detect.addWidget(self.auto_next)
                self.button_test = QPushButton('Event 1')
                self.button_test.clicked.connect(self.send_ai_event)
                self.layout_object_detect.addWidget(self.button_test)
                self.button_test2 = QPushButton('Event 2')
                self.button_test2.clicked.connect(self.send_ai_event2)
                self.layout_object_detect.addWidget(self.button_test2)
                self.button_test3 = QPushButton('Event 3')
                self.button_test3.clicked.connect(self.send_ai_event3)
                self.layout_object_detect.addWidget(self.button_test3)
            self.widget_object_tracking = QWidget()
            self.widget_object_tracking.setLayout(self.layout_object_detect)
            self.button_layout.addWidget(self.widget_object_tracking, 5)
        self.button_layout.addWidget(self.camera_bottom_toolbar, 3)
        self.button_layout.addWidget(divider2)
        self.button_layout.addWidget(self.widget_button_system, 2)
        self.layout_title_bar.addWidget(self.new_custom_tab_widget, 9)
        self.layout_title_bar.addWidget(self.button_widget, 1)
        self.widget_title_bar = ActionableTitleBar(parent=self, window_parent=self.window_parent)
        self.widget_title_bar.setLayout(self.layout_title_bar)
        self.widget_title_bar.setFixedHeight(32)
    
    def create_timeline_widget(self):
        qmlRegisterType(RulerContext, "models", 1, 0, "RulerContext")
        qmlRegisterType(DurationPresent, "models", 1, 0, "DurationPresent")
        qmlRegisterType(TimeStep2, "models", 1, 0, "TimeStep2")
        qmlRegisterType(SpeedStatus, "models", 1, 0, "SpeedStatus")
        self.timelinecontroller = TimeLineController()
        self.timeLineManager = TimeLineManager(parent=self)
        self.timeLineManager.timeLineController = self.timelinecontroller
        qml_widget = QQuickWidget()
        qml_widget.engine().rootContext().setContextProperty('timeLineManager', self.timeLineManager)
        qml_widget.setSource(QUrl("qrc:src/common/qml/videoplayback/TimeLineControl.qml"))
        qml_widget.setResizeMode(QQuickWidget.SizeRootObjectToView)
        return qml_widget
    
    def combobox_hover(self, data):
        screen_index, screen = data
        self.main_treeview_widget.show_border_triggered(screen_index=screen_index, screen=screen)
        QTimer.singleShot(3000, lambda: self.main_treeview_widget.handle_menu_about_to_hide())

    def toggle_sidebar_move(self, event):
        button_pos = self.toggle_sidebar_button.mapToGlobal(event.position())
        handle_pos = self.handle_sidebar.mapToGlobal(self.handle_sidebar.rect().center())
        move_delta = button_pos.x() - handle_pos.x()
        new_x = handle_pos.x() + move_delta
        new_pos = QPoint(new_x, handle_pos.y())
        int_pos = int(new_pos.x())
        self.splitter.moveSplitter(int_pos, 1)

    def toggle_eventbar_move(self, event):
        button_pos = self.toggle_eventbar_button.mapToGlobal(event.position())
        handle_pos = self.handle_eventbar.mapToGlobal(self.handle_eventbar.rect().center())
        move_delta = button_pos.x() - handle_pos.x()
        new_x = handle_pos.x() + move_delta
        new_pos = QPoint(new_x, handle_pos.y())
        int_pos = int(new_pos.x())
        self.splitter.moveSplitter(int_pos, 2)

    def toggle_sidebar(self, left=True):
        sizes = self.splitter.sizes()
        left_size = sizes[0]
        center_size = sizes[1]
        right_size = sizes[2]
        if left_size != 0:
            sizes[0] = 0
            sizes[1] = left_size + sizes[1]
            sizes[2] = sizes[2]
        else:
            sizes[0] = self.min_width_sidebar_default
            sizes[1] = sizes[1] - self.min_width_sidebar_default
            sizes[2] = sizes[2]

        self.splitter.setSizes(sizes)

    def toggle_event_bar(self, left=True):
        sizes = self.splitter.sizes()
        left_size = sizes[0]
        center_size = sizes[1]
        right_size = sizes[2]
        if right_size != 0:
            sizes[0] = sizes[0]
            sizes[1] = right_size + sizes[1]
            sizes[2] = 0
        else:
            sizes[0] = sizes[0]
            sizes[1] = sizes[1] - self.min_width_sidebar_default
            sizes[2] = self.min_width_sidebar_default

        self.splitter.setSizes(sizes)

    def toggle_timeline(self, left=True):
        sizes = self.main_splitter.sizes()
        top_size = sizes[0]
        bottom_size = sizes[1]
        if bottom_size != 0:
            sizes[1] = 0
            sizes[0] = top_size + bottom_size
        else:
            sizes[0] = sizes[0] - self.min_height_timeline
            sizes[1] = self.min_height_timeline
        self.main_splitter.setSizes(sizes)

    def handle_splitter_moved(self, pos, index):
        sizes = self.splitter.sizes()
        # if index == 1:  # Handle 1
        #     if int(self.screen_available_width * 0.06) > self.splitter.sizes()[0]:
        #         sizes[0] = 0
        #         self.splitter.setSizes(sizes)
        #
        # if index == 2:  # Handle 2
        #     if int(self.screen_available_width * 0.06) > self.splitter.sizes()[2]:
        #         sizes[2] = 0
        #         self.splitter.setSizes(sizes)

    def load_tab_widget(self):
        self.add_tab_widget()

    def calculate_layout(self, desktop_screen_size=None):
        if desktop_screen_size is None:
            screen = QGuiApplication.primaryScreen()
            desktop_screen_size = screen.availableGeometry()
        self.screen_available_width = desktop_screen_size.width()
        self.screen_available_height = desktop_screen_size.height()
        menubar = self.screen_available_width * 0.0165
        margin = 40
        tab_bar = self.screen_available_height * 0.043
        height_of_button_toolbar = self.screen_available_height * 0.037
        percent_width_of_left_side_layout = 0.15
        percent_width_of_right_side_layout = 0.1 if Config.ENABLE_EVENT_BAR else 0.145
        percent_menu_bar = 0.05
        window_title = QApplication.style().pixelMetric(QStyle.PM_TitleBarHeight)
        self.width_left_side_layout = 0
        self.width_right_side_layout = 0

        self.width_left_side_layout = self.screen_available_width * \
            percent_width_of_left_side_layout
        if self.width_left_side_layout < self.screen_available_width * (percent_width_of_right_side_layout):
            self.width_left_side_layout = self.screen_available_width * \
                (percent_width_of_right_side_layout)
        self.width_right_side_layout = self.screen_available_width * \
            percent_width_of_right_side_layout

        if Config.ENABLE_EVENT_BAR:
            if self.width_right_side_layout < self.screen_available_width * percent_width_of_right_side_layout:
                self.width_right_side_layout = self.screen_available_width * percent_width_of_right_side_layout
        self.width_center_layout = self.screen_available_width * \
            ((1 - percent_width_of_left_side_layout - percent_width_of_right_side_layout - percent_menu_bar) if Config.ENABLE_EVENT_BAR else (1 - percent_width_of_left_side_layout - percent_menu_bar))
        self.grid_layout_width = self.width_center_layout
        self.grid_layout_height = self.width_center_layout * 9 / 16
        temp_grid_layout_height = self.screen_available_height - \
            height_of_button_toolbar - margin - tab_bar - window_title - 10
        if self.grid_layout_height > temp_grid_layout_height:
            self.grid_layout_height = temp_grid_layout_height
            self.grid_layout_width = self.grid_layout_height * 16 / 9

    def resize_layout(self):
        pass

    def on_show_event(self, event):
        pass

    def on_resize_event(self, event):
        self.frame_size = self.frameGeometry()

    def find_name_selected(self, tab_type=TabType.VirtualWindow):
        name_list = []
        if tab_type == TabType.VirtualWindow:
            for tab_name, tab_model in tab_model_manager.tab_model_list.items():
                if tab_model.data.type == TabType.VirtualWindow:
                    name_list.append(tab_name)
            is_name_selected = False
            count = 0
            new_name = self.tr('Virtual Window ') + f'{count}'
            while not is_name_selected:
                new_name = self.tr('Virtual Window ') + f'{count}'
                if new_name not in name_list:
                    is_name_selected = True
                else:
                    count += 1
            return new_name
        elif tab_type == TabType.SavedView:
            tab_model_list = tab_model_manager.get_tab_model_list()
            for id,item in tab_model_list.items():
                if item.data.type == TabType.SavedView:
                    name_list.append(item.data.name)
            is_name_selected = False
            count = 0
            new_name = self.tr('View ') + f'{count}'
            while not is_name_selected:
                new_name = self.tr('View ') + f'{count}'
                if new_name not in name_list:
                    is_name_selected = True
                else:
                    count += 1
            return new_name
        elif tab_type == TabType.MapView:
            tab_model_list = tab_model_manager.get_tab_model_list()
            for id,item in tab_model_list.items():
                if item.data.type == TabType.MapView:
                    name_list.append(item.data.name)
            is_name_selected = False
            count = 0
            new_name = self.tr('Map ') + f'{count}'
            while not is_name_selected:
                new_name = self.tr('Map ') + f'{count}'
                if new_name not in name_list:
                    is_name_selected = True
                else:
                    count += 1
            return new_name
        else:
            tab_model_list = tab_model_manager.get_tab_model_list()
            for id,item in tab_model_list.items():
                if item.data.type == TabType.Invalid:
                    name_list.append(item.data.name)
            is_name_selected = False
            count = 0
            new_name = self.tr('View ') + f'{count}'
            while not is_name_selected:
                new_name = self.tr('View ') + f'{count}'
                if new_name not in name_list:
                    is_name_selected = True
                else:
                    count += 1
            return new_name

    def create_tab_model(self,tab_type = TabType.Invalid):
        tab_name = self.find_name_selected(tab_type=tab_type)
        json_data = [item.to_dict() for item in original_list_data_grid]
        tab = Tab(id=tab_name, name=tab_name, type=tab_type,
                  isShow=True, currentGrid=GridButtonModel.StandardGrid.DIVISIONS_1.to_dict(),
                  index=None, listGridData={}, listGridCustomData=json_data, direction={})
        tab_model = TabModel(tab=tab)
        tab_model_manager.add_tab_model(tab_model)
        return tab_model

    def create_tracking_tab_model(self, camera_model_list=[], tab_name=None):
        json_data = [item.to_dict() for item in original_list_data_grid]
        tab = Tab(id=tab_name, name=tab_name, type=TabType.Invalid, isShow=False,
                  currentGrid=GridButtonModel.CustomGrid.DIVISIONS_8.to_dict(),
                  index=0, listGridData={}, listGridCustomData=json_data, direction={})
        tab_model = TabModel(tab=tab)
        for index, item in enumerate(camera_model_list):
            if isinstance(item, EventAI):
                grid_item = GridItem(index=index, type=ItemType.Event, model=item)
            else:
                grid_item = GridItem(index=index, type=ItemType.Camera, model=item)
            tab_model.add_grid_item(grid_item)
        return tab_model

    def add_tab_widget(self, tab_model = None):
        grid_item_selected.clear()
        if tab_model is None:
            tab_model = self.create_tab_model()
        ###########################################

        camera_grid_widget = CameraGridWidget(parent=self,tab_model = tab_model)
        self.center_stacked_widget.addWidget(camera_grid_widget)
        self.center_stacked_widget.setCurrentWidget(camera_grid_widget)
        self.new_custom_tab_widget.addTabWidget(tab_model = tab_model)
        return camera_grid_widget

    def create_controller(self):
        pass

    def handle_color_preview(self, camera_id, camera_name):
        widget = grid_item_selected.data['widget']
        if hasattr(widget, 'grid_item_unclicked'):
            widget.grid_item_unclicked()
        current_widget = self.center_stacked_widget.currentWidget()
        current_tab_key = main_controller.current_tab
        if grid_item_selected.is_tab_index(current_tab_key) and grid_item_selected.data['tab_index'] is not None:
            if camera_id == grid_item_selected.data['camera_id']:
                return
        if isinstance(current_widget, CameraGridWidget):
            check = False
            camera_id_found = False
            for index, grid_item in current_widget.tab_model.data.listGridData.items():
                if not camera_id_found and isinstance(grid_item.model,CameraModel) and camera_id == grid_item.model.data.id:
                    camera_id_found = True
                    logger.debug(f'handle_color_preview = {grid_item.model.data.name}')

                    grid_item_selected.set_data(tab_index=current_tab_key,screen='Main',camera_id=camera_id,widget=grid_item.widget)
                    grid_item.widget.stack_item.grid_item_clicked(main_controller.current_tab)
                    break

    def stop_live_handle(self, data):
        current_widget = self.center_stacked_widget.currentWidget()
        tab_model = current_widget.tab_model
        if tab_model is not None:
            camera_model: GroupModel = camera_model_manager.get_camera_model(name=data)
            if camera_model is not None:
                for index, grid_item in tab_model.data.listGridData.items():
                    if isinstance(grid_item.model, CameraModel) and grid_item.model.data.id in camera_model.data.id:
                        tab_model.remove_grid_item_signal.emit(index)
        main_controller.gc_collect(current_widget)

    def stop_live_group_handle(self, data):
        current_widget: CameraGridWidget = self.center_stacked_widget.currentWidget()
        tab_model = current_widget.tab_model
        group_name = data
        if tab_model is not None:
            group_model: GroupModel = group_model_manager.get_group_model(name=group_name)
            if group_model is not None:
                for index, grid_item in tab_model.data.listGridData.items():
                    if isinstance(grid_item.model, CameraModel) and grid_item.model.data.id in group_model.data.cameraIds:
                        tab_model.remove_grid_item_signal.emit(index)
        main_controller.gc_collect(current_widget)

    def event_open_new_tab(self,data):
        key, id = data
        widget = None
        if key == 'New View':
            widget = self.add_tab_widget()

        elif key == 'New Saved View':
            tab_name = self.main_treeview_widget.find_name_selected(TabType.SavedView)
            list_saved_view_item = self.main_treeview_widget.get_item('List Saved View', tree_type=TreeType.List_Saved_View)
            new_item_saved_view = self.main_treeview_widget.add_saved_view_triggered(list_saved_view_item)
            widget = self.main_treeview_widget.new_tab_savedview_triggered(new_item_saved_view)

        elif key == 'New Virtual Window':
            tab_name = self.main_treeview_widget.find_name_selected(TabType.VirtualWindow)
            list_virtual_window_item = self.main_treeview_widget.get_item('List Virtual Window',
                                                                      tree_type=TreeType.List_Virtual_Window)
            new_item = self.main_treeview_widget.new_virtual_window_triggered(list_virtual_window_item)
            widget = self.main_treeview_widget.auto_open_virtual_window(new_item)

        if widget is not None:
            model = event_manager.get_event(id)
            if model is not None:
                tab_model:TabModel = widget.tab_model
                grid_item = tab_model.data.listGridData.get(0,None)
                if grid_item.model == model:
                    return
                if  grid_item is not None:
                    grid_item.type = ItemType.Event
                    tab_model.set_model(grid_item = grid_item,model = model)
                    tab_model.add_grid_item_signal.emit(grid_item.index)

    def open_camera_in_tab_from_map(self, data):
        model, tab_name, row, col, item_data, list_camera_selection = data
        if item_data == TreeType.Camera:
            if tab_name == 'New View':
                widget = self.add_tab_widget()
                self.stream_camera_group_in_grid(is_group=False, model=model, widget=widget, row=row,
                                                 col=col)
            elif tab_name == 'New Saved View':
                tab_name = self.new_custom_tab_widget.find_name_selected(TabType.SavedView)
                self.open_to_new_virtual_or_saved(tab_type=TabType.SavedView, tab_name=tab_name,
                                                  model=model, row=row, col=col, is_group=False)
            elif tab_name == 'New Virtual Window':
                widget = None
                tab_name = self.new_custom_tab_widget.find_name_selected(TabType.VirtualWindow)
                self.open_to_new_virtual_or_saved(tab_type=TabType.VirtualWindow, tab_name=tab_name,
                                                  model=model, row=row, col=col, is_group=False)
            else:
                widget = self.new_custom_tab_widget.getWidgetByName(tab_name)
                self.stream_camera_group_in_grid(is_group=False, model=model, widget=widget, row=row, col=col)

        elif item_data == TreeType.Group:
            if tab_name == 'New View':
                widget = self.add_tab_widget()
                self.stream_camera_group_in_grid(is_group=True, model=model, widget=widget, row=row,
                                                 col=col)
            elif tab_name == 'New Saved View':
                tab_name = self.new_custom_tab_widget.find_name_selected(TabType.SavedView)
                self.open_to_new_virtual_or_saved(tab_type=TabType.SavedView, tab_name=tab_name,
                                                  model=model, row=row, col=col, is_group=True)
            elif tab_name == 'New Virtual Window':
                widget = None
                tab_name = self.new_custom_tab_widget.find_name_selected(TabType.VirtualWindow)
                self.open_to_new_virtual_or_saved(tab_type=TabType.VirtualWindow, tab_name=tab_name,
                                                  model=model, row=row, col=col, is_group=True)
            else:
                widget = self.new_custom_tab_widget.getWidgetByTabModel(tab_name)
                if widget is not None:
                    self.stream_camera_group_in_grid(is_group=True, model=model, widget=widget, row=row, col=col)

        elif item_data == TreeType.Multi_Select_Item:
            if tab_name == 'New View':
                widget = self.add_tab_widget()
                self.stream_camera_group_in_grid(is_multi_item_select=True, widget=widget, multi_camera_select=list_camera_selection)
            elif tab_name == 'New Saved View':
                tab_name = self.new_custom_tab_widget.find_name_selected(TabType.SavedView)
                self.open_to_new_virtual_or_saved(tab_type=TabType.SavedView, tab_name=tab_name,
                                                  model=model, row=row, col=col, is_group=False,
                                                  is_multi_item_select=True, multi_camera_select=list_camera_selection)
            elif tab_name == 'New Virtual Window':
                tab_name = self.new_custom_tab_widget.find_name_selected(TabType.VirtualWindow)
                self.open_to_new_virtual_or_saved(tab_type=TabType.VirtualWindow, tab_name=tab_name,
                                                  model=model, row=row, col=col, is_group=False,
                                                  is_multi_item_select=True, multi_camera_select=list_camera_selection)
            else:
                widget = self.new_custom_tab_widget.getWidgetByTabModel(tab_name)
                if widget is not None: 
                    self.stream_camera_group_in_grid(is_multi_item_select=True, widget=widget, multi_camera_select=list_camera_selection)

    def open_floor_in_tab_signal(self, data):
        model, tab_name, row, col, item_data, server_ip = data
        if item_data == TreeType.FloorItem:
            widget = self.new_custom_tab_widget.getWidgetByName(tab_name)
            if widget:
                widget: CameraGridWidget
                value = (model, row, col, widget.frame_width,
                            widget.frame_height, server_ip)
                widget.open_floor_in_position(value)


    def open_map_in_tab(self, data):
        model, tab_name, row, col, item_data, list_camera_selection, server_ip = data
        if item_data == TreeType.List_Map:
            widget = self.new_custom_tab_widget.getWidgetByName(tab_name)
            self.open_map_group_in_grid(widget=widget, model=model, row=row, col=col, server_ip=server_ip)

    def stream_camera_group_in_grid(self, is_group=False, is_multi_item_select=False, widget=None, model=None,
                                    row=None, col=None, multi_camera_select=None):
        if is_group:
            if widget:
                widget: CameraGridWidget
                list_id = []
                list_camera = []
                for id in model.data.cameraIds:
                    camera_model = camera_model_manager.get_camera_model(id = id)
                    if camera_model is not None:
                        list_camera.append(camera_model)
                if len(list_camera) > 0:
                    controller:Controller = controller_manager.get_controller(server_ip=model.data.server_ip)
                    widget.add_group_signal(list_camera)
                    if widget.tab_model.data.type != TabType.Invalid:
                        widget.tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.DropGroup,'data':{'id': model.data.id}}
                        controller.update_tabmodel_by_put(parent=self, tab=widget.tab_model.data)
                main_controller.show_message_dialog(None, "OPEN_CAMERA_SUCCESS")
            else:
                main_controller.show_message_dialog(None, "OPEN_CAMERA_FAIL")
        elif is_multi_item_select:
            if widget:
                widget: CameraGridWidget
                if multi_camera_select is not None and len(multi_camera_select) > 0:
                    controller:Controller = controller_manager.get_controller(server_ip=widget.tab_model.data.server_ip)
                    widget.add_group_signal(multi_camera_select)
                    if widget.tab_model.data.type != TabType.Invalid:
                        widget.tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.MultipleSelection,'data':{'id': None}}
                        controller.update_tabmodel_by_put(parent=self, tab=widget.tab_model.data)
        else:
            if widget:
                widget: CameraGridWidget
                value = (model, row, col, widget.frame_width,
                         widget.frame_height)
                widget.open_in_position_camera(value)
                main_controller.show_message_dialog(None, "OPEN_CAMERA_SUCCESS")
            else:
                main_controller.show_message_dialog(None, "OPEN_CAMERA_FAIL")

    def open_map_group_in_grid(self, widget=None, model=None, row=None, col=None, server_ip=None):
        if widget:
            widget: CameraGridWidget
            value = (model, row, col, widget.frame_width,
                        widget.frame_height, server_ip)
            widget.open_map_in_position(value)

    def open_camera_in_position(self, data):
        camera_name = data[0]
        tab_name = data[1]
        row_col = data[2]
        row = row_col[0]
        col = row_col[1]
        current_widget: CameraGridWidget = self.center_stacked_widget.currentWidget()
        value = (camera_name, row, col, current_widget.frame_width,
                 current_widget.frame_height)
        current_widget.open_in_position_camera(value)

    def open_map_in_position(self, data):
        camera_name = data[0]
        tab_name = data[1]
        row_col = data[2]
        row = row_col[0]
        col = row_col[1]
        current_widget: CameraGridWidget = self.center_stacked_widget.currentWidget()
        value = (camera_name, row, col, current_widget.frame_width,
                 current_widget.frame_height)
        current_widget.open_map_in_position(value)

    def edit_floor_signal(self, data):
        item, tab_type = data
        floor_model = item.item_model
        if floor_model is not None:
            # Kiểm tra xem tên tab và type có tồn tại chưa nếu chưa thì tạo tab mới
            tab_model_result: TabModel = tab_model_manager.get_tab_model(id = floor_model.id)
            if tab_model_result is not None:
                for index in range(self.new_custom_tab_widget.tab_widget.count()):
                    widget = self.new_custom_tab_widget.getWidgetByIndex(index)
                    if widget.tab_model == tab_model_result:
                        self.new_custom_tab_widget.tab_widget.setCurrentIndex(index)
                        return
            # Nếu đã tồn tại thì không tạo tab mới
            json_data = [item.to_dict() for item in original_list_data_grid]
            tab = Tab(id=floor_model.id, name=self.tr("Editing ") + item.text(), type=tab_type,
                    isShow=True, currentGrid=GridButtonModel.StandardGrid.DIVISIONS_1.to_dict(),
                    index=None, listGridData={}, listGridCustomData=json_data, direction={})
            tab_model = TabModel(tab=tab)
            tab_model_manager.add_tab_model(tab_model)

            camera_grid_widget = self.add_tab_widget(tab_model=tab_model)
            grid_item = tab_model.data.listGridData.get(0, None)
            if grid_item.model == floor_model:
                # case kéo một event trùng nhau
                return
            if  grid_item is not None:
                grid_item.type = ItemType.Floor
                tab_model.set_model(grid_item = grid_item,model = floor_model)
            tab_model.add_grid_item_signal.emit(grid_item.index)

    def edit_map_handle(self, data):
        item, tab_type = data
        map_model = item.item_model
        if map_model is not None:
            # Kiểm tra xem tên tab và type có tồn tại chưa nếu chưa thì tạo tab mới
            tab_model_result: TabModel = tab_model_manager.get_tab_model(id = map_model.id)
            if tab_model_result is not None:
                for index in range(self.new_custom_tab_widget.tab_widget.count()):
                    widget = self.new_custom_tab_widget.getWidgetByIndex(index)
                    if widget.tab_model == tab_model_result:
                        self.new_custom_tab_widget.tab_widget.setCurrentIndex(index)
                        return
            # Nếu đã tồn tại thì không tạo tab mới
            json_data = [item.to_dict() for item in original_list_data_grid]
            tab = Tab(id=map_model.id, name=self.tr("Editing digital map"), type=tab_type,
                    isShow=True, currentGrid=GridButtonModel.StandardGrid.DIVISIONS_1.to_dict(),
                    index=None, listGridData={}, listGridCustomData=json_data, direction={})
            tab_model = TabModel(tab=tab)
            tab_model_manager.add_tab_model(tab_model)

            camera_grid_widget = self.add_tab_widget(tab_model=tab_model)
            grid_item = tab_model.data.listGridData.get(0, None)
            if grid_item.model == map_model:
                # case kéo một event trùng nhau
                return
            if  grid_item is not None:
                grid_item.type = ItemType.MapOSM
                tab_model.set_model(grid_item = grid_item,model = map_model)
            tab_model.add_grid_item_signal.emit(grid_item.index)

    def switch_video_stream_from_camera_widget(self, data):
        current_widget: CameraGridWidget = self.center_stacked_widget.currentWidget()
        current_widget.switch_video_stream_from_button(data)

    def open_to_new_virtual_or_saved(self, tab_type, tab_name, model, row, col, is_group=False, is_multi_item_select=False, multi_camera_select=None):
        self.main_treeview_widget.open_to_new_virtual_or_saved_trigger(tab_type, tab_name, model, row, col,
                                                                       is_group, is_multi_item_select, multi_camera_select)

    def open_camera_in_tab(self, data):
        logger.debug(f"data tab index {data}")
        pass

    def save_camera_stream_dictionary(self):
        list_widget = self.new_custom_tab_widget.getAllWidget()
        for widget in list_widget:
            widget: CameraGridWidget
            widget.save_camera_stream_dictionary()

    def complete_fetching_data(self,data):
        controller:Controller = data
        for serverIp,item in floor_manager.data.items():
            for id, floorModel in item.items():
                pass
        for tab_model in tab_model_manager.tab_model_list.values():
            if tab_model.data.type != TabType.Invalid and tab_model.data.server_ip == controller.server.data.server_ip:
                tab_model.convert_data()

        self.main_treeview_widget.create_server(controller=controller)
        self.main_treeview_widget.update_treeview_data(server_ip=controller.server.data.server_ip)
        self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Invalid})


    def update_filter_camera(self, list_camera=[]):
        pass

    def on_window_resized(self, event):
        pass

    def process_event(self, data):
        event_id,route_list = data
        self.event:EventAI = event_manager.get_event(id=event_id)
        print(route_list)
        self.list_tracing_camera = []
        self.list_tracing_camera.append(camera_model_manager.get_camera_model(id=self.event.cameraId))
        self.list_tracing_camera.insert(1, self.event)
        for distance_cam_dict in route_list:
            logger.debug(f'distance_cam_dict = {distance_cam_dict}')
            self.list_tracing_camera.append(distance_cam_dict['camera'])
        
        logger.debug(f'process_event = {route_list}')
        self.update_ui_signal.emit(("Tracing Event", route_list))

    def update_ui_slot(self,data):
        current_time = time.time()
        tab_name,camera_model = data
        logger.debug(f'update_ui_slot = {len(self.list_tracing_camera)}')
        for cameraModel in self.list_tracing_camera:
            if isinstance(cameraModel,CameraModel):
                logger.debug(f'cameraModel = {cameraModel.data.name}')
        # try:
        # Thực hiện tính toán và sắp xếp ở đây
        if len(self.list_tracing_camera) != 0:
            key, tracking_window, is_existing = self.find_value_in_dict(main_controller.tracing_windows_dict, None)
            logger.debug(f'key, tracking_window, is_existing {key, tracking_window, is_existing}')
            screen_idx = screenModelManager.getScreenIndex()
            if screen_idx is None:
                return
            if screen_idx not in main_controller.tracing_windows_dict and not is_existing:
                self.tab_model_tracking = self.create_tracking_tab_model(camera_model_list=self.list_tracing_camera,
                                                                        tab_name=tab_name)
                current_time = time.time()

                tracking_window = TrackingCameraGridWidget(screen_index=screen_idx,
                                                        is_demo=True, tab_name=tab_name,
                                                        tracking_node_model=None,
                                                        tab_model=self.tab_model_tracking)
                end_time = time.time()
                logger.debug(f'end_time2 = {end_time-current_time}')
                tuple_target = (tracking_window, None)
                main_controller.tracing_windows_dict[screen_idx] = [tuple_target]
                main_controller.tracing_windows_dict[screen_idx][0][0].showFullScreen()
                next_camera_ids = [self.list_tracing_camera[2].data.id]
                self.handle_alert(self.tab_model_tracking, self.event, next_camera_ids, False)

            else:
                for screen_idx, window_and_model in list(main_controller.tracing_windows_dict.items()):
                    for item in window_and_model:
                        if self.tab_model_tracking is not None:
                            self.diff_camera_list(new_list_camera_tracing=self.list_tracing_camera,
                                                tab_name=tab_name)
                            next_camera_ids = [self.list_tracing_camera[2].data.id]
                            self.handle_alert(self.tab_model_tracking, self.event, next_camera_ids, True)

        # self.item_alert_in_treeview(camera_model.data.name)

        # except Exception as e:
        #     logger.error(f"Error in process_event: {e}")
        end_time = time.time()

    def grid_item_changed(self, data):
        camera_widget = data
        if isinstance(camera_widget, CameraWidget):
            self.controller: Controller = controller_manager.get_controller(
                server_ip=camera_widget.camera_model.data.server_ip
            )
            if camera_widget.timelinecontroller is None:
                camera_widget.create_timelinecontroller()
                camera_widget.timelinecontroller.openCalendarDialog.connect(self.openCalendarDialog)
                self.timeLineManager.timeLineController = camera_widget.timelinecontroller
                self.controller.get_videos(parent=self, cameraIds=camera_widget.camera_model.data.id)
            else:
                self.timeLineManager.timeLineController = camera_widget.timelinecontroller
        else:
            self.default_timelinecontroller()

    def default_timelinecontroller(self):
        self.timeLineManager.timeLineController = self.timelinecontroller
        self.timelinecontroller.setIsTimeLine(False)
        self.timelinecontroller.setCameraName(self.tr('No camera selected'))

    def openCalendarDialog(self, data):
        if data.isCalendar:
            if self.calendar_dialog is not None:
                self.calendar_dialog.close()
                self.calendar_dialog = None
        else:
            self.calendar_dialog = CalendarDialog(parent=self,timeLineManager=self.timeLineManager)
            self.calendar_dialog.show()

    def showMenu(self, position):
        logger.debug(f'showMenu = {position, type(position)}')
        menu = QMenu()
        menu.setStyleSheet(Style.StyleSheet.context_menu)
        clear_selection = menu.addAction('Clear Selection')
        add_bookmark = menu.addAction('Add bookmark')
        export_video = menu.addAction('Export video')
        preview_search = menu.addAction('Preview search')

        current_mouse = QCursor.pos()
        logger.debug(f'current_mouse = {current_mouse}')
        position_of_mouse = self.mapFromGlobal(current_mouse)
        logger.debug(f'position_of_mouse = {position_of_mouse}')
        action = menu.exec_(self.mapToGlobal(position_of_mouse))
        if action == clear_selection:
            print('Clear Selection')
        elif action == add_bookmark:
            print('Add bookmark')
        elif action == export_video:
            print('Export video')
        elif action == preview_search:
            print('Preview search')
        else:
            print('None')

        self.timeline.closeMenu()

    def add_event_signal(self, event_id):
        if screenModelManager.isTracking:
            self.event_processor.event_queue.put(event_id)

    def find_value_in_dict(self, target_dict, value):
        for key, items in target_dict.items():
            for item in items:
                if value == item[1]:
                    return key, item[0], True
        return None, None, False

    def handle_alert(self, tab_model, event, next_camera_ids, close_alert):
        for index, grid_item in tab_model.data.listGridData.items():
            if grid_item.virtual_widget is not None and isinstance(grid_item.virtual_widget, CameraWidget):
                if close_alert:
                    grid_item.virtual_widget.warning_alert_widget.close_alert_widget()
                if event.cameraId == grid_item.model.data.id:
                    grid_item.virtual_widget.add_alert_to_queue(event)
                if grid_item.model.data.id in next_camera_ids:
                    grid_item.virtual_widget.prepare_show_next_tracking_animation()

    def diff_camera_list(self, new_list_camera_tracing=[], tab_name=None):
        tab_model = self.tab_model_tracking
        if tab_model is not None:
            # sắp xếp lại vị trí camera giống nhau giữa 2 list trước
            for idx, model in enumerate(new_list_camera_tracing):
                for index, grid_item in tab_model.data.listGridData.items():
                    if grid_item.virtual_widget is not None and isinstance(grid_item.virtual_widget, CameraWidget):
                        grid_item.virtual_widget.stop_all_timer()
                    if model == grid_item.model:
                        if idx == index:
                            # camera giống nhau và trùng vị trí luôn
                            break
                        else:
                            new_grid_item = tab_model.data.listGridData[idx]
                            new_row = new_grid_item.row
                            new_col = new_grid_item.col
                            old_grid_item = tab_model.data.listGridData[index]
                            old_row = old_grid_item.row
                            old_col = old_grid_item.col
                            new_grid_item.index = index
                            new_grid_item.row = old_row
                            new_grid_item.col = old_col
                            old_grid_item.index = idx
                            old_grid_item.row = new_row
                            old_grid_item.col = new_col
                            tab_model.data.listGridData[idx] = old_grid_item
                            tab_model.data.listGridData[index] = new_grid_item
                            tab_model.swap_grid_item_signal.emit((idx,index))
                            break
            # Drop những camera vào đúng vị trí
            for idx, model in enumerate(new_list_camera_tracing):
                temp = False
                for index, grid_item in tab_model.data.listGridData.items():
                    if model == grid_item.model:
                        temp = True
                if not temp:
                    # kiểm tra ra camera ở vị trí idx không có trong danh sách camera cũ lên cần drop vào
                    if isinstance(model, CameraModel):
                        tab_model.data.listGridData[idx].type = ItemType.Camera
                        tab_model.data.listGridData[idx].model = model
                        tab_model.add_grid_item_signal.emit(idx)
                    elif isinstance(model, EventAI):
                        pass
                        tab_model.data.listGridData[idx].type = ItemType.Event
                        tab_model.data.listGridData[idx].model = model
                        tab_model.add_grid_item_signal.emit(idx)

    def item_alert_in_treeview(self, camera_name):
        self.main_treeview_widget.change_item_color(camera_name, QColor(236, 122, 26))
        QTimer.singleShot(5000, lambda: self.main_treeview_widget.change_item_color(camera_name, QColor(f"{main_controller.get_theme_attribute('Color', 'main_background')}")))

    def stop_app(self):
        if Config.ENABLE_WARNING_ALERT_CAMERA:
            self.run_alert_processor = False

        if Config.ENABLE_EVENT_BAR:
            self.event_bar.event_list_view.thread_pool.shutdown(
                wait=True, cancel_futures=True)

    def callback_tab_changed(self, idx):
        self.center_stacked_widget.setCurrentIndex(idx)
        current_widget: CameraGridWidget = self.center_stacked_widget.currentWidget()
        self.camera_bottom_toolbar.set_camera_grid_widget(current_widget)
        if current_widget.tab_model is not None:
            list_custom_grid_model = []
            if current_widget.tab_model.data.index == idx:
                current_list_data_custom_grid = current_widget.tab_model.data.listGridCustomData
                current_grid_model = current_widget.tab_model.data.currentGrid
                if current_list_data_custom_grid is not None and current_grid_model is not None:
                    for grid_data in current_list_data_custom_grid:
                        model = ItemGridModel.from_dict(grid_data)
                        list_custom_grid_model.append(model)
                    current_model = ItemGridModel.from_dict(current_grid_model)
                    current_widget.camera_bottom_toolbar.list_custom_grid_model = list_custom_grid_model
                    current_widget.camera_bottom_toolbar.update_ui_grid_menu(list_custom_grid_model, current_model)

    def search_items(self,text):
        self.main_treeview_widget.put_filter_queue({'text':text,'status':Status.Invalid})

    def change_mode_trigger(self,data):
        self.filter_text.show()
        if data == "All":
            self.filter_text.hide()
            self.main_treeview_widget.filter_mode_status = TreeType.Invalid
            self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Change_Filter_Mode})
        elif data == "Server":
            self.filter_text.label.setText(data)
            self.main_treeview_widget.filter_mode_status = TreeType.Server
            self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Change_Filter_Mode})
        elif data == "Camera Group":
            self.filter_text.label.setText(data)
            self.main_treeview_widget.filter_mode_status = TreeType.List_Camera
            self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Change_Filter_Mode})
        elif data == "Virtual Window":
            self.filter_text.label.setText(data)
            self.main_treeview_widget.filter_mode_status = TreeType.List_Virtual_Window
            self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Change_Filter_Mode})
        elif data == "Saved View":
            self.filter_text.label.setText(data)
            self.main_treeview_widget.filter_mode_status = TreeType.List_Saved_View
            self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Change_Filter_Mode})
        elif data == "Map":
            self.filter_text.label.setText(data)
            self.main_treeview_widget.filter_mode_status = TreeType.List_Map
            self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Change_Filter_Mode})
        else:
            pass

    def show_camera_info_widget(self, camera_model: CameraModel = None, focus_camera_name=False):
        if main_controller.key_filter is not None:
            QApplication.instance().removeEventFilter(main_controller.key_filter)
        camera_info = CameraInfoDialog(parent=self, title=camera_model.data.name, data=camera_model)
        if focus_camera_name:
            camera_info.text_camera_name.line_edit.setFocus()
        camera_info.exec()
        if main_controller.key_filter is not None:
            QApplication.instance().installEventFilter(main_controller.key_filter)

    def icon_clicked(self):
        self.filter_text.hide()
        self.main_treeview_widget.filter_mode_status = TreeType.Server
        self.main_treeview_widget.put_filter_queue(
            {'text': self.widget_search_title_bar.search_widget.search_bar.text(), 'status': Status.Change_Filter_Mode})

    def open_setting_dialog_in_camera_screen(self, camera_name):
        camera_model = None
        camera_list = camera_model_manager.get_camera_list()
        for camera in camera_list:
            if camera_name == camera.data.name:
                camera_model = camera
        camera_info = CameraInfoDialog(parent=self, title=camera_model.data.name, data=camera_model)
        camera_info.exec()

    def rename_camera_from_preview_trigger(self, item_model):
        self.show_camera_info_widget(camera_model=item_model, focus_camera_name=True)

    def eventFilter(self, obj, event):
        if obj == self:
            pass
        if obj.objectName() == "toggle_sidebar_button":
            if self.splitter is not None and self.splitter.widget(0) is not None:
                is_sidebar_visible = self.splitter.sizes()[0]
                if isinstance(obj, ButtonStatusSidebar):
                    if event.type() == QEvent.Type.Enter:
                        if is_sidebar_visible == 0:
                            self.toggle_sidebar_button.setToolTip(self.tr("Open the left sidebar"))
                            self.toggle_sidebar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_sidebar_big_right')))
                        else:
                            self.toggle_sidebar_button.setToolTip(self.tr("Close the left sidebar"))
                            self.toggle_sidebar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_sidebar_big_left')))
                    elif event.type() == QEvent.Type.Leave:
                        self.toggle_sidebar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_status_sidebar')))

        if obj.objectName() == "toggle_eventbar_button":
            if self.splitter is not None and self.splitter.widget(2) is not None:
                is_event_bar_visible = self.splitter.sizes()[2]
                if isinstance(obj, ButtonStatusSidebar):
                    if event.type() == QEvent.Type.Enter:
                        if is_event_bar_visible == 0:
                            self.toggle_eventbar_button.setToolTip(self.tr("Open the event bar"))
                            self.toggle_eventbar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_sidebar_big_left')))
                        else:
                            self.toggle_eventbar_button.setToolTip(self.tr("Close the event bar"))
                            self.toggle_eventbar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_sidebar_big_right')))
                    elif event.type() == QEvent.Type.Leave:
                        self.toggle_eventbar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_status_sidebar')))

        return super().eventFilter(obj, event)

    def restyle_camera_screen(self):
        self.main_treeview_widget.restyle_main_treeview_widget()
        self.new_custom_tab_widget.restyle_custom_tab_widget()
        self.widget_button_system.set_dynamic_stylesheet()
        self.camera_bottom_toolbar.restyle_camera_bottom_toolbar()
        self.center_view_widget.setStyleSheet(f'''
                 QWidget#center_view_widget{{
                 border-top: 1px solid {main_controller.get_theme_attribute('Color','common_border')};
                 }}
             ''')
        self.splitter.setStyleSheet(
            f"""QSplitter::handle {{ 
            background-color: {main_controller.get_theme_attribute('Color', 'main_background_splitter')}; 
            border-top: 1px solid {main_controller.get_theme_attribute('Color','common_border')}
            }}""")

        self.main_splitter.setStyleSheet(
            f"""QSplitter::handle {{ 
                    background-color: {main_controller.get_theme_attribute('Color', 'main_background_splitter')}; 
                    border-top: 1px solid {main_controller.get_theme_attribute('Color', 'common_border')}
                    }}""")
        self.toggle_timeline_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_status_timeline')))
        self.toggle_sidebar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image','icon_status_sidebar')))
        self.toggle_eventbar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image','icon_status_sidebar')))

        if Config.ENABLE_EVENT_BAR:
            self.event_bar.restyle_even_bar()

        list_widget = self.new_custom_tab_widget.getAllWidget()
        for widget in list_widget:
            widget: CameraGridWidget
            widget.restyle_camera_grid_widget()
        self.widget_search_title_bar.set_dynamic_stylesheet()
        self.widget_title_bar.set_dynamic_stylesheet()
        self.side_bar_widget.setStyleSheet(f'''
            QWidget#side_bar_widget {{
                border-left: None; 
                border-top: 1px solid {main_controller.get_theme_attribute('Color','common_border')};
                border-bottom: None;
                border-right: 1px solid {main_controller.get_theme_attribute('Color','common_border')};
            }}
        ''')
        self.filter_text.set_dynamic_stylesheet()

        self.timelinecontroller.setTheme("dark" if main_controller.current_theme == Theme.DARK else "light")


    def retranslate_camera_screen(self):
        self.main_treeview_widget.retranslateUi()
        self.widget_search_title_bar.retransalate_ui()
        self.widget_search_title_bar.search_widget.retranslateUi_searchbar()
        if Config.ENABLE_EVENT_BAR:
            self.event_bar.retranslateUi()
        list_widget = self.new_custom_tab_widget.getAllWidget()
        for widget in list_widget:
            widget: CameraGridWidget
            widget.retranslateUi()

    def closeEvent(self, event):
        self.event_processor.stop()
        self.event_processor.event_queue.join()
        super().closeEvent(event)

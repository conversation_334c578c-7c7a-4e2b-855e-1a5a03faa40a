from dataclasses import dataclass, field, asdict, fields
from typing import List, Optional, Dict, Any
import json

@dataclass
class Resolution:
    width: int = None
    height: int = None

    @classmethod
    def fromDict(cls, data_dict):
        return cls(width=data_dict.get("width"), height=data_dict.get("height"))

    def toDict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}

@dataclass
class CameraData:
    id: str = None
    profileToken: str = None
    urlMainstream: str = None
    urlSubStream: str = None
    supportedMainResolution: List[Resolution] = None
    mainstreamResolution: str = None
    supportedMainFps: List[int] = None
    mainstreamFps: str = None
    # rtsp_list: List[str] = None
    is_ptz: bool = None
    ptzCap: List[str] = None
    supportedSubResolution: List[Resolution] = None
    substreamResolution: str = None
    supportedSubFps: List[int] = None
    substreamFps: str = None
    username: str = None
    password: str = None
    ip: str = None
    port: int = None
    manufacturer: Optional[str] = None
    camera_type: Optional[str] = None
    camera_model: Optional[str] = None
    added: bool = False

    @classmethod
    def fromDict(cls, model_dict):
        return cls(
            id=model_dict.get("id"),
            profileToken=model_dict.get("profileToken"),
            # supportedMainResolution=[Resolution.fromDict(res) for res in json.loads(model_dict["supportedMainResolution"])],
            urlMainstream=model_dict.get("urlMainstream"),
            urlSubStream=model_dict.get("urlSubStream"),
            supportedMainResolution=model_dict.get("supportedMainResolution"),
            mainstreamResolution=model_dict.get("mainstreamResolution"),
            supportedMainFps=model_dict.get("supportedMainFps"),
            mainstreamFps=model_dict.get("mainstreamFps"),
            # rtsp_list=model_dict.get("rtsp_list"),
            is_ptz=model_dict.get("is_ptz"),
            ptzCap = model_dict.get("ptzCap"),
            # supportedSubResolution=[Resolution.fromDict(res) for res in json.loads(model_dict["supportedSubResolution"])],
            supportedSubResolution=model_dict.get("supportedSubResolution"),
            substreamResolution=model_dict.get("substreamResolution"),
            supportedSubFps=model_dict.get("supportedSubFps"),
            substreamFps=model_dict.get("substreamFps"),
            username=model_dict.get("username"),
            password=model_dict.get("password"),
            ip=model_dict.get("ip"),
            port=model_dict.get("port"),
            manufacturer=model_dict.get("manufacturer"),
            camera_type=model_dict.get("camera_type"),
            camera_model=model_dict.get("camera_model"),
            added=model_dict.get("added")
        )

    def toDict(self):
        return {
            "profileToken": self.profileToken,
            "supportedMainResolution": json.dumps([res.toDict() for res in self.supportedMainResolution]),
            "mainstreamResolution": self.mainstreamResolution,
            "supportedMainFps": json.dumps(self.supportedMainFps),
            "mainstreamFps": self.mainstreamFps,
            "rtsp_list": self.rtsp_list,
            "is_ptz": self.is_ptz,
            "supportedSubResolution": json.dumps([res.toDict() for res in self.supportedSubResolution]),
            "substreamResolution": self.substreamResolution,
            "supportedSubFps": json.dumps(self.supportedSubFps),
            "substreamFps": self.substreamFps,
            "username": self.username,
            "password": self.password,
            "ip": self.ip,
            "port": self.port,
            "manufacturer": self.manufacturer,
            "camera_type": self.camera_type,
            "camera_model": self.camera_model,
            "added": self.added
        }

@dataclass
class EventTestData:
    id: str = None
    createdAt: str = None
    dst: str = None
    deliveryTag: Optional[str] = None
    event: str = None
    data: List[CameraData] = None

    @classmethod
    def fromDict(cls, data_dict):
        id = data_dict.get("id")
        createdAt = data_dict.get("createdAt")
        dst = data_dict.get("dst")
        deliveryTag = data_dict.get("deliveryTag")
        event = data_dict.get("event")
        data_node_cameras = [CameraData.fromDict(camera) for camera in data_dict.get('data', [])]

        return cls(
            id=id,
            createdAt=createdAt,
            dst=dst,
            deliveryTag=deliveryTag,
            event=event,
            data=data_node_cameras
        )

    def toDict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "createdAt": self.createdAt,
            "dst": self.dst,
            "deliveryTag": self.deliveryTag,
            "event": self.event,
            "data": [node.toDict() for node in self.data]
        }

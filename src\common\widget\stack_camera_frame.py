import pickle
import json
from PySide6.QtGui import QDrag, QIcon, QMouseEvent, QColor,QRegion, QPainter, QBrush, QFontMetrics
from PySide6.QtWidgets import QStackedWidget, QApplication, QLabel, QSizePolicy,QWidget,QHBoxLayout
from PySide6.QtCore import Signal, Qt, QMimeData, QPoint, QTimer, QRect, QByteArray
from src.common.qml.models.map_controller import FloorModel,floor_manager,BuildingModel,building_manager,MapModel,map_manager
from src.common.widget.animation_label import AnimationBorderQLabel
from src.common.controller.main_controller import main_controller
from src.common.model.main_tree_view_model import TreeType
from src.common.model.group_model import group_model_manager
from src.common.model.camera_model import camera_model_manager
from src.styles.style import Style
from src.common.camera.grid_item_selected import grid_item_selected
from src.common.key_board.key_board_manager import key_board_manager
import logging
from src.common.model.event_data_model import event_manager
from src.common.model.tab_model import TabModel,tab_model_manager,GridItem,ItemType,SignalType
from src.common.model.device_models import TabType
from src.common.controller.controller_manager import Controller, controller_manager
import uuid
import time
logger = logging.getLogger(__name__)

class OverlayLabel(QLabel):
    border_width = 1
    padding = 10
    def __init__(self,parent, **kwargs):
        super().__init__(parent, **kwargs)

        self.setAttribute(Qt.WA_TransparentForMouseEvents)

    def resizeEvent(self, event) -> None:
        # print(f'resizeEvent: {self.rect()}')
        border = QRegion(self.rect())
        border -= QRegion(
            QRect(
                self.border_width,
                self.border_width,
                self.width() - self.border_width * 2,
                self.height() - self.border_width * 2,
            )
        )
        # print(f'border: {border.boundingRect()}')
        self.setMask(border)

    def paintEvent(self, event) -> None:
        painter = QPainter(self)

        painter.setPen(Qt.NoPen)
        painter.setBrush(QBrush(QColor(0, 0, 0, 0)))
        painter.drawRect(self.rect())

        #draw text
        label = self.printable_label(event.rect().width(), painter.font())
        painter.fillRect(self.rect(), QColor(0, 0, 0, 128))
        painter.setPen(QColor(255, 255, 255))
        # pos x at center, pos y at center
        print(f'event.rect() = {self.rect()} - label: {label}')
        painter.drawText(self.rect(), Qt.AlignCenter, label)

    def printable_label(self, width, font):
        label = self.text()

        max_width = width - self.padding * 2
        metrics = QFontMetrics(font)

        size = metrics.size(0, label)
        if size.width() > max_width:
            size.setWidth(max_width)
            label = metrics.elidedText(label, Qt.ElideMiddle, max_width)

        return label

class OverlayBorder(QWidget):
    border_width = 1

    def __init__(self,parent=None, **kwargs):
        super().__init__(parent, **kwargs)

        self.setAttribute(Qt.WA_TransparentForMouseEvents)

    def resizeEvent(self, event) -> None:
        # print(f'resizeEvent: {self.rect()}')
        border = QRegion(self.rect())
        border -= QRegion(
            QRect(
                self.border_width,
                self.border_width,
                self.width() - self.border_width * 2,
                self.height() - self.border_width * 2,
            )
        )
        # print(f'border: {border.boundingRect()}')
        self.setMask(border)

    def paintEvent(self, event) -> None:
        painter = QPainter(self)

        painter.setPen(Qt.NoPen)
        painter.setBrush(QBrush(QColor(255, 0, 0, 255)))
        painter.drawRect(self.rect())

class BackgroundWidget(QWidget):

    def __init__(self,parent = None, stack_item=None,index = None):
        super().__init__(parent)
        self.setFocusPolicy(Qt.StrongFocus)
        self.setMouseTracking(True)
        self.stack_item = stack_item
        self.index = index
        self.screen_name ='Main'
        self.root_width = None
        self.root_height = None
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(0,0,0,0)
        self.load_ui()

    def load_ui(self):
        self.background_label = QLabel()
        # self.camera_frame.mousePressEvent = self.frame_press_event
        self.background_label.setContentsMargins(0, 0, 0, 0)
        self.background_label.setAlignment(Qt.AlignmentFlag.AlignTop|Qt.AlignmentFlag.AlignCenter)
        self.background_label.setStyleSheet(f'''
            background-color: {main_controller.get_theme_attribute("Color", "camera_widget_background")}; 
        ''')
        self.background_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.main_layout.addWidget(self.background_label)
    
    def update_resize(self, width, height):
        pass

    # def mousePressEvent(self, event):
    #     if event.button() == Qt.LeftButton:
    #         print(f'LeftButton')
    #         current_tab = main_controller.current_tab
    #         # self.index = self.camera_grid_widget.get_index_of_item_from_row_col(self.position.x(),self.position.y())
    #         self.grid_item_clicked(current_tab=current_tab)

class StackFrame(QStackedWidget):
    drop_group_signal = Signal(object)
    grid_item_clicked_signal = Signal(object)

    def __init__(self, parent, frame_width, frame_height, position=None, is_tracking_item=False):
        super().__init__()
        self.setMouseTracking(True)
        self.setContentsMargins(0, 0, 0, 0)
        self.is_tracking_item = is_tracking_item
        self.position = position
        self.frame_width = frame_width
        self.frame_height = frame_height
        self.camera_grid_widget = parent
        self.screen_name = 'Main'

        # self.camera_frame = QLabel()
        # # self.camera_frame.mousePressEvent = self.frame_press_event
        # self.camera_frame.setContentsMargins(0, 0, 0, 0)
        # self.camera_frame.setAlignment(Qt.AlignmentFlag.AlignfTop|Qt.AlignmentFlag.AlignCenter)
        # self.camera_frame.setStyleSheet(Style.StyleSheet.camera_frame)
        # self.camera_frame.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.main_widget = None
        self.background_widget = None
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        # self.update_border()
        self.setAcceptDrops(True)
        self.index = self.camera_grid_widget.get_index_of_item_from_row_col(self.position.x(),self.position.y())
        # self.addWidget(self.main_widget)
        # self.add_widget()

        self.border_widget = None
        if not self.is_tracking_item:

            self.border_widget = OverlayBorder(self)
            self.border_widget.setContentsMargins(0, 0, 0, 0)
            self.border_widget.setObjectName("border_select")
            # self.border_widget.setStyleSheet(Style.StyleSheet.item_focused)
            self.border_widget.setGeometry(QRect(0, 0, self.frame_width, self.frame_height))
            self.border_widget.setVisible(False)
            #self.border_widget.setAttribute(Qt.WA_TransparentForMouseEvents)
            # not listen anything in self.border_widget
            self.border_widget.setMouseTracking(False)
            self.border_widget.setAcceptDrops(False)
            self.installEventFilter(self.border_widget)

        self.animation_label = None
        if self.is_tracking_item:
            self.animation_label = AnimationBorderQLabel(self)
            self.animation_label.setVisible(False)
            self.animation_label.setMouseTracking(False)
            self.animation_label.setAcceptDrops(False)
            self.animation_label.setContentsMargins(0, 0, 0, 0)
            self.animation_label.setObjectName("animation_label")
            self.animation_label.setGeometry(QRect(0, 0, self.frame_width, self.frame_height))
            # self.border_widget.setStyleSheet(Style.StyleSheet.item_focused)


        self.position_widget = QLabel(self)
        self.position_widget.setAlignment(Qt.AlignmentFlag.AlignTop|Qt.AlignmentFlag.AlignCenter)
        self.position_widget.setStyleSheet(Style.PrimaryStyleSheet.get_camera_grid_item_normal_style(main_controller))
        self.position_widget.setGeometry(0, 0, self.frame_width, self.frame_height)
        self.position_widget.setVisible(False)
        self.position_widget.setAttribute(Qt.WA_TransparentForMouseEvents)
        # not listen anything in self.border_widget
        self.position_widget.setMouseTracking(False)
        self.position_widget.setAcceptDrops(False)

    def update_resize(self, width, height):
        self.frame_width = width
        self.frame_height = height
        if self.border_widget is not None:
            self.border_widget.setGeometry(0, 0, self.frame_width, self.frame_height)
        self.position_widget.setGeometry(0, 0, self.frame_width, self.frame_height)
        if self.animation_label is not None:
            self.animation_label.setGeometry(0, 0, self.frame_width, self.frame_height)

        
    def add_widget(self,widget = None):
        if widget is not None:
            self.main_widget = widget
            if isinstance(widget, BackgroundWidget):
                self.background_widget = widget
            self.addWidget(self.main_widget)
            if widget == grid_item_selected.data['widget']:
                grid_item_selected.widget_changed.emit((widget))
                if self.border_widget is not None:
                    self.border_widget.setVisible(True)
                    self.border_widget.setStyleSheet(Style.StyleSheet.item_focused)
            # set self.border_widget always on top
            if self.border_widget is not None:
                self.border_widget.raise_()
            self.position_widget.raise_()

            return widget
        else:
            self.background_widget = BackgroundWidget(stack_item=self,index=self.index)
            self.main_widget = self.background_widget
            self.addWidget(self.main_widget)
            
            # set self.border_widget always on top
            if self.border_widget is not None:
                self.border_widget.raise_()
            self.position_widget.raise_()
            
            return self.background_widget
        
    def load_widget(self,widget):
        is_grid_selected = False
        
        if self.count() == 1:
            old_widget = self.widget(0)
            if old_widget == grid_item_selected.data['widget']:
                is_grid_selected = True
            if isinstance(old_widget, BackgroundWidget):
                self.background_widget = None
            old_widget.deleteLater()
            self.removeWidget(old_widget)
        # logger.debug(f'virtual = {widget.root_width,widget.root_height}')
        ###################################################################### 
        # do khi mở 1 của sổ virtual window mà dữ liệu grid trong tab_model là loại Custom_Grid (8,10,13 ..)
        # thì xảy ra hiện tượng swap vị trí camera thì gây hiện tượng sê dịch grid 
        # Chưa rõ nguyên nhân như nào lên tạm thời update_resize trước khi addWidget để giải quyết thêm
        if hasattr(widget, 'update_resize'):
            widget.update_resize(self.size().width(),self.size().height())
        ###########################################################################
        self.addWidget(widget)
        
        if is_grid_selected or widget == grid_item_selected.data['widget']:
            grid_item_selected.data['widget'] = widget
            grid_item_selected.widget_changed.emit((widget))
            if self.border_widget is not None:
                self.border_widget.setVisible(True)
                self.border_widget.setStyleSheet(Style.StyleSheet.item_focused)
                self.border_widget.raise_()
        # logger.debug(f'virtual1 = {widget.root_width,widget.root_height}')
    def show_grid_item_index(self,number):
        # self.camera_frame.setText(str(number))
        # self.camera_frame.setStyleSheet(Style.StyleSheet.camera_frame)
        timer = QTimer(self)
        timer.setInterval(5000)  
        timer.setSingleShot(True)
        timer.timeout.connect(self.hide_grid_item_index)
        timer.start()
        key_board_manager.timer_list.append(timer)
        
    def hide_grid_item_index(self):
        # self.background_widget.setText('')
        # self.background_widget.setStyleSheet(Style.StyleSheet.camera_frame_focused)
        pass

    def change_grid_item_index_color(self):
        # self.camera_frame.setStyleSheet(Style.StyleSheet.camera_frame_color)
        pass
        

    # def add_widget(self):
    #     self.addWidget(self.camera_frame)

    # def update_border(self):
    #     index = self.camera_grid_widget.get_index_of_item_from_row_col(self.position.x(),self.position.y())
    #     if grid_item_selected.is_same_positon(index=index):
    #         grid_item_selected.data['widget'] = self
    #         # self.camera_frame.setStyleSheet(Style.StyleSheet.camera_frame_focused)

    def dragEnterEvent(self, event):
        event.acceptProposedAction()
    # def keyPressEvent(self, event):
    #     # print(f'StackFrame = {event.key()}')
    #     super().keyPressEvent(event)
        
    # def keyPressEvent(self, event):
    #     logger.debug(f'keyPressEvent = {event.key()}')

    def dropEvent(self, event):
        mime_data = event.mimeData()
        logger.info(f'dropEvent = {mime_data.text(),mime_data.objectName(),event.source().objectName()}')
        if mime_data.hasText() and (mime_data.objectName() == TreeType.Camera or event.source().objectName() == TreeType.Camera):
            text = mime_data.text()
            event.acceptProposedAction()
            retrieved_byte_array = mime_data.data("application/data")
            logger.info(f'retrieved_byte_array = {retrieved_byte_array}')
            try:
                retrieved_data_dict = pickle.loads(retrieved_byte_array)
            except Exception as e:
                retrieved_data_dict = bytes(retrieved_byte_array).decode('utf-8')
                retrieved_data_dict = json.loads(retrieved_data_dict)

            id = retrieved_data_dict.get('id',None)
            camera_name = text
            model = camera_model_manager.get_camera_model(id = id)
            
            if model is not None:
                controller:Controller = controller_manager.get_controller(server_ip=model.data.server_ip)
                tab_model:TabModel = self.camera_grid_widget.tab_model
                grid_item = tab_model.data.listGridData.get(self.index,None)
                if grid_item.model == model:
                    # case kéo một event trùng nhau
                    return
                if  grid_item is not None:
                    grid_item.type = ItemType.Camera
                    grid_item.row = self.position.x()
                    grid_item.col = self.position.y()
                    grid_item.width = self.frame_width
                    grid_item.height = self.frame_height
                    tab_model.set_model(grid_item = grid_item,model = model)
                    tab_model.add_grid_item_signal.emit(grid_item.index)
                    if tab_model.data.type != TabType.Invalid:
                        tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.DropCamera,'data':{'key': grid_item.index,'camera_id': model.data.id,'server_ip':model.data.server_ip,'row':grid_item.row,'col':grid_item.col}}
                        controller.update_tabmodel_by_put(parent=self, tab=tab_model.data)

        elif mime_data.hasText() and mime_data.objectName() == TreeType.Group:
            data = mime_data.data("application/tracking_group")  # kiem tra item vua keo vao la tracking group
            text = mime_data.text()
            event.acceptProposedAction()
            retrieved_byte_array = mime_data.data("application/data")
            retrieved_data_dict = pickle.loads(retrieved_byte_array)
            group_id = retrieved_data_dict.get('id',None)
            group_model = group_model_manager.get_group_model(id= group_id)
            tab_model = self.camera_grid_widget.tab_model
            if group_model is not None:
                list_camera = []
                controller:Controller = controller_manager.get_controller(server_ip=group_model.data.server_ip)
                for id in group_model.data.cameraIds:
                    camera_model = camera_model_manager.get_camera_model(id=id)
                    if camera_model is not None:
                        list_camera.append(camera_model)
                tab_model.add_group_signal.emit(list_camera)
                if tab_model.data.type != TabType.Invalid:
                    tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.DropGroup,'data':{'id': group_model.data.id}}
                    controller.update_tabmodel_by_put(parent=self, tab=tab_model.data)
        elif mime_data.hasText() and mime_data.objectName() == TreeType.Virtual_Window_Item:
            text = mime_data.text()
            event.acceptProposedAction()
            retrieved_byte_array = mime_data.data("application/data")
            retrieved_data_dict = pickle.loads(retrieved_byte_array)
            id = retrieved_data_dict.get('id',None)
            # logger.debug(f"aaaaaaaaaaa = {id}")
            tab_model = tab_model_manager.get_tab_model(id = id)
            camera_screen = main_controller.list_parent['CameraScreen']
            virtual_window_widget = main_controller.list_parent['MainTreeView']
            item = virtual_window_widget.get_item(name=text,tree_type = TreeType.Virtual_Window_Item,server_ip = tab_model.data.server_ip)
            ######## check co virtual window nao dang mo ##########
            list_screen_index = {}
            for server in virtual_window_widget.tree_data.servers:
                for base_item in server.list_virtual_windows.child_item:
                    if base_item.name in main_controller.list_parent:
                        index = main_controller.list_parent[base_item.model.data.id][0]
                        list_screen_index[index] = base_item.name
            ############## check man hinh #######################
            current_screen = self.screen()
            screens = QApplication.screens()
            screen_index_selected = None
            if len(screens) > 1:
                for screen in screens:
                    # logger.debug(f'current_screen = {current_screen.name()}')
                    # logger.debug(f'screen = {screen.name()}')
                    screen_index = QApplication.screens().index(screen)
                    if screen != current_screen:
                        if screen_index not in list_screen_index:
                            screen_index_selected = screen_index
                    else:
                        pass
                
                ########################################################
                if screen_index_selected is not None:
                    virtual_window_widget.open_to_window_triggered(screen_index_selected,item)
                else:
                    for screen in screens:
                        # logger.debug(f'current_screen = {current_screen.name()}')
                        # logger.debug(f'screen = {screen.name()}')
                        screen_index = QApplication.screens().index(screen)
                        if screen != current_screen:
                            screen_index_selected = screen_index
                            virtual_window_widget.open_to_window_triggered(screen_index_selected,item)
            else:           
                virtual_window_widget.open_to_window_triggered(0,item)

        elif mime_data.hasText() and mime_data.objectName() == TreeType.Saved_View_Item:
            text = mime_data.text()
            event.acceptProposedAction()
            retrieved_byte_array = mime_data.data("application/data")
            retrieved_data_dict = pickle.loads(retrieved_byte_array)
            id = retrieved_data_dict.get('id',None)
            # logger.debug(f"aaaaaaaaaaa = {id}")
            tab_model = tab_model_manager.get_tab_model(id = id)
            camera_screen = main_controller.list_parent['CameraScreen']
            main_treeview_widget = main_controller.list_parent['MainTreeView']
            item = main_treeview_widget.get_item(name=text,tree_type = TreeType.Saved_View_Item,server_ip = tab_model.data.server_ip)
            widget = camera_screen.new_custom_tab_widget.getCurrentWidget()
            if hasattr(widget, 'tab_model'):
                if widget.tab_model.data.type == TabType.VirtualWindow:
                    widget.camera_bottom_toolbar.exit_all_clicked(is_emit = False)
                    # Sao chep dữ liệu trong QSetting khi mở 1 saved view vào virtual window
                    virtual_tab_model = widget.tab_model
                    virtual_tab_model.data.currentGrid= tab_model.data.currentGrid
                    virtual_tab_model.data.listGridCustomData = tab_model.data.listGridCustomData
                    virtual_tab_model.data.listGridData = {}
                    for index_item,grid_item in tab_model.data.listGridData.items():
                        virtual_tab_model.data.listGridData[index_item] = GridItem(index=index_item,type=grid_item.type,row = grid_item.row,col = grid_item.row,width=grid_item.width,height=grid_item.height,model=grid_item.model)

                    widget.update_list_camera()
                    virtual_tab_model.change_grid_view_signal.emit(virtual_tab_model.data.currentGrid)
                    widget.timeout()
                    return
                if widget.tab_model.data.type == TabType.Invalid:
                    # kiểm tra saved này có đang được mở ko, nếu đang dc  mở thì focused sang tab đó luôn
                    for index in range(camera_screen.new_custom_tab_widget.tab_widget.count()):
                        widget = camera_screen.center_stacked_widget.widget(index)
                        if widget.tab_model == tab_model:
                            camera_screen.new_custom_tab_widget.tab_widget.setCurrentIndex(tab_model.data.index)
                            return
                        
                    widget.camera_bottom_toolbar.exit_all_clicked()
                    widget.switch_tab_model(tab_model)

                    widget.update_list_camera()
                    widget.timeout()
                    camera_screen.new_custom_tab_widget.tab_widget.rename_tab(
                                tab_model.data.index, text)
                    if tab_model.data.index == 1:
                        camera_screen.new_custom_tab_widget.tab_widget.setCurrentIndex(0)
                        camera_screen.new_custom_tab_widget.tab_widget.setCurrentIndex(1)
                    item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
                    
                    return 
                       
            # for index in range(camera_screen.new_custom_tab_widget.tab_widget.count()):
            #     tab_name = camera_screen.new_custom_tab_widget.tab_widget.tab_bar.tabText(
            #         index)
            #     widget = camera_screen.center_stacked_widget.widget(index)
            #     if text == tab_name and hasattr(widget, 'tab_type') and widget.tab_type == TabType.SavedView:
            #         camera_screen.new_custom_tab_widget.tab_widget.setCurrentIndex(widget.tab_index)
            #         return
            # item.setIcon(QIcon(Style.PrimaryImage.open_all_virtual))   
            # camera_screen.add_tab_widget(tab_name=text, tab_type= TabType.SavedView)

        elif mime_data.hasText() and mime_data.objectName() == TreeType.FloorItem:
            text = mime_data.text()
            event.acceptProposedAction()
            retrieved_byte_array = mime_data.data("application/data")
            retrieved_data_dict = pickle.loads(retrieved_byte_array)
            id = retrieved_data_dict.get('id',None)
            model = floor_manager.get_floor(id = id) 
            if model is not None:
                controller:Controller = controller_manager.get_controller(server_ip=model.serverIp)
                tab_model:TabModel = self.camera_grid_widget.tab_model
                grid_item = tab_model.data.listGridData.get(self.index,None)
                if grid_item.model == model:
                    # case kéo một event trùng nhau
                    return
                if  grid_item is not None:
                    grid_item.type = ItemType.Floor
                    grid_item.row = self.position.x()
                    grid_item.col = self.position.y()
                    grid_item.width = self.frame_width
                    grid_item.height = self.frame_height
                    tab_model.set_model(grid_item = grid_item,model = model)
                    tab_model.add_grid_item_signal.emit(grid_item.index)
                    if tab_model.data.type != TabType.Invalid:
                        tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.DropFloor,'data':{'key': grid_item.index,'floor_id': model.id,'server_ip':model.serverIp,'row':grid_item.row,'col':grid_item.col}}
                        controller.update_tabmodel_by_put(parent=self, tab=tab_model.data)

        elif mime_data.hasText() and mime_data.objectName() == TreeType.List_Map:
            text = mime_data.text()
            event.acceptProposedAction()
            retrieved_byte_array = mime_data.data("application/data")
            retrieved_data_dict = pickle.loads(retrieved_byte_array)
            id = retrieved_data_dict.get('id',None)
            model = map_manager.get_map_model(id = id)
            if model is not None:
                controller:Controller = controller_manager.get_controller(server_ip=model.serverIp)
                tab_model:TabModel = self.camera_grid_widget.tab_model
                grid_item = tab_model.data.listGridData.get(self.index,None)
                if grid_item.type == ItemType.MapOSM:
                    # case.keo mot event trung nhau
                    return
                if grid_item is not None:
                    grid_item.type = ItemType.MapOSM
                    grid_item.row = self.position.x()
                    grid_item.col = self.position.y()
                    grid_item.width = self.frame_width
                    grid_item.height = self.frame_height
                    grid_item.server_ip = main_controller.current_controller.server.data.server_ip
                    tab_model.set_model(grid_item = grid_item,model=model)
                print(f'List_Map = {grid_item.to_dict()}')
                tab_model.add_grid_item_signal.emit(grid_item.index)
                if tab_model.data.type != TabType.Invalid:
                    tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.DropMap,'data':{'key': grid_item.index,'map_id': model.id,'server_ip':model.serverIp,'row':grid_item.row,'col':grid_item.col}}
                    controller.update_tabmodel_by_put(parent=self, tab=tab_model.data)

        elif mime_data.hasText() and (mime_data.objectName() == "swap_item" or mime_data.text() == "swap_item"):
            logger.info(f'swap_item')
            text = mime_data.text()
            event.acceptProposedAction()
            # camera_name = text
            # width = self.frame_width
            # height = self.frame_height
            row = self.position.x()
            col = self.position.y()
            # mime_object_name = mime_data.objectName()
            data = mime_data.data("application/position")
            # retrieved_data_list = pickle.loads(data)
            try:
                retrieved_data_list = pickle.loads(data)
            except Exception as e:
                retrieved_data_list = bytes(data).decode('utf-8')
                retrieved_data_list = json.loads(retrieved_data_list)
            logger.info(f'retrieved_data_list = {retrieved_data_list}')
            retrieved_row, retrieved_col = retrieved_data_list.get("position")
            # value = (camera_name, row, col, width, height, mime_object_name, retrieved_row, retrieved_col, self)
            # print(f'dropEvent = {retrieved_row} {retrieved_col} -> {row} {col}')
            new_index = self.camera_grid_widget.get_index_of_item_from_row_col(row, col)
            old_index = self.camera_grid_widget.get_index_of_item_from_row_col(retrieved_row, retrieved_col)
            if new_index != old_index:
                tab_model:TabModel = self.camera_grid_widget.tab_model
                controller:Controller = controller_manager.get_controller(server_ip=tab_model.data.server_ip)
                new_grid_item = tab_model.data.listGridData.get(new_index,None)
                old_grid_item = tab_model.data.listGridData.get(old_index,None)
                # logger.debug(f"aaaa = {new_index,old_index}")
                new_grid_item.index = old_index
                new_grid_item.row = retrieved_row
                new_grid_item.col = retrieved_col
                old_grid_item.index = new_index
                old_grid_item.row = row
                old_grid_item.col = col
                tab_model.data.listGridData[new_index] = old_grid_item
                tab_model.data.listGridData[old_index] = new_grid_item
                tab_model.swap_grid_item_signal.emit((new_index,old_index))
                
                if tab_model.data.type != TabType.Invalid:
                    tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.SwapItem,'data':{'newkey': new_index,'oldkey': old_index,'new_row':retrieved_row,'new_col':retrieved_col}}
                    controller.update_tabmodel_by_put(parent=self, tab=tab_model.data)

        elif mime_data.hasText() and mime_data.objectName() == "Multi_selection_item":
            mime_text = mime_data.text()
            text_list = mime_text.split('\n')
            event.acceptProposedAction()
            retrieved_byte_array = mime_data.data("application/multidata")
            retrieved_data_dict = pickle.loads(retrieved_byte_array)
            logger.debug(f'Multi_selection_item = {retrieved_data_dict}')
            list_camera = []
            tab_model = self.camera_grid_widget.tab_model
            controller:Controller = controller_manager.get_controller(server_ip=tab_model.data.server_ip)

            for id, tree_type in retrieved_data_dict.items():
                if tree_type == TreeType.Camera:
                    camera_model = camera_model_manager.get_camera_model(id = id)
                    if camera_model is not None and camera_model not in list_camera:
                        list_camera.append(camera_model)
                elif tree_type == TreeType.Group:
                    group_model = group_model_manager.get_group_model(id = id)
                    if group_model is not None:
                        for camera_id in group_model.data.cameraIds:
                            camera_model = camera_model_manager.get_camera_model(id = camera_id)
                            if camera_model is not None and camera_model not in list_camera:
                                list_camera.append(camera_model)
            # self.drop_group_signal.emit(list_camera)                    
            # tab_model.add_group_signal.emit(list_camera)
            # # tạm thời xử lý sau với case này
            # if tab_model.data.type != TabType.Invalid:
            #     tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.DropGroup,'data':{'name': text}}
            #     controller.update_tabmodel_by_put(parent=self, tab=tab_model.data)

            tab_model.add_group_signal.emit(list_camera)
            if tab_model.data.type != TabType.Invalid:
                tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.MultipleSelection,'data':{'id': None}}
                controller.update_tabmodel_by_put(parent=self, tab=tab_model.data)

        elif mime_data.hasText() and mime_data.objectName() == 'event':
            text = mime_data.text()
            event.acceptProposedAction()
            # logger.debug(f'event = {text}')
            model = event_manager.get_event(text)
            # logger.debug(f'event = {model}')

            if model is not None:
                tab_model:TabModel = self.camera_grid_widget.tab_model
                grid_item = tab_model.data.listGridData.get(self.index,None)
                if grid_item.model == model:
                    # case kéo một event trùng nhau
                    return
                if  grid_item is not None:
                    grid_item.type = ItemType.Event
                    grid_item.row = self.position.x()
                    grid_item.col = self.position.y()
                    grid_item.width = self.frame_width
                    grid_item.height = self.frame_height
                    tab_model.set_model(grid_item = grid_item,model = model)
                    tab_model.add_grid_item_signal.emit(grid_item.index)
        event.accept()

    def dragMoveEvent(self, event):
        event.accept()

    def dragLeaveEvent(self, event):
        event.accept()

    # create function allow copy this widget to new
    def clone(self):
        # camera_frame = QLabel()
        # camera_frame.setContentsMargins(0, 0, 0, 0)
        # camera_frame.setStyleSheet(Style.StyleSheet.camera_frame)
        # camera_frame.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        clone_stack_frame = StackFrame(self.camera_grid_widget, self.frame_width, self.frame_height, self.position)
        clone_stack_frame.screen_name = self.screen_name
        clone_stack_frame.resizeEvent = lambda event: self.camera_grid_widget.item_resize_event(clone_stack_frame, event)
        clone_stack_frame.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        clone_stack_frame.add_widget()
        # clone_stack_frame.drop_group_signal.connect(self.camera_grid_widget.on_drop_group)
        # clone_widget.addWidget(camera_frame)
        return clone_stack_frame
    

    def addItemToStack(self, widget):
        current_index = self.currentIndex()
        new_index = current_index + 1
        if self.count() == 2:
            # Remove the current widget at the current index    
            self.removeWidget(self.widget(current_index))
            self.addWidget(widget)    
            self.setCurrentIndex(current_index)
        else:
            # Set the newly added widget as the current widget
            self.addWidget(widget)
            self.setCurrentIndex(new_index)

    def mousePressEvent(self, event: QMouseEvent) -> None:
        if event.button() == Qt.LeftButton:
            # if MouseButtonPress
            if event.type() == QMouseEvent.Type.MouseButtonPress:
                current_tab = main_controller.current_tab
                self.grid_item_clicked(current_tab=current_tab)
            elif event.type() == QMouseEvent.Type.MouseButtonDblClick:
                # forward mouse event to child
                self.currentWidget().mouseDoubleClickEvent(event)
        return super().mousePressEvent(event)
    
    def swap_item(self,data):
            row = self.position.x()
            col = self.position.y()
            retrieved_row, retrieved_col = data.get("position")
            new_index = self.camera_grid_widget.get_index_of_item_from_row_col(row, col)
            old_index = self.camera_grid_widget.get_index_of_item_from_row_col(retrieved_row, retrieved_col)
            if new_index != old_index:
                tab_model:TabModel = self.camera_grid_widget.tab_model
                controller:Controller = controller_manager.get_controller(server_ip=tab_model.data.server_ip)
                new_grid_item = tab_model.data.listGridData.get(new_index,None)
                old_grid_item = tab_model.data.listGridData.get(old_index,None)
                # logger.info(f"aaaa = {new_index,old_index}")
                new_grid_item.index = old_index
                new_grid_item.row = retrieved_row
                new_grid_item.col = retrieved_col
                old_grid_item.index = new_index
                old_grid_item.row = row
                old_grid_item.col = col
                tab_model.data.listGridData[new_index] = old_grid_item
                tab_model.data.listGridData[old_index] = new_grid_item
                tab_model.swap_grid_item_signal.emit((new_index,old_index))
                
                # if tab_model.data.type != TabType.Invalid:
                #     tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.SwapItem,'data':{'newkey': new_index,'oldkey': old_index,'new_row':retrieved_row,'new_col':retrieved_col}}
                #     controller.update_tabmodel_by_put(parent=self, tab=tab_model.data)

    def grid_item_clicked(self, current_tab = None):
        current_widget = self.currentWidget()
        if grid_item_selected.data['tab_index'] is not None:
            # case trước đó đang được focus vào đâu đó của màn hình chính
            if not grid_item_selected.is_tab_index(current_tab):
                # case vị trí focus trước đó khác tab
                grid_item_selected.set_data(screen=self.screen_name,tab_index=current_tab,widget=current_widget)
                if self.border_widget is not None:
                    self.border_widget.setVisible(True)
                    self.border_widget.setStyleSheet(Style.StyleSheet.item_focused)
            else:
                # case vị trí focus trước đó trùng tab
                if grid_item_selected.is_same_widget(current_widget):
                    # case vị trí focus trước đó và hiện tại trùng nhau
                    return
                else:
                    # case vị trí focus trước đó và hiện tại khác nhau thì cần xử lý unclick
                    widget = grid_item_selected.data['widget']
                    if widget is not None and hasattr(widget.stack_item, 'grid_item_unclicked'):
                        widget.stack_item.grid_item_unclicked()
                    grid_item_selected.set_data(screen=self.screen_name,tab_index=current_tab,widget=current_widget)
                    if self.border_widget is not None:
                        self.border_widget.setVisible(True)
                        self.border_widget.setStyleSheet(Style.StyleSheet.item_focused)
        else:
            # case trước đó đang không focus vào màn hình chính, nhưng có thể hoặc đang không focus vào virtual window
            widget = grid_item_selected.data['widget']
            if widget is not None:
                # case trước đó đang focus vào ví trí camera nào đó trong virtual-> cần xử lý unclick
                if widget is not None and hasattr(widget.stack_item, 'grid_item_unclicked'):
                    widget.stack_item.grid_item_unclicked()
                grid_item_selected.set_data(screen=self.screen_name,tab_index=current_tab,widget=current_widget)
                if self.border_widget is not None:
                    self.border_widget.setVisible(True)
                    self.border_widget.setStyleSheet(Style.StyleSheet.item_focused)
            else:
                # case trước đó đang không focus vào đâu
                grid_item_selected.set_data(screen=self.screen_name,tab_index=current_tab,widget=current_widget)
                if self.border_widget is not None:
                    self.border_widget.setVisible(True)
                    self.border_widget.setStyleSheet(Style.StyleSheet.item_focused)
        if self.border_widget is not None:
            self.border_widget.raise_()
            self.grid_item_clicked_signal.emit((self.currentWidget(), self.border_widget.isVisible()))
        grid_item_selected.widget_changed.emit(self.currentWidget())

    def grid_item_unclicked(self):
        if self.border_widget is not None:
            # Update style
            self.border_widget.setStyleSheet(Style.StyleSheet.camera_frame)
            self.border_widget.setVisible(False)
        grid_item_selected.set_data(screen = None,type = None,tab_index = None,camera_id = None, widget = None)
        if self.border_widget is not None:
            self.grid_item_clicked_signal.emit((self.currentWidget(),not self.border_widget.isVisible()))
        grid_item_selected.widget_changed.emit(None)  
          
    def show_grid_item_index(self,number):
        self.position_widget.setText(str(number))
        self.position_widget.setStyleSheet(Style.PrimaryStyleSheet.get_camera_grid_item_normal_style(main_controller))
        self.position_widget.setVisible(True)
        self.position_widget.raise_()
        timer = QTimer(self)
        timer.setInterval(5000)  
        timer.setSingleShot(True)
        timer.timeout.connect(self.hide_grid_item_index)
        timer.start()
        key_board_manager.timer_list.append(timer)
        
    def hide_grid_item_index(self):
        self.position_widget.setText('')
        self.position_widget.setVisible(False)
        # self.background_widget.setStyleSheet(Style.StyleSheet.camera_frame_focused)
        pass

    def change_grid_item_index_color(self):
        self.position_widget.setStyleSheet(Style.PrimaryStyleSheet.get_camera_grid_item_hover_style(main_controller))
        pass

    def start_border_animation(self):
        if self.animation_label is not None:
            self.animation_label.setVisible(True)
            self.animation_label.raise_()
            self.animation_label.startFadeInAnimation()

    def stop_border_animation(self):
        if self.animation_label is not None:
            self.animation_label.resetToDefaultColor()
            self.animation_label.stopAllAnimation()
            self.animation_label.setVisible(False)

    def start_next_border_animation(self):
        if self.animation_label is not None:
            # expected other color
            self.animation_label.setVisible(True)
            self.animation_label.setAnimationColor(QColor(0, 255, 0))  # Set green animation
            self.animation_label.raise_()
            self.animation_label.startFadeInAnimation()

    def closeEvent(self, event):
        if self.border_widget is not None:
            self.border_widget.close()
            self.border_widget.deleteLater()
        if self.animation_label is not None:
            self.animation_label.close()
            self.animation_label.deleteLater()

    def handle_qml_drop_event(self, data):
        row = self.position.x()
        col = self.position.y()
        retrieved_data_list = pickle.loads(data.data())
        retrieved_row, retrieved_col = retrieved_data_list
        # value = (camera_name, row, col, width, height, mime_object_name, retrieved_row, retrieved_col, self)
        # print(f'dropEvent = {retrieved_row} {retrieved_col} -> {row} {col}')
        new_index = self.camera_grid_widget.get_index_of_item_from_row_col(row, col)
        old_index = self.camera_grid_widget.get_index_of_item_from_row_col(retrieved_row, retrieved_col)
        if new_index != old_index:
            tab_model:TabModel = self.camera_grid_widget.tab_model
            controller:Controller = controller_manager.get_controller(server_ip=tab_model.data.server_ip)
            new_grid_item = tab_model.data.listGridData.get(new_index,None)
            old_grid_item = tab_model.data.listGridData.get(old_index,None)
            # logger.debug(f"aaaa = {new_index,old_index}")
            new_grid_item.index = old_index
            new_grid_item.row = retrieved_row
            new_grid_item.col = retrieved_col
            old_grid_item.index = new_index
            old_grid_item.row = row
            old_grid_item.col = col
            tab_model.data.listGridData[new_index] = old_grid_item
            tab_model.data.listGridData[old_index] = new_grid_item
            tab_model.swap_grid_item_signal.emit((new_index,old_index))
            
            if tab_model.data.type != TabType.Invalid:
                tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.SwapItem,'data':{'newkey': new_index,'oldkey': old_index,'new_row':retrieved_row,'new_col':retrieved_col}}
                controller.update_tabmodel_by_put(parent=self, tab=tab_model.data)
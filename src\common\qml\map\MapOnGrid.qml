import QtQuick 
import QtQuick.Controls 
import QtQuick.Layouts
import QtLocation
import QtPositioning
import CustomComponents 1.0
Rectangle {
    id: root
    property bool isLongPress: false
    anchors.fill: parent
    property var cameraList: []
    property int previewWidth: 500
    property int previewHeight: 340
    property var currentMimeData: null
    Loader {
        id: cameraDetailLoader
        width: previewWidth
        height: previewHeight
        visible: source !== ""
        z: 100
    }
    // Plugin {
    //     id: mapPlugin
    //     name: "osm"
    //     PluginParameter {
    //         name: "osm.mapping.providersrepository.disabled"
    //         value: "true"
    //     }
    //     PluginParameter {
    //         name: "osm.cache.directory"
    //         value: ""
    //     }
    //     PluginParameter {
    //         name: "osm.cache.disk.cost"
    //         value: "0"
    //     }
    //     PluginParameter {
    //         name: "osm.cache.memory.cost"
    //         value: "0"
    //     }
    // }

    // ConfirmDialog {
    //     id: createDialog
    //     z: 9999
    //     onCreateBuilding: {
    //         console.log("Creating building:", buildingName)
    //         // Gọi controller xử lý tạo building ở đây
    //         createDialog.close()
    //     }
    //     onCancel: createDialog.close()
    // }
    
    DropArea {
        anchors.fill: parent
        onEntered: (drag) => {
            if(!mapState.editMode && !mapState.lockMode){
                drag.accepted = false;
            }
        }

        onDropped: function(drop) {
            if (mapState.lockMode){
                mapState.dropEventChanged(drop.getDataAsArrayBuffer("application/position"))
            }else if (mapState.editMode) {
                let coord = map.toCoordinate(Qt.point(drop.x, drop.y))
                mapModel.handleDrop(drop.getDataAsArrayBuffer("application/data"),coord.latitude,coord.longitude)
            }

        }
    }
    
    Map {
        id: map
        anchors.fill: parent
        plugin: Plugin {
            id: mapPlugin
            name: "osm"
            PluginParameter {
                name: "osm.mapping.custom.host"
                value: "http://**************:8080/styles/basic-preview/"
            }
            // PluginParameter {
            //     name: "osm.mapping.offline.directory"
            //     value: "e:/Project/python/VMS-Training/qml_reconstruct/cache"
            // }
            // PluginParameter {
            //     name: "osm.mapping.cache.directory"
            //     value: "e:/Project/python/VMS-Training/qml_reconstruct/cache"
            // }
            PluginParameter {
                name: "osm.cache.directory"
                value: ""
            }
            PluginParameter {
                name: "osm.cache.disk.cost"
                value: "0"
            }
            PluginParameter {
                name: "osm.cache.memory.cost"
                value: "0"
            }
        }

        zoomLevel: 14
        center: QtPositioning.coordinate(21.014506, 105.846509)
        activeMapType: supportedMapTypes[supportedMapTypes.length - 1]
        property geoCoordinate startCentroid
        antialiasing: true
        MapItemView {
            model: (function(){
                    return mapModel.cameraIds
                })()
            delegate: MapItem {
                id: cameraItem
                model: (function(){
                    return modelData
                })()
                coordinate: QtPositioning.coordinate(modelData.latitude, modelData.longitude)
                rtsp: modelData.urlMainstream
                itemId: modelData.id
                itemName: modelData.name
                camLocation: modelData.location
                onUntrackSignal: (cameraModel) => {
                    mapModel.removeCameraFromMap(cameraModel)
                }
                onButtonSignal: (id) => {
                    if (map.width < previewWidth) {
                        mapState.notifyChanged(MapState.SelectGridType)
                        return
                    }
                    if (cameraItem.x < 0) {
                        cameraDetailLoader.x = 0
                    }else{
                        if ((cameraItem.x + previewWidth + 30) > map.width){
                            cameraDetailLoader.x = map.width - previewWidth - 30
                        }else{
                            cameraDetailLoader.x = cameraItem.x + 30;
                        }
                    }
                    if (cameraItem.y < 0) {
                        cameraDetailLoader.y = 0
                    }else {
                        if ((cameraItem.y + previewHeight + 30) > map.height){
                            cameraDetailLoader.y = map.height - previewHeight - 30
                        }else{
                            cameraDetailLoader.y = cameraItem.y + 30;
                        }
                    }
                    cameraDetailLoader.sourceComponent = previewItemComponent
                }
                Component {
                    id: previewItemComponent
                    PreviewItem {
                        id: previewItem
                        model: cameraItem.model
                        buttonType: "Camera"
                        isPlayingStream: true
                        visible: true
                        itemName: cameraItem.itemName
                        onCloseSignal: {
                            cameraDetailLoader.sourceComponent = null;
                            cameraDetailLoader.width = previewWidth
                            cameraDetailLoader.height = previewHeight
                            mapState.viewMode = false
                        }
                        onFullScreenSignal: {
                            mapState.viewMode = !mapState.viewMode
                            if (mapState.viewMode){
                                cameraDetailLoader.width = map.width
                                cameraDetailLoader.height = map.height
                                cameraDetailLoader.x = 0
                                cameraDetailLoader.y = 0
                            }else{
                                cameraDetailLoader.width = previewWidth
                                cameraDetailLoader.height = previewHeight
                                if (cameraItem.x < 0) {
                                    cameraDetailLoader.x = 0
                                }else{
                                    if ((cameraItem.x + previewWidth + 30) > map.width){
                                        cameraDetailLoader.x = map.width - previewWidth - 30
                                    }else{
                                        cameraDetailLoader.x = cameraItem.x + 30;
                                    }
                                }
                                if (cameraItem.y < 0) {
                                    cameraDetailLoader.y = 0
                                }else {
                                    if ((cameraItem.y + previewHeight + 30) > map.height){
                                        cameraDetailLoader.y = map.height - previewHeight - 30
                                    }else{
                                        cameraDetailLoader.y = cameraItem.y + 30;
                                    }
                                }
                            }
                        }
                    }
                }

            }
        }
        MapItemView {
            model: mapModel.buildingIds
            delegate: MapItem {
                id: buildingItem
                model: modelData
                itemType: "Building"
                itemId: modelData.id
                itemName: modelData.name
                coordinate: QtPositioning.coordinate(modelData.latitude, modelData.longitude)
                onUntrackSignal: (buildingModel) => {
                    mapModel.removeBuildingFromMap(buildingModel)
                }
                onButtonSignal: (buildingModel) => {
                    if (map.width < previewWidth) {
                        mapState.notifyChanged(MapState.SelectGridType)
                        return
                    }
                    if (buildingItem.x < 0) {
                        cameraDetailLoader.x = 0
                    }else{
                        if ((buildingItem.x + previewWidth + 30) > map.width){
                            cameraDetailLoader.x = map.width - previewWidth - 30
                        }else{
                            cameraDetailLoader.x = buildingItem.x + 30;
                        }
                    }
                    if (buildingItem.y < 0) {
                        cameraDetailLoader.y = 0
                    }else {
                        if ((buildingItem.y + previewHeight + 30) > map.height){
                            cameraDetailLoader.y = map.height - previewHeight - 30
                        }else{
                            cameraDetailLoader.y = buildingItem.y + 30;
                        }
                    }
                    cameraDetailLoader.sourceComponent = previewItemComponent
                }
                Component {
                    id: previewItemComponent
                    PreviewItem {
                        id: previewItem
                        model: buildingItem.model
                        buttonType: "Building"
                        visible: true
                        itemName: buildingItem.itemName
                        onCloseSignal: {
                            cameraDetailLoader.sourceComponent = null;
                            cameraDetailLoader.width = previewWidth
                            cameraDetailLoader.height = previewHeight
                            mapState.viewMode = false
                        }
                        onFullScreenSignal: {
                            mapState.viewMode = !mapState.viewMode
                            if (mapState.viewMode){
                                cameraDetailLoader.width = map.width
                                cameraDetailLoader.height = map.height
                                cameraDetailLoader.x = 0
                                cameraDetailLoader.y = 0
                            }else{
                                cameraDetailLoader.width = previewWidth
                                cameraDetailLoader.height = previewHeight
                                if (buildingItem.x < 0) {
                                    cameraDetailLoader.x = 0
                                }else{
                                    if ((buildingItem.x + previewWidth + 30) > map.width){
                                        cameraDetailLoader.x = map.width - previewWidth - 30
                                    }else{
                                        cameraDetailLoader.x = buildingItem.x + 30;
                                    }
                                }
                                if (buildingItem.y < 0) {
                                    cameraDetailLoader.y = 0
                                }else {
                                    if ((buildingItem.y + previewHeight + 30) > map.height){
                                        cameraDetailLoader.y = map.height - previewHeight - 30
                                    }else{
                                        cameraDetailLoader.y = buildingItem.y + 30;
                                    }
                                }
                            }

                            
                        }
                    }
                }
                
            }
        }

        Behavior on center {
            PropertyAnimation {
                duration: 400
                easing.type: Easing.InOutQuad
            }
        }

        PinchHandler {
            id: pinch
            enabled: mapState ? !mapState.lockMode : false
            target: null
            onActiveChanged: if (active) {
                map.startCentroid = map.toCoordinate(pinch.centroid.position, false)
            }
            onScaleChanged: (delta) => {
                map.zoomLevel += Math.log2(delta)
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            onRotationChanged: (delta) => {
                map.bearing -= delta
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            grabPermissions: PointerHandler.TakeOverForbidden 
        }
        WheelHandler {
            id: wheel
            enabled: mapState ? !mapState.lockMode : false
            grabPermissions: PointerHandler.ApprovesCancellation
            acceptedDevices: Qt.platform.pluginName === "cocoa" || Qt.platform.pluginName === "wayland"
                             ? PointerDevice.Mouse | PointerDevice.TouchPad
                             : PointerDevice.Mouse
            rotationScale: 1 /120
            property: "zoomLevel"
        }
        DragHandler {
            id: drag
            target: null
            enabled: mapState ? !mapState.lockMode : false
            grabPermissions: PointerHandler.TakeOverForbidden 
            onTranslationChanged: (delta) => map.pan(-delta.x, -delta.y)
        }


        Shortcut {
            enabled: map.zoomLevel < map.maximumZoomLevel
            sequence: StandardKey.ZoomIn
            onActivated: map.zoomLevel = Math.round(map.zoomLevel + 1)
        }
        Shortcut {
            enabled: map.zoomLevel > map.minimumZoomLevel
            sequence: StandardKey.ZoomOut
            onActivated: map.zoomLevel = Math.round(map.zoomLevel - 1)
        }
    }
    DragHandler {
        id: dragHandler
        enabled: (mapState && mapState.lockMode) ? true : false
        onActiveChanged: {
            if (active) {
                root.currentMimeData = {
                    "text/plain": "swap_item",
                    "application/position": JSON.stringify({
                        id: mapModel.id,
                        tree_type: "",
                        position: widget.getPosition()
                    })
                }
            }
        }
    }


    Drag.source: root
    Drag.active: dragHandler.active
    Drag.mimeData: root.currentMimeData
    // Drag.mimeData: {
    //     "text/plain": "swap_item",
    //     "application/position": JSON.stringify({
    //         id: mapModel.id,
    //         tree_type: "",
    //         position: widget.getPosition()
    //     })
    // }

    Drag.dragType: Drag.Automatic
    Drag.supportedActions: Qt.MoveAction

    Connections {
        target: mapModel
        function onNewCameraChanged(camera) {
            cameraList.push(camera)
        }
    }
    Component.onCompleted: {
        console.log("QML da load xong! ",mapModel.id)
    }
}
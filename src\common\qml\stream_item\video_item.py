from PySide6.QtQuick import QQuickPaintedItem
from PySide6.QtCore import Property, Signal, Slot, Qt
from PySide6.QtGui import QPainter, QPixmap

class VideoItem(QQuickPaintedItem):
    clicked = Signal()
    doubleClicked = Signal()
    rightClicked = Signal()
    frameCountChanged = Signal(int)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._pixmap = QPixmap()
        self._position = -1
        self._is_playing = False
        self._frame_count = 0
        
        # Enable mouse events
        self.setAcceptedMouseButtons(Qt.LeftButton | Qt.RightButton)
        self.setAcceptHoverEvents(True)
        
    def paint(self, painter: QPainter):
        if self._pixmap.isNull():
            return
            
        # Scale pixmap to fit while maintaining aspect ratio
        target_rect = self.boundingRect()
        scaled_pixmap = self._pixmap.scaled(
            int(target_rect.width()),
            int(target_rect.height()),
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )
        
        # Center the pixmap
        x = (target_rect.width() - scaled_pixmap.width()) / 2
        y = (target_rect.height() - scaled_pixmap.height()) / 2
        painter.drawPixmap(x, y, scaled_pixmap)

    @Slot(QPixmap)
    def updateFrame(self, pixmap):
        self._pixmap = pixmap
        self._frame_count += 1
        self.frameCountChanged.emit(self._frame_count)
        self.update()

    def mousePressEvent(self, event):
        if event.button() == Qt.RightButton:
            self.rightClicked.emit()
        else:
            self.clicked.emit()

    def mouseDoubleClickEvent(self, event):
        self.doubleClicked.emit()

    @Property(int)
    def position(self):
        return self._position

    @position.setter
    def position(self, pos):
        print(f"CustomVideo position changed from {self._position} to {pos}")
        self._position = pos
    @Property(bool)
    def isPlaying(self):
        return self._is_playing

    @isPlaying.setter
    def isPlaying(self, playing):
        if self._is_playing != playing:
            self._is_playing = playing
            if not playing:
                self._pixmap = QPixmap()
                self._frame_count = 0
                self.update()

    @Property(int, notify=frameCountChanged)
    def frameCount(self):
        return self._frame_count
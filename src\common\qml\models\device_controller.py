from PySide6.QtWidgets import QDialog
from PySide6.QtCore import QObject, Slot
from enum import IntEnum, auto
from PySide6.QtCore import Qt, QAbstractListModel, QModelIndex
from PySide6.QtCore import QObject, Property, Slot
from PySide6.QtCore import QObject,Property
import enum
from src.common.controller.main_controller import main_controller
from src.common.widget.dialogs.camera_info_dialog import CameraInfoDialog
from src.common.model.camera_model import CameraModel,camera_model_manager
from src.common.model.group_model import GroupModel, group_model_manager
from src.common.widget.dialogs.warning_dialog import WarningDialog
from src.common.widget.dialogs.ai_box_dialog import AIBoxDialog
from src.common.widget.dialogs.add_group_dialog import AddGroupDialog
from src.common.model.aiflows_model import aiflow_model_manager,AiFlowModel
from src.presentation.device_management_screen.widget.content_add_script_ai_dialog import ContentAddUpdate<PERSON><PERSON><PERSON>ialog
from src.presentation.device_management_screen.widget.ai_state import AIFlowType
from src.common.model.door_model import DoorModel,door_model_manager
# from src.common.widget.dialogs.add_integrated_device_dialog import AddIntegratedDeviceDialog
from src.common.widget.notifications.listen_message_notifications import listen_show_notification
import logging
logger = logging.getLogger(__name__)

class AIState(enum.Enum):
    NODATA = 0
    AIOFF = 1
    AION = 2

class ModelType:
    Group = "Group"
    AIBox = "AIBox"
    Camera = "Camera"
    Door = "Door"

class ItemRoles(IntEnum):
    ID = Qt.UserRole
    TYPE = auto()
    NAME = auto()
    BRANCH = auto()
    MODEL = auto()
    IPADDRESS = auto()
    MACADDRESS = auto()
    PARTNER = auto()
    GROUP = auto()
    CHILD = auto()
    RECOGNITION = auto()
    PROTECTION = auto()
    FREQUENCY = auto()
    ACCESS = auto()
    MOTION = auto()
    TRAFFIC = auto()
    WEAPON = auto()
    UFO = auto()
    RECOGNITION_PROTECTION = auto()
    RISK_IDENTIFICATION = auto()


_role_names = {
    ItemRoles.ID: b'id_model',
    ItemRoles.TYPE: b'type',
    ItemRoles.NAME: b'name',
    ItemRoles.BRANCH: b'branch',
    ItemRoles.MODEL: b'device_model',
    ItemRoles.IPADDRESS: b'ipaddress',
    ItemRoles.MACADDRESS: b'macaddress',
    ItemRoles.PARTNER: b'partner',
    ItemRoles.GROUP: b'group',
    ItemRoles.CHILD: b'child',
    ItemRoles.RECOGNITION: b'recognition',
    ItemRoles.PROTECTION: b'protection',
    ItemRoles.FREQUENCY: b'frequency',
    ItemRoles.ACCESS: b'access',
    ItemRoles.MOTION: b'motion',
    ItemRoles.TRAFFIC: b'traffic',
    ItemRoles.WEAPON: b'weapon',
    ItemRoles.UFO: b'ufo',
    ItemRoles.RECOGNITION_PROTECTION: b'recognition_protection',
    ItemRoles.RISK_IDENTIFICATION: b'risk_identification',
}

class ListModel(QAbstractListModel):
    def __init__(self):
        super().__init__()
        self._data = []
        self.list_model = {}
        
    def clear_data(self):
        for id,model in self.list_model.items():
            if isinstance(model,CameraModel):
                model.change_model.disconnect(self.change_model)
                model.add_ai_flow.disconnect(self.add_ai_flow)
            elif isinstance(model,GroupModel):
                model.change_model.disconnect(self.change_model)
            elif isinstance(model,DoorModel):
                model.change_model.disconnect(self.change_model)
        for list_data in self._data:
            if list_data[ItemRoles.CHILD] is not None:
                list_data[ItemRoles.CHILD].clear_data()

    def roleNames(self):
        return _role_names

    def rowCount(self, parent=QModelIndex()):
        return len(self._data)

    def data(self, index, role):
        if role not in list(ItemRoles):
            return None

        try:
            item = self._data[index.row()]
        except IndexError:
            logger.warning(f"Index out of range: {index.row()}, data length: {len(self._data)}")
            return None

        if role in item:
            return item[role]

        return None
    
    def removeItem(self, index):
        if 0 <= index < len(self._data):
            self.beginRemoveRows(QModelIndex(), index, index)
            self._data.pop(index)  # Xóa phần tử khỏi danh sách
            self.endRemoveRows()

    def convert_data(self,model,child):
        logger.debug(f"Converting data for model: {model.data.id if model else 'None'}, type: {type(model).__name__}")
        data = {
            ItemRoles.RECOGNITION:  {'id': None,'state':AIState.AIOFF},
            ItemRoles.PROTECTION:  {'id': None,'state':AIState.AIOFF},
            ItemRoles.FREQUENCY: {'id': None,'state':AIState.AIOFF},
            ItemRoles.ACCESS: {'id': None,'state':AIState.AIOFF},
            ItemRoles.MOTION: {'id': None,'state':AIState.AIOFF},
            ItemRoles.TRAFFIC: {'id': None,'state':AIState.AIOFF},
            ItemRoles.WEAPON: {'id': None,'state':AIState.AIOFF},
            ItemRoles.UFO: {'id': None,'state':AIState.AIOFF},
        }
        if isinstance(model,CameraModel):
            aiFlowIds = model.data.aiFlowIds
            logger.debug(f"aiFlowIds = {aiFlowIds}")
            for ai_flow_id in aiFlowIds:
                aiflow: AiFlowModel = aiflow_model_manager.get_aiflow_model(id=ai_flow_id)
                if aiflow is not None:
                    type = aiflow.data.type
                    logger.debug(f"type = {type}")
                    if type == AIFlowType.RECOGNITION.__str__():
                        logger.debug(f"RECOGNITION = {aiflow.data.id} - {aiflow.data.is_apply()}")
                        data.update({ItemRoles.RECOGNITION: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                    if type == AIFlowType.PROTECTION.__str__():
                        data.update({ItemRoles.PROTECTION: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                    if type == AIFlowType.FREQUENCY.__str__():
                        data.update({ItemRoles.FREQUENCY: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                    if type == AIFlowType.ACCESS.__str__():
                        data.update({ItemRoles.ACCESS: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                    if type == AIFlowType.MOTION.__str__():
                        data.update({ItemRoles.MOTION: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                    if type == AIFlowType.TRAFFIC.__str__():
                        data.update({ItemRoles.TRAFFIC: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                    if type == AIFlowType.WEAPON.__str__():
                        data.update({ItemRoles.WEAPON: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                    if type == AIFlowType.UFO.__str__():
                        data.update({ItemRoles.UFO: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})

            data.update({
                ItemRoles.ID: model.data.id,
                ItemRoles.TYPE: ModelType.Camera,
                ItemRoles.NAME: model.data.name,
                ItemRoles.BRANCH: model.data.cameraBranch,
                ItemRoles.MODEL: model.data.cameraModel,
                ItemRoles.IPADDRESS: model.data.ipAddress,
                ItemRoles.MACADDRESS: model.data.ipAddress,
                ItemRoles.PARTNER: model.data.ipAddress,
                ItemRoles.GROUP: model.data.ipAddress,
                ItemRoles.CHILD: child,
            })
            return data
        elif isinstance(model,GroupModel):
            camera_ids = model.data.cameraIds
            for camera_id in camera_ids:
                camera_model = camera_model_manager.get_camera_model(id = camera_id)
                if camera_model is not None:
                    aiFlowIds = camera_model.data.aiFlowIds
                    for ai_flow_id in aiFlowIds:
                        aiflow: AiFlowModel = aiflow_model_manager.get_aiflow_model(id=ai_flow_id)
                        if aiflow is not None:
                            type = aiflow.data.type
                            if type == AIFlowType.RECOGNITION.__str__():
                                data.update({ItemRoles.RECOGNITION: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                            if type == AIFlowType.PROTECTION.__str__():
                                data.update({ItemRoles.PROTECTION: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                            if type == AIFlowType.FREQUENCY.__str__():
                                data.update({ItemRoles.FREQUENCY: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                            if type == AIFlowType.ACCESS.__str__():
                                data.update({ItemRoles.ACCESS: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                            if type == AIFlowType.MOTION.__str__():
                                data.update({ItemRoles.MOTION: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                            if type == AIFlowType.TRAFFIC.__str__():
                                data.update({ItemRoles.TRAFFIC: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                            if type == AIFlowType.WEAPON.__str__():
                                data.update({ItemRoles.WEAPON: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                            if type == AIFlowType.UFO.__str__():
                                data.update({ItemRoles.UFO: {"id":aiflow.data.id, "state": AIState.AION if aiflow.data.is_apply() else AIState.AIOFF}})
                    
            if model.data.type == "AI_BOX":
                data.update({
                    ItemRoles.ID:  model.data.id,
                    ItemRoles.TYPE:  ModelType.AIBox,
                    ItemRoles.NAME: model.data.name,
                    ItemRoles.IPADDRESS: model.data.ip,
                    ItemRoles.MACADDRESS: model.data.mac,
                    ItemRoles.PARTNER: model.data.partnerId,
                    ItemRoles.GROUP: model.data.ip,
                    ItemRoles.CHILD: child  
                })
            else:
                data.update({
                    ItemRoles.ID:  model.data.id,
                    ItemRoles.TYPE:  ModelType.Group,
                    ItemRoles.NAME: model.data.name,
                    ItemRoles.CHILD: child  
                })
            return data
        
    def _add_item(self,model = None, type = ModelType.Camera,child = None,data = None):
        self.list_model[model.data.id] = model
        model.change_model.connect(self.change_model)
        if type == ModelType.Camera:
            model.add_ai_flow.connect(self.add_ai_flow)
        new_item = {}
        if data is not None:
            new_item = data
        else:
            new_item = self.convert_data(model,child)
        self._data.append(new_item)
    
    def add_item_after_index(self, idx = 0, model = None, type = ModelType.Camera,child = None, data = None):
        self.list_model[model.data.id] = model
        model.change_model.connect(self.change_model)
        if type == ModelType.Camera:
            model.add_ai_flow.connect(self.add_ai_flow)
        # elif type == ModelType.AIBox or type == ModelType.Group:
        #     model.change_model.connect(self.change_model)
        index_of_new_item= idx
        new_item = data
        self.beginInsertRows(QModelIndex(), index_of_new_item, index_of_new_item)
        self._data.append(new_item)
        self.endInsertRows()

    def match_model(self,model):
        for idx,item in enumerate(self._data):
            if model['id_model'] == item[ItemRoles.ID]:
                return (idx,self)
            if item[ItemRoles.CHILD] is not None:
                for index,item1 in enumerate(item[ItemRoles.CHILD]._data):
                    if model['id_model'] == item1[ItemRoles.ID]:
                        return (index,self)
        return (None,None)
    
    def get_index(self,model):
        for index,item in enumerate(self._data):
            if item[ItemRoles.ID] == model.data.id:
                return index
        return None   
    
    def add_ai_flow(self,data):
        ai_flow_id,model = data
        # aiflow:AiFlows = main_controller.current_controller.list_aiflows[ai_flow_id]
        index = self.get_index(model)
        aiflow:AiFlowModel = aiflow_model_manager.get_aiflow_model(id = ai_flow_id)
        logger.debug(f'add_ai_flow = {aiflow.data} - {aiflow.data.is_apply()}')
        if aiflow is not None:
            logger.debug(f'add_ai_flow - type: {aiflow.data.type} - apply: {aiflow.data.is_apply()} - id: {aiflow.data.id}')
            if aiflow.data.is_recognition():
                if aiflow.data.is_apply():
                    self._data[index][ItemRoles.RECOGNITION] = {'id': aiflow.data.id,'state':AIState.AION}
                else:
                    self._data[index][ItemRoles.RECOGNITION] = {'id': aiflow.data.id,'state':AIState.AIOFF}
            if aiflow.data.is_protection():
                if aiflow.data.is_apply():
                    self._data[index][ItemRoles.PROTECTION] = {'id': aiflow.data.id,'state':AIState.AION}
                else:
                    self._data[index][ItemRoles.PROTECTION] = {'id': aiflow.data.id,'state':AIState.AIOFF}
            if aiflow.data.is_frequency():
                if aiflow.data.is_apply():
                    self._data[index][ItemRoles.FREQUENCY] = {'id': aiflow.data.id,'state':AIState.AION}
                else:
                    self._data[index][ItemRoles.FREQUENCY] = {'id': aiflow.data.id,'state':AIState.AIOFF}
            if aiflow.data.is_access():
                if aiflow.data.is_apply():
                    self._data[index][ItemRoles.ACCESS] = {'id': aiflow.data.id,'state':AIState.AION}
                else:
                    self._data[index][ItemRoles.ACCESS] = {'id': aiflow.data.id,'state':AIState.AIOFF}
            if aiflow.data.is_motion():
                if aiflow.data.is_apply():
                    self._data[index][ItemRoles.MOTION] = {'id': aiflow.data.id,'state':AIState.AION}
                else:
                    self._data[index][ItemRoles.MOTION] = {'id': aiflow.data.id,'state':AIState.AIOFF}
            if aiflow.data.is_traffic():
                if aiflow.data.is_apply():
                    self._data[index][ItemRoles.TRAFFIC] = {'id': aiflow.data.id,'state':AIState.AION}
                else:
                    self._data[index][ItemRoles.TRAFFIC] = {'id': aiflow.data.id,'state':AIState.AIOFF}
            if aiflow.data.is_weapon():
                if aiflow.data.is_apply():
                    self._data[index][ItemRoles.WEAPON] = {'id': aiflow.data.id,'state':AIState.AION}
                else:
                    self._data[index][ItemRoles.WEAPON] = {'id': aiflow.data.id,'state':AIState.AIOFF}
            if aiflow.data.is_ufo():
                if aiflow.data.is_apply():
                    self._data[index][ItemRoles.UFO] = {'id': aiflow.data.id,'state':AIState.AION}
                else:
                    self._data[index][ItemRoles.UFO] = {'id': aiflow.data.id,'state':AIState.AIOFF}
            self.dataChanged.emit(self.index(index), self.index(index), [])

    def change_model(self, data):
        key, value, model = data
        index = self.get_index(model)
        logger.debug(f'change_model = {key,value}')
        if index is not None:
            if key == 'name':
                self._data[index][ItemRoles.NAME] = value
            elif key == 'protectionRecognitionCount':
                logger.debug(f'change_model - protectionRecognitionCount = {value}')
                self._data[index][ItemRoles.RECOGNITION_PROTECTION] = value
            elif key == 'threatDetectionCount':
                logger.debug(f'change_model - threatDetectionCount = {value}')
                self._data[index][ItemRoles.RISK_IDENTIFICATION] = value
            elif key == 'aiFlowDTOList':
                # đọc từng ai_flow_dict trong value
                if value is not None:
                    for ai_flow_dict in value:
                        print(f'ai_flow = {ai_flow_dict} - {type(ai_flow_dict)}')
                        from src.common.model.aiflows_model import AiFlow
                        # parser ai_flow_dict
                        aiflow: AiFlow = AiFlow.from_dict(ai_flow_dict)
                        if aiflow is not None:
                            # lưu dữ liệu vào aiflow_model_manager
                            aiflow_model: AiFlowModel = aiflow_model_manager.get_aiflow_model(id = aiflow.id)
                            if aiflow_model is not None:
                                aiflow_model.data = aiflow
                                # cập nhật dữ liệu vào self._data
                                if aiflow.is_recognition():
                                    logger.debug(f'change_model - recognition = {aiflow.id} - {aiflow.is_apply()} - type: {aiflow.type}')
                                    if aiflow.is_apply():
                                        logger.debug(f'change_model - recognition = {aiflow.id} - {AIState.AION}')
                                        self._data[index][ItemRoles.RECOGNITION] = {'id': aiflow.id, 'state': AIState.AION}
                                    else:
                                        logger.debug(f'change_model - recognition = {aiflow.id} - {AIState.AIOFF}')
                                        self._data[index][ItemRoles.RECOGNITION] = {'id': aiflow.id, 'state': AIState.AIOFF}
                                if aiflow.is_protection():
                                    if aiflow.is_apply():
                                        logger.debug(f'change_model - protection = {aiflow.id} - {AIState.AION}')
                                        self._data[index][ItemRoles.PROTECTION] = {'id': aiflow.id, 'state': AIState.AION}
                                    else:
                                        logger.debug(f'change_model - protection = {aiflow.id} - {AIState.AIOFF}')
                                        self._data[index][ItemRoles.PROTECTION] = {'id': aiflow.id, 'state': AIState.AIOFF}
                                if aiflow.is_frequency():
                                    if aiflow.is_apply():
                                        self._data[index][ItemRoles.FREQUENCY] = {'id': aiflow.id, 'state': AIState.AION}
                                    else:
                                        self._data[index][ItemRoles.FREQUENCY] = {'id': aiflow.id, 'state': AIState.AIOFF}
                                if aiflow.is_access():
                                    if aiflow.is_apply():
                                        self._data[index][ItemRoles.ACCESS] = {'id': aiflow.id, 'state': AIState.AION}
                                    else:
                                        self._data[index][ItemRoles.ACCESS] = {'id': aiflow.id, 'state': AIState.AIOFF}
                                if aiflow.is_motion():
                                    if aiflow.is_apply():
                                        self._data[index][ItemRoles.MOTION] = {'id': aiflow.id, 'state': AIState.AION}
                                    else:
                                        self._data[index][ItemRoles.MOTION] = {'id': aiflow.id, 'state': AIState.AIOFF}
                                if aiflow.is_traffic():
                                    if aiflow.is_apply():
                                        self._data[index][ItemRoles.TRAFFIC] = {'id': aiflow.id, 'state': AIState.AION}
                                    else:
                                        self._data[index][ItemRoles.TRAFFIC] = {'id': aiflow.id, 'state': AIState.AIOFF}
                                if aiflow.is_weapon():
                                    if aiflow.is_apply():
                                        self._data[index][ItemRoles.WEAPON] = {'id': aiflow.id, 'state': AIState.AION}
                                    else:
                                        self._data[index][ItemRoles.WEAPON] = {'id': aiflow.id, 'state': AIState.AIOFF}
                                if aiflow.is_ufo():
                                    if aiflow.is_apply():
                                        self._data[index][ItemRoles.UFO] = {'id': aiflow.id, 'state': AIState.AION}
                                    else:
                                        self._data[index][ItemRoles.UFO] = {'id': aiflow.id, 'state': AIState.AIOFF}
                            else:
                                logger.error(f'change_model - aiflow_model is None')
            elif key == 'cameraIds': # GroupModel
                # xử lý việc xóa camera không có trong Group trước
                child_list_model = self._data[index][ItemRoles.CHILD]
                list_id = []
                if child_list_model is not None and isinstance(child_list_model,ListModel):
                    list_idx = []
                    for idx,child_model in enumerate(child_list_model._data):
                        
                        if child_model[ItemRoles.ID] not in value and child_model[ItemRoles.TYPE] == ModelType.Camera:
                            list_idx.append(idx)
                            # child_list_model.removeItem(idx)
                        else:
                            list_id.append(child_model[ItemRoles.ID])
                    for idx in reversed(list_idx):
                        child_list_model.removeItem(idx)
                # thêm camera mới vào Group
                if isinstance(child_list_model,ListModel):
                    for id_camera in value:
                        if id_camera not in list_id:
                            camera_model = camera_model_manager.get_camera_model(id = id_camera)
                            if camera_model is not None:
                                camera_model_data = self.convert_data(camera_model,None)
                                child_list_model.add_item_after_index(idx = len(child_list_model._data), model = camera_model, type = ModelType.Camera,child = None, data = camera_model_data)

            elif key == 'childGroupIds': # GroupModel
                pass
            elif key == 'cameraGroupIds': # CameraModel
                pass
            elif key == 'doorType': # DoorModel
                pass
            elif key == 'brandName': # DoorModel
                pass
            elif key == 'username': # DoorModel
                pass
            elif key == 'password': # DoorModel
                pass
            elif key == 'ipAddress': # DoorModel
                pass
            elif key == 'port': # DoorModel
                pass
            self.dataChanged.emit(self.index(index), self.index(index), [])

class DeviceController(QObject):
    def __init__(self, parent=None) -> None:
        super().__init__(parent)
        self._listmodel = ListModel()
        logger.debug("DeviceController initialized with new ListModel")
    
    @Property(QObject, constant=True)
    def listmodel(self):
        logger.debug(f"listmodel property accessed, data count: {len(self._listmodel._data)}")
        return self._listmodel

    # @Slot(int, bool)
    # def connect_group(self, list_index, connected):
    #     self._listmodel.set_group_connected(list_index, connected)

    @Slot(int)
    def add_after_index(self, idx = 0,model = None,type = ModelType.AIBox, child = None,data = None):
        self._listmodel.add_item_after_index(idx=idx,model=model,type=type,child=child,data = data)

    @Slot(str, result=str)
    def get_color_theme_by_key(self, key = None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Color", key)

    @Slot(str, result=str)
    def get_image_theme_by_key(self, key = None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Image", key)

    @Slot(str,dict,bool)
    def aiflow_clicked(self,key = None,model = None, is_checkbox = False):
        logger.info(f"aiflow_clicked called with key={key}, model_id={model['id_model'] if model else 'None'}, is_checkbox={is_checkbox}")
        ai_flow_type = self.get_ai_type(key=key)
        ai_data = model[key]
        logger.info(f"aiflow_clicked - ai_data: {ai_data}")
        id = model["id_model"]
        aiflow_id = ai_data['id']
        model_type = model["type"]
        logger.info(f"aiflow_clicked - Extracted data: ai_flow_type={ai_flow_type}, id={id}, aiflow_id={aiflow_id}, model_type={model_type}")
        
        if not is_checkbox:
            logger.debug("Handling button click (non-checkbox) action")
            model_data = self.get_model(id = id, type = model_type)
            if model_type == ModelType.Camera:
                logger.debug("Opening ContentAddUpdateScriptAIDialog for Camera")
                dialog = ContentAddUpdateScriptAIDialog(camera_data=model_data.data, ai_flow_type=ai_flow_type)
                result = dialog.exec()
                if result == QDialog.DialogCode.Accepted:
                    logger.debug(f'aiflow_clicked dialog result = {result}')
                    success = dialog.success
                    logger.debug(f'aiflow_clicked success = {success}')
                    if success:
                        logger.debug("Updating camera model state")
                        # self._listmodel.change_model(('state', True, model_data))
            elif model_type == ModelType.AIBox or model_type == ModelType.Group:   
                logger.debug(f"Handling AIBox/Group type for {model_type}")
                data = None
                if data is not None:
                    logger.debug(f"Applying AI flow with data: {data}")
                    main_controller.current_controller.apply_ai_flow(data = data)
        else:
            logger.info(f"aiflow_clicked Handling checkbox click action - aiflow_id:{aiflow_id}")
            aiflow_model = aiflow_model_manager.get_aiflow_model(id = aiflow_id)
            model_data = self.get_model(id = id, type = model_type)
            logger.info(f"aiflow_clicked model_data: {model_data.data.aiFlowIds}")
            logger.info(f"aiflow_clicked ai_flow_type: {ai_flow_type}")

            apply_value = True
            if aiflow_model is not None:
                logger.info(f'CASE: aiflow_model is not None')
                apply_value = not aiflow_model.data.is_apply()
                logger.info(f"aiflow_clicked aiflow_model: A {aiflow_model.data.to_dict()}")
                has_zone = ai_flow_type == AIFlowType.RECOGNITION or (aiflow_model.data.polygonIds is not None and len(aiflow_model.data.polygonIds) > 0)
                if model_type == ModelType.Camera:
                    data = {
                        "id": model_data.data.id if model_data is not None else None,
                        "apply": apply_value,
                        "type": ai_flow_type
                    }
                self.process_ai_flow_data(data=data, has_zone=has_zone, aiflow_model = aiflow_model)
            else:
                logger.info(f'CASE: aiflow_model is None')
                # trường hợp là RECOGNITION và không có aiflow_model thì tạo mới AIFlow và gọi apply true để mở luôn
                def on_get_ai_flow_and_type_thread(result):
                    logger.info(f'on_get_ai_flow_and_type_thread: {result}')
                    if result is not None:
                        aiflow_model = result
                        if isinstance(aiflow_model, AiFlowModel):
                            # aiflow_model = aiflow_model_manager.get_aiflow_model(id = aiflow_model.data.id)
                            logger.info(f'aiflow_model: has aiflow_model - aiflow_model.data.to_dict(): {aiflow_model.data.to_dict()}')
                            logger.info(f'aiflow_model: has aiflow_model - aiflow_model.data.is_apply(): {aiflow_model.data.is_apply()}')
                            apply_value = not aiflow_model.data.is_apply()
                            logger.info(f'aiflow_clicked apply_value: {apply_value}')
                            if model_type == ModelType.Camera:
                                logger.info(f"aiflow_clicked Processing checkbox click for Camera: {apply_value}")
                                data = {
                                    "id": model_data.data.id if model_data is not None else None,
                                    "apply": apply_value,
                                    "type": ai_flow_type
                                }
                            logger.info(f'aiflow_clicked data: {data}')
                            has_zone = ai_flow_type == AIFlowType.RECOGNITION or (aiflow_model.data.polygonIds is not None and len(aiflow_model.data.polygonIds) > 0)
                            self.process_ai_flow_data(data=data, has_zone=has_zone, aiflow_model = aiflow_model)
                
                main_controller.current_controller.get_ai_flow_and_type(
                    camera_id = model_data.data.id, 
                    type = ai_flow_type, 
                    callback = on_get_ai_flow_and_type_thread
                )
            # else:
            #     logger.info(f'CASE: aiflow_model is None and ai_flow_type is not RECOGNITION')
            #     if model_type == ModelType.Camera:
            #         logger.info(f"aiflow_clicked Processing checkbox click for Camera: {apply_value}")
            #         data = {
            #             "id": model_data.data.id if model_data is not None else None,
            #             "apply": apply_value,
            #             "type": ai_flow_type
            #         }
                    
            #     has_zone = False # không có zone
            #     self.process_ai_flow_data(data=data, has_zone=has_zone, aiflow_model = None)
    
    def process_ai_flow_data(self, data, has_zone, aiflow_model):
        """Process AI flow data and apply it to camera
        
        Args:
            data (dict): Data containing camera id, flow type and apply value
            has_zone (bool): Whether the AI flow has zone configured
        """
        if data is not None:
            logger.debug(f"aiflow_clicked data: {data}")
            logger.debug(f"aiflow_clicked aiflow_model: {aiflow_model} - has_zone: {has_zone}")
            if aiflow_model is not None:
                if has_zone:
                    main_controller.current_controller.apply_ai_flow_camera(data=data)
                else:
                    from src.api.api_client import TypeRequestVMS
                    listen_show_notification.listen_API_fail_message_signal.emit(
                        (None, TypeRequestVMS.APPLY_AIFLOW, "AI_FLOW_NOT_APPLIED_WITHOUT_ZONE"))
            else:
                from src.api.api_client import TypeRequestVMS
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (None, TypeRequestVMS.APPLY_AIFLOW, "AI_FLOW_NOT_FOUND"))
                

    def on_get_ai_flow_and_type_thread(self,data):
        logger.debug(f"on_get_ai_flow_and_type_thread: {data}")

    @Slot(str,dict)
    def checkbox_clicked(self,key = None,model = None):     
        logger.debug(f"checkbox_clicked = {key,model['id_model']}")

    @Slot(str,dict)
    def action_clicked(self,action_type,listmodel):
        id = listmodel["id_model"]
        model_type = listmodel["type"]
        model = self.get_model(id = id, type = model_type)
        if model is not None:
            if model_type == ModelType.Camera:
                if action_type == "Edit":
                    camera_info = CameraInfoDialog(parent=main_controller.list_parent['DeviceTable'], data=model)
                    camera_info.exec()
                elif action_type == "Delete":
                    if model.data.recordSetting:
                        dialog = WarningDialog(warn= self.tr('The camera is recording. Are you sure you want to delete?'))
                        result = dialog.exec()
                    else:
                        dialog = WarningDialog()
                        result = dialog.exec()
                    if result == QDialog.Accepted:
                        main_controller.current_controller.delete_camera(data=model.data)
                    elif result == QDialog.Rejected:
                        pass
                elif action_type == "Up":
                    pass
                elif action_type == "Down":
                    pass
            elif model_type == ModelType.Door:
                if action_type == "Edit":
                    # door_dialog = AddIntegratedDeviceDialog(parent=main_controller.list_parent['DeviceTable'],door_model = model)
                    # door_dialog.exec()
                    pass
                elif action_type == "Delete":
                    dialog = WarningDialog()
                    result = dialog.exec()
                    if result == QDialog.Accepted:
                        main_controller.current_controller.delete_door(data=model.data)
                elif action_type == "Up":
                    pass
                elif action_type == "Down":
                    pass
            elif model_type == ModelType.AIBox:
                if action_type == "Edit":
                    ai_box_info = AIBoxDialog(parent=main_controller.list_parent['DeviceTable'], data=model)
                    ai_box_info.exec()
                elif action_type == "Delete":
                    pass    
                elif action_type == "Up":
                    pass
                elif action_type == "Down":
                    pass
            elif model_type == ModelType.Group:
                if action_type == "Edit":
                    group_info = AddGroupDialog(parent=main_controller.list_parent['DeviceTable'], title=self.tr("EDIT GROUP"), group_model=model)
                    group_info.exec()
                elif action_type == "Delete":
                    dialog = WarningDialog()
                    result = dialog.exec()
                    if result == QDialog.Accepted:
                        main_controller.current_controller.delete_camera_group(data=model.data)
                    elif result == QDialog.Rejected:
                        pass
                elif action_type == "Up":
                    pass
                elif action_type == "Down":
                    pass 
    
    def get_ai_type(self,key):
        if key == "recognition":
            return AIFlowType.RECOGNITION
        elif key == "protection":
            return AIFlowType.PROTECTION
        elif key == "frequency":
            return AIFlowType.FREQUENCY
        elif key == "access":
            return AIFlowType.ACCESS
        elif key == "motion":
            return AIFlowType.MOTION
        elif key == "traffic":
            return AIFlowType.TRAFFIC
        elif key == "weapon":
            return AIFlowType.WEAPON
        elif key == "ufo":
            return AIFlowType.UFO
        return None
    
    def get_ai_flow_model(self,id = None):
        return aiflow_model_manager.get_aiflow_model(id = id)
          
    def get_model(self,id = None, type = ModelType.Camera):
        model = None
        if type == ModelType.Camera:
            model = camera_model_manager.get_camera_model(id=id)
        elif type == ModelType.Door:
            model = door_model_manager.get_door_model(id=id)
        else:
            model = group_model_manager.get_group_model(id = id)
        return model
    
    def clear(self):
        logger.debug("DeviceController.clear() called")
        self._listmodel.clear_data()
        self._listmodel = ListModel()
        logger.debug(f"New ListModel created, data count: {len(self._listmodel._data)}")

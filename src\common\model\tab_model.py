from typing import List

from dataclasses import dataclass, asdict, fields
from PySide6.QtCore import QObject, Signal,Slot
from PySide6.QtWidgets import QWidget
from src.utils.camera_qsettings import Camera_Qsettings
from src.common.model.camera_model import <PERSON>Model, camera_model_manager
from src.common.model.group_model import group_model_manager
from src.common.model.event_data_model import EventAI
from src.common.model.device_models import TabType
from src.common.qml.models.map_controller import MapModel,FloorModel,map_manager,floor_manager

import uuid
import logging
logger = logging.getLogger(__name__)

class ItemType:
    Label = 'Label'
    Camera = 'Camera'
    Floor = 'Floor'
    MapOSM = 'MapOSM'
    Event = 'Event'
    VideoLocal = 'VideoLocal'

class SignalType:
    Invalid = 'Invalid'
    DropCamera = 'DropCamera'
    DropMap = 'DropMap'
    DropFloor = 'DropFloor'
    ChangeGridView = 'ChangeGridView'
    DropGroup = 'DropGroup'
    SwapItem = 'SwapItem'
    RemoveItem = 'RemoveItem'
    RemoveAllItem = 'RemoveAllItem'
    FullScreenItem = 'FullScreenItem'
    ReplaceAllTabModelData = 'ReplaceAllTabModelData'
    EditTabModelName = 'EditTabModelName'
    MultipleSelection = 'MultipleSelection'

@dataclass
class GridItem:
    index: int = None
    type: str = None
    is_fullscreen: bool = False
    is_virtual_fullscreen: bool = False
    row: int = None
    col: int = None
    width: int = None
    height: int = None
    virtual_width:int = None
    virtual_height:int = None
    model:dict = None
    widget:QWidget = None
    virtual_widget:QWidget = None

    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        return cls(**filtered_dict)

    # def to_dict(self):
    #     return asdict(self)
    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}
@dataclass
class Direction:

    type: str = None
    data: str = None

    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        return cls(**filtered_dict)

    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}
    
@dataclass
class Tab:
    id: str = None
    name: str = None
    server_ip: str = None
    type: str = None
    index: int = None
    isShow: bool = None
    direction: dict = None
    currentGrid: dict = None
    gridItemIds: List[str] = None
    listGridData: dict = None
    listGridCustomData: List[dict] = None

    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        # filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        filtered_dict = {}
        for key, value in data_dict.items():
            if key in field_names:
                if key == 'currentGrid' or key == 'listGridCustomData':
                    result = eval(value) 
                    filtered_dict[key] = result
                elif key == 'listGridData' or key == 'direction':
                    result = eval(value) if value is not None else {}
                    filtered_dict[key] = result
                elif key == 'isShow':
                    filtered_dict[key] = False
                else:
                    filtered_dict[key] = value
        return cls(**filtered_dict)
    
    def to_dict(self):
        # only return the fields that are not None
        dist = {}
        for k, v in self.__dict__.items():
            if k == 'currentGrid' or k == 'listGridCustomData':
                if v is not None:
                   dist[k] = str(v)
            elif k == 'direction':
                if v is not None:
                    dist[k] = str(v)
            elif k == 'listGridData':
                temp = {}
                for key, item in self.listGridData.items():
                    key = str(key)
                    temp[key] = GridItem.to_dict(item)
                    if item.type == ItemType.Camera:
                        temp[key]['model'] = item.model.data.id
                    elif item.type == ItemType.Floor:
                        temp[key]['model'] = item.model.id
                    elif item.type == ItemType.Event:
                        temp[key]['model'] = item.model.to_dict()
                    elif item.type == ItemType.MapOSM:
                        temp[key]['model'] = item.model.id
                    temp[key]['widget'] = None
                    temp[key]['virtual_widget'] = None
                dist[k] = str(temp)
            else:
                if v is not None:
                    dist[k] = v
        return dist 
    
class TabModel(QObject):
    add_grid_item_signal = Signal(int)
    swap_grid_item_signal = Signal(tuple)
    change_grid_view_signal = Signal(dict)
    remove_grid_item_signal = Signal(int)
    fullscreen_grid_item_signal = Signal(int)
    update_qsetting_signal = Signal()
    add_group_signal = Signal(list)
    remove_all_grid_item_signal = Signal(str)
    replace_all_tab_model_data_signal = Signal(tuple)
    tab_name_changed = Signal(str)
    id_changed =Signal(str)
    def __init__(self,tab:Tab = None):
        super().__init__()
        self.data = tab
        self.list_standard_item = []
        self.widget_registered = []

    def is_show(self):
        if len(self.widget_registered) > 0:
            return True
        return False
    
    def register_signal(self,widget = None):
        if hasattr(widget,'add_grid_item_signal'):
            self.add_grid_item_signal.connect(widget.add_grid_item_signal)
        if hasattr(widget,'swap_grid_item_signal'):
            self.swap_grid_item_signal.connect(widget.swap_grid_item_signal)
        if hasattr(widget,'change_grid_view_signal'):
            self.change_grid_view_signal.connect(widget.change_grid_view_signal)
        if hasattr(widget,'remove_grid_item_signal'):
            self.remove_grid_item_signal.connect(widget.remove_grid_item_signal)
        if hasattr(widget,'fullscreen_grid_item_signal'):
            self.fullscreen_grid_item_signal.connect(widget.fullscreen_grid_item_signal)
        if hasattr(widget,'update_qsetting_signal'):
            self.update_qsetting_signal.connect(widget.update_qsetting_signal)
        if hasattr(widget,'add_group_signal'):
            self.add_group_signal.connect(widget.add_group_signal)
        if hasattr(widget,'remove_all_grid_item_signal'):
            self.remove_all_grid_item_signal.connect(widget.remove_all_grid_item_signal)
        if hasattr(widget,'replace_all_tab_model_data_signal'):
            self.replace_all_tab_model_data_signal.connect(widget.replace_all_tab_model_data_signal)
        self.widget_registered.append(widget)

    def unregister_signal(self,widget = None):
        if hasattr(widget,'add_grid_item_signal'):
            self.add_grid_item_signal.disconnect(widget.add_grid_item_signal)
        if hasattr(widget,'swap_grid_item_signal'):
            self.swap_grid_item_signal.disconnect(widget.swap_grid_item_signal)
        if hasattr(widget,'change_grid_view_signal'):
            self.change_grid_view_signal.disconnect(widget.change_grid_view_signal)
        if hasattr(widget,'remove_grid_item_signal'):
            self.remove_grid_item_signal.disconnect(widget.remove_grid_item_signal)
        if hasattr(widget,'fullscreen_grid_item_signal'):
            self.fullscreen_grid_item_signal.disconnect(widget.fullscreen_grid_item_signal)
        if hasattr(widget,'update_qsetting_signal'):
            self.update_qsetting_signal.disconnect(widget.update_qsetting_signal)
        if hasattr(widget,'add_group_signal'):
            self.add_group_signal.disconnect(widget.add_group_signal)
        if hasattr(widget,'remove_all_grid_item_signal'):
            self.remove_all_grid_item_signal.disconnect(widget.remove_all_grid_item_signal)
        if hasattr(widget,'replace_all_tab_model_data_signal'):
            self.replace_all_tab_model_data_signal.disconnect(widget.replace_all_tab_model_data_signal)
        if widget in self.widget_registered:
            self.widget_registered.remove(widget)

    def register_standard_item(self, standard_item):
        self.list_standard_item.append(standard_item)

    def unregister_standard_item(self,standard_item):
        try:
            standard_item.disconnect_slot()
            self.list_standard_item.remove(standard_item)
        except Exception as e:
            print(f'unregister_standard_item = {e}')

    def clear_list_standard_item(self):
        for standard_item in self.list_standard_item:
            standard_item.disconnect_slot()
        self.list_standard_item = []

    def add_grid_item(self,item: GridItem = None):
        self.data.listGridData[item.index] = item
        # print(f'add_grid_item = {item.index} : {item.to_dict()}')

    def remove_grid_item(self,item: GridItem = None):
        del self.data.listGridData[item.index]

    def from_dict(self, data_dict):
        self.id = data_dict['id']
        self.server_ip = data_dict['server_ip']
        self.tab_name = data_dict['tab_name']
        self.tab_type = data_dict['tab_type']
        self.grid = data_dict['grid']
        self.tab_index = data_dict['tab_index']
        self.list_data_custom_grid = data_dict['list_data_custom_grid']
        self.list_grid_item = {}
        for key, item in data_dict['list_grid_item'].items():
            self.list_grid_item[key] = GridItem.from_dict(item)
            if item['type'] == ItemType.Camera:
                self.list_grid_item[key].model = item['model']
            elif item['type'] == ItemType.Floor:
                self.list_grid_item[key].model = item['model']
            # self.list_grid_item[key].model = item['model']
            self.list_grid_item[key].widget = item['widget']
            self.list_grid_item[key].virtual_widget = item['virtual_widget']
        return self

    def to_dict(self):
        dist = {}
        dist['id'] = self.id
        dist['server_ip'] = self.server_ip
        dist['tab_name'] = self.tab_name
        dist['tab_type'] = self.tab_type
        dist['grid'] = self.grid
        dist['tab_index'] = self.tab_index
        dist['list_data_custom_grid'] = self.list_data_custom_grid
        dist['list_grid_item'] = {}
        for key, item in self.list_grid_item.items():
            dist['list_grid_item'][key] = GridItem.to_dict(item)
            if item.type == ItemType.Camera:
                dist['list_grid_item'][key]['model'] = item.model.data.name
            elif item.type == ItemType.Floor:
                # print(f'item = {item.model.name}')
                dist['list_grid_item'][key]['model'] = item.model.name
            elif item.type == ItemType.Event:
                dist['list_grid_item'][key]['model'] = item.model.to_dict()
                # print(f'item = {dist["list_grid_item"][key]["model"]}')
            # dist['list_grid_item'][key]['model'] = item.model

            dist['list_grid_item'][key]['widget'] = None
            dist['list_grid_item'][key]['virtual_widget'] = None
        # print(f'item = {dist}')
        return dist

    def to_qsetting(self):
        dist = self.to_dict()
        # print(f'to_qsetting = {dist}')
        Camera_Qsettings.get_instance().set_status_tab(dist)

    def set_model(self,grid_item:GridItem = None,model = None):
        # Sử dụng hàm set_model nhằm mục đích mỗi khi có tín hiệu xóa camera tu server thì tab_model sẽ xóa camera này trong griditem
        try:
            if isinstance(grid_item.model,CameraModel):
                grid_item.model.delete_camera_signal.disconnect(lambda data: self.delete_camera_signal(data))
        except Exception as e:
            logger.debug(f'set_model error: {e}')
        try:
            if isinstance(model,CameraModel):
                grid_item.model = model
                grid_item.model.delete_camera_signal.connect(lambda data: self.delete_camera_signal(data))
            elif isinstance(model,EventAI):
                grid_item.model = model
            elif isinstance(model,FloorModel):
                grid_item.model = model
            elif isinstance(model,MapModel):
                grid_item.model = model
        except Exception as e:
            logger.debug(f'set_model error: {e}') 


    def delete_camera_signal(self,camera_model):
        for index, grid_item in self.data.listGridData.items():
            if camera_model == grid_item.model:
                if self.is_show():
                    self.remove_grid_item_signal.emit(index)
                else:
                    self.data.listGridData[index].type = ItemType.Label
                    self.data.listGridData[index].model = 'Label'
                    self.data.listGridData[index].widget = None
                    self.data.listGridData[index].virtual_widget = None
                if self.data.type != TabType.Invalid:
                    from src.common.controller.controller_manager import controller_manager
                    from src.common.controller.main_controller import main_controller
                    controller = controller_manager.get_controller(server_ip = self.data.server_ip)
                    self.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.Invalid,'data':{}}
                    controller.update_tabmodel_by_put(parent=main_controller.list_parent['CameraScreen'], tab= self.data)

    def change_model(self, item_type = None, key = None, model = None):
        if item_type == ItemType.Camera:
            for index, value in self.data.listGridData.items():
                # print(f'change_model = {key} : {value.to_dict()}')

                if value.model == key:
                    value.model = model
                    # print(f'change_model1 = {key} : {self.tab_name} : {value.to_dict()}')
    
    def update_tabmodel(self,direction,tab:Tab):
        old_id = self.data.direction.get('id',None)
        new_id = direction.get('id',None)
        print(f'update_tabmodel = {new_id,old_id}')
        if old_id != new_id:
            if isinstance(direction,dict) and len(direction) > 0:
                type = direction.get('type',None)
                data = direction.get('data',None)
                logger.debug(f'data = {data}')
                if type == SignalType.DropCamera:
                    camera_id = data['camera_id']
                    row = data['row']
                    col = data['col']
                    camera_model = camera_model_manager.get_camera_model(id = camera_id)
                    # model = None
                    # for camera_model in camera_list:
                    #     if data['name'] == camera_model.data.name:
                    #         model = camera_model
                    #         break
                    if camera_model is not None:
                        index = data['key']
                        grid_item = self.data.listGridData.get(index,None)
                        if grid_item is None:
                            grid_item = GridItem(index=index, type=ItemType.Camera, row=row,col=col,model = camera_model)
                            self.add_grid_item(item = grid_item)
                            self.add_grid_item_signal.emit(index)
                        else:
                            grid_item.type = ItemType.Camera
                            self.set_model(grid_item = grid_item,model = camera_model)
                            self.add_grid_item_signal.emit(index)

                elif type == SignalType.ChangeGridView:
                    model = data['grid']
                    if len(self.widget_registered) > 0:
                        self.data.currentGrid = model
                        self.change_grid_view_signal.emit(model)
                    else:
                        self.convert_data(tab=tab)

                elif type == SignalType.DropGroup:
                    group_id = data['id']
                    # logger.debug(f'ahihi = {model}')    
                    if len(self.widget_registered) > 0:
                        list_id = []
                        group_model = group_model_manager.get_group_model(id = group_id)
                        camera_list = camera_model_manager.get_camera_list(server_ip= group_model.data.server_ip)
                        for id in group_model.data.cameraIds:
                            list_id.append(id)
                        # list_camera = self.main_controller.get_all_camera_in_group(
                        #     item.id)
                        list_camera= []
                        if len(list_id) > 0:
                            for camera in camera_list.values():
                                if camera.data.id in list_id:
                                    list_camera.append(camera)
                            self.add_group_signal.emit(list_camera) 
                    else:
                        self.convert_data(tab = tab)
                elif type == SignalType.MultipleSelection:
                    self.convert_data(tab = tab)
                    self.change_grid_view_signal.emit(tab.currentGrid)

                elif type == SignalType.SwapItem:
                    new_index = data['newkey']
                    old_index = data['oldkey']
                    new_row = data['new_row']
                    new_col = data['new_col']
                    if new_index != old_index:
                        if len(self.widget_registered) > 0:
                            # Case tab này đang được show
                            self.swap_grid_item_signal.emit((new_index,old_index))
                        else:
                            # Case tab này đang không được show thì cần Update tab_model
                            new_grid_item:GridItem = self.data.listGridData.get(new_index,None)
                            old_grid_item:GridItem = self.data.listGridData.get(old_index,None)
                            if new_grid_item is None:
                                new_grid_item = GridItem(index=new_index, type=ItemType.Label,model = 'Label',row=new_row,col=new_col)
                                self.add_grid_item(item = new_grid_item)
                            temp_type = new_grid_item.type
                            temp_model = new_grid_item.model
                            new_grid_item.type = old_grid_item.type
                            new_grid_item.model = old_grid_item.model
                            old_grid_item.type = temp_type
                            old_grid_item.model = temp_model

                elif type == SignalType.RemoveItem:
                    index = data['key']
                    if len(self.widget_registered) > 0:
                        grid_item = self.data.listGridData.get(index,None)
                        self.remove_grid_item_signal.emit(index)
                    else:
                        grid_item = self.data.listGridData.get(index,None)
                        grid_item.type = ItemType.Label
                        grid_item.model = 'Label'
                elif type == SignalType.FullScreenItem:
                    index = data['key']
                    if len(self.widget_registered) > 0:
                        self.fullscreen_grid_item_signal.emit(index)
                    else:
                        pass
                        grid_item = self.data.listGridData.get(index,None)
                        grid_item.is_fullscreen = False if grid_item.is_fullscreen else True
                        # self.remove_grid_item(grid_item)
                elif type == SignalType.RemoveAllItem:
                    if len(self.widget_registered) > 0:
                        self.remove_all_grid_item_signal.emit('remove_all_grid_item_signal')
                    else:
                        self.convert_data(tab = tab)

                elif type == SignalType.ReplaceAllTabModelData:
                    if len(self.widget_registered) > 0:
                        tab_model = TabModel(tab = tab)
                        tab_model.convert_data()
                        self.replace_all_tab_model_data_signal.emit((tab_model))
                    else:
                        self.data = tab
                        self.convert_data()
                elif type == SignalType.EditTabModelName:
                    old_name = data.get('old_name',None)
                    new_name = data.get('new_name',None)
                    if old_name is not None and new_name is not None:
                        self.tab_name_changed.emit(new_name)
                        tab_model_manager.tab_name_changed_signal.emit((self,new_name))
    
    def get_grid_item(self,name = None,type = None):
        for index, grid_item in self.data.listGridData.items():
            if type == ItemType.Camera:
                if grid_item.type == type and name == grid_item.model.data.name:
                    return grid_item
            elif type == ItemType.Floor:
                pass
                # Xu ly sau
                # map_name = item['model']
                # for map in self.main_controller.list_item_map_model:
                #     if map.name == map_name:
                #         map_item_model = map
                #         grid_item = GridItem(index = item['index'],type=item['type'], row = item['row'], col = item['col'],width = item['width'],height = item['height'],model= map_item_model,widget = None)
                #         # tab_model.add_grid_item(grid_item)
                #         listGridData[grid_item.index] = grid_item
                #         break
            elif type == ItemType.Event:
                if grid_item.type == type and name == grid_item.model:
                    return grid_item
        return None

    def convert_data(self,tab:Tab = None):
        # xử lý data tabmodel từ server sang TabModel
        listGridData = {}
        oldlistGridData = {}
        if tab is not None:
            oldlistGridData = tab.listGridData
            self.data.currentGrid = tab.currentGrid
        else:
            oldlistGridData = self.data.listGridData
        for index, item in oldlistGridData.items():
            type=item['type']
            if type == ItemType.Camera:
                camera_model:CameraModel = camera_model_manager.get_camera_model(id = item['model'])
                if camera_model is not None:
                    # Cần đăng ký nhận signal delete_camera_signal khi có tín hiệu xóa camera thì tabmodel này sẽ lắng nghe dc để xóa camera trên Tabmodel
                    camera_model.delete_camera_signal.connect(lambda data: self.delete_camera_signal(data))
                    model = camera_model
                    grid_item = GridItem(index = item['index'],type=item['type'], row = item['row'], col = item['col'],width = item['width'],height = item['height'],is_fullscreen=False,is_virtual_fullscreen=False, model= model,widget = None)
                    # tab_model.add_grid_item(grid_item)
                    listGridData[grid_item.index] = grid_item
                else:
                    model = "Label"
                    grid_item = GridItem(index = item['index'],type=ItemType.Label, row = item['row'], col = item['col'],width = item['width'],height = item['height'],is_fullscreen=False,is_virtual_fullscreen=False, model= model,widget = None)
                    # tab_model.add_grid_item(grid_item)
                    listGridData[grid_item.index] = grid_item
            elif type == ItemType.Floor:
                floor_model = floor_manager.get_floor(id = item["model"])
                if floor_model is not None:
                    grid_item = GridItem(index = item['index'],type=item['type'], row = item['row'], col = item['col'],width = item['width'],height = item['height'],is_fullscreen=False,is_virtual_fullscreen=False, model= floor_model,widget = None)
                    listGridData[grid_item.index] = grid_item
                else:
                    model = "Label"
                    grid_item = GridItem(index = item['index'],type=ItemType.Label, row = item['row'], col = item['col'],width = item['width'],height = item['height'],is_fullscreen=False,is_virtual_fullscreen=False, model= model,widget = None)
                    # tab_model.add_grid_item(grid_item)
                    listGridData[grid_item.index] = grid_item

            elif type == ItemType.MapOSM:
                map_model = map_manager.get_map_model(id = item["model"])
                if map_model is not None:
                    grid_item = GridItem(index = item['index'],type=item['type'], row = item['row'], col = item['col'],width = item['width'],height = item['height'],is_fullscreen=False,is_virtual_fullscreen=False, model= map_model,widget = None)
                    listGridData[grid_item.index] = grid_item
                else:
                    model = "Label"
                    grid_item = GridItem(index = item['index'],type=ItemType.Label, row = item['row'], col = item['col'],width = item['width'],height = item['height'],is_fullscreen=False,is_virtual_fullscreen=False, model= model,widget = None)
                    # tab_model.add_grid_item(grid_item)
                    listGridData[grid_item.index] = grid_item

            elif type == ItemType.Event:
                event_model = EventAI.from_dict(item['model'])
                grid_item = GridItem(index = item['index'],type=item['type'], row = item['row'], col = item['col'],width = item['width'],height = item['height'],is_fullscreen=item['is_fullscreen'],is_virtual_fullscreen=item['is_virtual_fullscreen'],model= event_model,widget = None)
                listGridData[grid_item.index] = grid_item
        self.data.listGridData = listGridData

class TabModelManager(QObject):
    add_tab_model_signal = Signal(tuple)
    add_tab_model_list_signal = Signal(tuple)
    tab_name_changed_signal = Signal(tuple)
    __instance = None
    def __init__(self):
        super().__init__()
        self.tab_model_list = {}
    @staticmethod
    def get_instance():
        if TabModelManager.__instance is None:
            TabModelManager.__instance = TabModelManager()
        return TabModelManager.__instance

    def add_tab_model(self,tab_model:TabModel = None):
        self.tab_model_list[tab_model.data.id] = tab_model

    def add_tab_model_list(self,controller = None,tab_model_list = []):
        for tab_model in tab_model_list:
            self.tab_model_list[tab_model.data.id] = tab_model
        self.add_tab_model_list_signal.emit((controller,tab_model_list)) 

    def remove_tab_model(self, tab_model:TabModel = None):
        del self.tab_model_list[tab_model.data.id]
         
    def get_tab_model_list(self,server_ip = None):
        output = {}
        for id,tab_model in self.tab_model_list.items():
            if tab_model.data.server_ip == server_ip:
                output[id] = tab_model
        return output

    def get_tab_model(self,tab_type = None, tab_name = None,id =None,server_ip = None):
        if id is not None and id in self.tab_model_list:
            return self.tab_model_list[id]
        elif tab_name is not None:
            for id,tab_model in self.tab_model_list.items():
                if tab_model.data.type == tab_type and tab_model.data.name == tab_name and tab_model.data.server_ip == server_ip:
                    return tab_model
                # elif tab_model.data.name == tab_name:
                #     return tab_model
        return None

    def id_selected(self):
        id = 0
        while id in self.tab_model_list.keys():
            id += 1
        return id
    
    def change_tab_name(self,tab_model:TabModel = None, name = None):
        if tab_model.data.name == name:
            return
        else:
            tab_model.data.name = name

    def delete_server(self,server_ip = None):
        tab_model_list_selected = {}
        for id,tab_model in self.tab_model_list.items():
            if tab_model.data.server_ip == server_ip:
                tab_model_list_selected[id] = tab_model
        for id,tab_model in tab_model_list_selected.items():
            del self.tab_model_list[id]
            
    def edit_key(self,tab_model = None):
        for id,item in self.tab_model_list.items():
            if item.data.type == tab_model.data.type and item.data.name == tab_model.data.name and item.data.server_ip == tab_model.data.server_ip:
                del self.tab_model_list[id]
                self.tab_model_list[tab_model.data.id] = tab_model
                break
        # self.tab_model_list[tab_model.data.id] = self.tab_model_list.pop(tab_model.data.server_ip)    

    def clear_list_standard_item(self):
        for tab_model in self.tab_model_list.values():
            tab_model.clear_list_standard_item()

tab_model_manager = TabModelManager.get_instance()

"""
PyAV Wrapper - <PERSON><PERSON><PERSON> bọc cho các thao tác PyAV trong video streaming.
"""

import sys
import av
import cv2
import time
import logging
from av.video.reformatter import VideoReformatter
from av.codec.hwaccel import HWAccel, hwdevices_available
from av.codec.context import Flags, Flags2
logger = logging.getLogger(__name__)

class PyAVWrapper:
    """
    Lớ<PERSON> bọc cho các thao tác PyAV.
    Lớp này cung cấp giao diện đơn giản để làm việc với video streams
    sử dụng PyAV, với hỗ trợ tăng tốc phần cứng.
    """
    
    def __init__(self):
        """
        Khởi tạo PyAVWrapper.
        
        Args:
            use_hw_accel: Có sử dụng tăng tốc phần cứng nếu có sẵn hay không
        """
        logger.debug("Khởi tạo PyAVWrapper")
        self.container = None
        self.stream = None
        self.decoder_ctx = None
        self.stream_info = self._create_default_stream_info()
        self._max_frame_size = 1280  # Giới hạn kích thước khung hình tối đa
        self._reformatter = None  # Đối tượng VideoReformatter để thay đổi kích thước hiệu quả
        self._target_width = None  # Chiều rộng mục tiêu cho việc thay đổi kích thước
        self._target_height = None  # Chiều cao mục tiêu cho việc thay đổi kích thước
        self._target_format = 'rgb24'  # Định dạng mục tiêu cho việc thay đổi kích thước
        self._interpolation = av.video.reformatter.Interpolation.AREA  # Phương pháp nội suy mặc định
        self.is_running = True  # Biến điều khiển việc lấy khung hình

    @staticmethod
    def _create_default_stream_info():
        """Tạo thông tin stream mặc định."""
        logger.debug("Tạo thông tin stream mặc định")
        return {
            'width': 0,
            'height': 0,
            'fps': 0,
            'codec': None,
            'hw_accel': None
        }

    @staticmethod
    def _get_stream_options(url):
        """
        Lấy các tùy chọn streaming mặc định dựa trên giao thức URL.
        
        Args:
            url: URL của video stream
            
        Returns:
            dict: Các tùy chọn container cho stream
        """
        options = {}
        
        if url.startswith('rtsp://'):
            options = {
                'rtsp_transport': 'tcp',
                'rtsp_flags': 'prefer_tcp',
                'stimeout': '5000000',  # Timeout 5 giây
                'rtsp_tcp_timeout': '5000000',
                'buffer_size': '10240000',  # Buffer 10MB
                'max_delay': '500000',  # Độ trễ tối đa 0.5 giây
                'analyzeduration': '100',
            }
        elif url.startswith('http://') or url.startswith('https://'):
            options = {
                'analyzeduration': '100',
                'http_transport': 'tcp',
                'http_timeout': '5000000',  # Timeout 5 giây
                'buffer_size': '10240000',  # Buffer 10MB
                'max_delay': '500000',  # Độ trễ tối đa 0.5 giây
            }
            
        return options

    def _setup_decoder_context(self, codec_context, hw_type=None):
        """
        Thiết lập ngữ cảnh decoder với các tham số chung.
        
        Args:
            codec: Codec để sử dụng
            codec_context: Ngữ cảnh codec gốc cho các tham số
            hw_type: Loại tăng tốc phần cứng (tùy chọn)
            hw_codec: Codec phần cứng (tùy chọn)
            
        Returns:
            tuple: (decoder_context, success)
        """
        logger.debug("Thiết lập ngữ cảnh decoder với hw_type=%s", hw_type)
        try:
            if hw_type:
                # Tạo hwaccel từ hw_type
                hwaccel = HWAccel(device_type=hw_type, allow_software_fallback=False)
                # Tạo decoder context
                decoder_ctx = codec_context.create(codec=codec_context.codec, mode='r',hwaccel=hwaccel)
            else:
                # Tạo decoder context
                decoder_ctx = codec_context.create(codec=codec_context.codec, mode='r')
            
            decoder_ctx.thread_type = "AUTO"
            decoder_ctx.skip_frame = 'BIDIR'
            decoder_ctx.flags2 |= Flags2.fast  # Cờ FAST
            decoder_ctx.flags2 |= Flags2.no_output  # Cờ NO_OUTPUT
            decoder_ctx.flags |= Flags.low_delay  # Cờ LOW_DELAY
            decoder_ctx.flags |= Flags.psnr  # Cờ PSNR
            decoder_ctx.flags |= Flags.gray
            decoder_ctx.thread_count = 1  # Số luồng tối đa
            decoder_ctx.extradata = codec_context.extradata
            logger.debug("Thiết lập ngữ cảnh decoder thành công với %s", hw_type)
            return decoder_ctx, True
            
        except Exception as e:
            logger.error("Không thể thiết lập ngữ cảnh decoder: %s", str(e))
            return None, False

    def _initialize_hardware_decoder(self, codec_context):
        """
        Khởi tạo decoder được tăng tốc bằng phần cứng.
        
        Args:
            codec_context: Ngữ cảnh codec gốc
            
        Returns:
            tuple: (decoder_context, hw_type)
        """
        logger.debug("Khởi tạo decoder phần cứng")
        try:
            # Kiểm tra trực tiếp các thiết bị phần cứng có sẵn
            available_hw_devices = hwdevices_available()
            
            if not available_hw_devices:
                logger.debug("Không có tăng tốc phần cứng khả dụng")
                return None, None
                
            # Chọn thiết bị phù hợp dựa trên hệ điều hành
            # Sắp xếp thứ tự ưu tiên sử dụng để tăng tốc phần cứng
            if sys.platform == 'win32':
                preferred_devices = ['cuda', 'd3d12va', 'd3d11va', 'dxva2']
            elif sys.platform == 'darwin':
                preferred_devices = ['videotoolbox']
            else:  # Linux
                preferred_devices = ['cuda','vaapi']
                
            # Thử từng thiết bị trong danh sách ưu tiên cho đến khi thành công
            for hw_type in preferred_devices:
                if hw_type not in available_hw_devices:
                    continue
                    
                logger.debug("Thử sử dụng thiết bị phần cứng: %s cho codec: %s", hw_type, codec_context.name)
                
                decoder_ctx, success = self._setup_decoder_context(
                    codec_context, 
                    hw_type=hw_type,
                )
                
                if success:
                    logger.debug("Khởi tạo decoder phần cứng thành công với %s", hw_type)
                    return decoder_ctx, hw_type
                else:
                    logger.warning("Không thể khởi tạo decoder phần cứng với %s, thử thiết bị tiếp theo", hw_type)
            
            logger.debug("Đã thử tất cả thiết bị phần cứng nhưng không thành công")
            return None, None
            
        except Exception as e:
            logger.error("Lỗi khởi tạo decoder phần cứng: %s", str(e))
            return None, None

    def _initialize_software_decoder(self, codec_context):
        """
        Khởi tạo decoder phần mềm.
        
        Args:
            codec_context: Ngữ cảnh codec gốc
            
        Returns:
            decoder_context hoặc None
        """
        logger.debug("Khởi tạo decoder phần mềm")
        try:
            decoder_ctx, success = self._setup_decoder_context(codec_context)
            if success:
                logger.debug("Khởi tạo decoder phần mềm thành công")
            else:
                logger.warning("Không thể khởi tạo decoder phần mềm")
            return decoder_ctx if success else None
        except Exception as e:
            logger.error("Lỗi khởi tạo decoder phần mềm: %s", str(e))
            return None

    def open_stream(self, url, options=None):
        """
        Mở một video stream.
        
        Args:
            url: URL của video stream
            options: Các tùy chọn bổ sung cho stream
        Returns:
            bool: True nếu thành công, False nếu không
        """
        start_time = time.time()
        logger.info("Đang mở stream từ URL: %s", url)
        try:
            # Lấy các tùy chọn streaming phù hợp dựa trên URL
            if options is None:
                container_options = self._get_stream_options(url)
            else:
                # Có thể crash nếu options không hợp lệ
                container_options = options
            logger.debug("Sử dụng các tùy chọn container: %s", container_options)
            
            # Mở container với các tùy chọn phù hợp và timeout 20 giây
            self.container = av.open(url, container_options=container_options, timeout=20)
            self.stream = next(
                (s for s in self.container.streams if s.type == 'video'), None)
            codec_context = self.stream.codec_context
            # Lấy chiều rộng và chiều cao của stream
            self.width = self.stream.width
            self.height = self.stream.height
            logger.debug(f"Kích thước stream: {self.width}x{self.height}")
            
            # Khởi tạo hw_type
            hw_type = None
            
            # Chỉ sử dụng tăng tốc phần cứng cho độ phân giải lớn hơn 5K
            use_hw_accel = (self.width > 5120 or self.height > 2880)
            
            if use_hw_accel and sys.platform == 'win32':
                logger.debug("Độ phân giải lớn hơn 5K, đang thử giải mã bằng phần cứng")
                
                self.decoder_ctx, hw_type = self._initialize_hardware_decoder(codec_context)
                
                # Chuyển sang giải mã phần mềm nếu tăng tốc phần cứng thất bại
                if not self.decoder_ctx:
                    logger.debug("Tăng tốc phần cứng thất bại, chuyển sang giải mã phần mềm")
                    self.decoder_ctx = self._initialize_software_decoder(codec_context)
            else:
                # Sử dụng giải mã phần mềm cho độ phân giải thấp hơn hoặc bằng 5K
                logger.debug(f"Độ phân giải thấp hơn hoặc bằng 5K, sử dụng giải mã phần mềm")
                self.decoder_ctx = self._initialize_software_decoder(codec_context)
                
            if not self.decoder_ctx:
                logger.error("Không thể khởi tạo bất kỳ decoder nào")
                raise RuntimeError("Không thể khởi tạo bất kỳ decoder nào")
                
            # Cập nhật thông tin stream
            self.stream_info.update({
                'width': codec_context.width,
                'height': codec_context.height,
                'fps': int(self.stream.average_rate) if self.stream.average_rate is not None else 0.0,
                'codec': codec_context.name,
                'hw_accel': hw_type
            })
            
            end_time = time.time()
            duration = end_time - start_time
            logger.debug("Mở stream thành công: %s (thời gian: %.2f giây)", self.stream_info, duration)
            
            # Lưu thời gian bắt đầu để tính thời gian chờ frame đầu tiên
            self._stream_start_time = time.time()
            
            return True
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            logger.error("Không thể mở stream: %s (thời gian: %.2f giây)", str(e), duration)
            return False

    def close_stream(self):
        """Đóng video stream và dọn dẹp tài nguyên."""
        logger.debug("Đang đóng stream")
        self.is_running = False  # Đặt cờ dừng để không lấy khung hình nữa

    def close_container(self):
        """Đóng container và xóa tham chiếu."""
        if self.container:
            try:
                self.container.close()
            except Exception as e:
                logger.error("Lỗi khi đóng container: %s", str(e))
            finally:
                self.container = None  # Xóa tham chiếu đến container
                del self.container
                self.stream = None  # Xóa tham chiếu đến stream
                del self.stream
                self.decoder_ctx = None  # Xóa tham chiếu đến decoder context
                del self.decoder_ctx
                logger.debug("Đã đóng container và xóa tham chiếu")

    def get_frame(self, width=None, height=None, format=None, interpolation=None, callback=None):
        """
        Lấy khung hình tiếp theo từ stream.
        
        Args:
            width: Chiều rộng mong muốn cho khung hình đầu ra
            height: Chiều cao mong muốn cho khung hình đầu ra
            format: Định dạng mong muốn cho khung hình đầu ra
            interpolation: Phương pháp nội suy để sử dụng khi thay đổi kích thước
            callback: Hàm callback để xử lý khung hình
            
        Returns:
            tuple: (frame, timestamp) hoặc (None, None) nếu không lấy được khung hình
        """
        if not self.is_running:
            logger.debug("Dừng lấy khung hình do is_running=False")
            # Đóng container khi is_running = False
            self.close_container()
            return None, None
            
        if not self.container or not self.decoder_ctx:
            logger.warning("Không thể lấy khung hình - container hoặc decoder chưa được khởi tạo")
            return None, None
            
        try:
            for packet in self.container.demux(self.stream):
                # Kiểm tra cờ is_running
                if not self.is_running:
                    logger.debug("Dừng lấy khung hình do is_running=False")
                    # Đóng container khi is_running = False
                    self.close_container()
                    return None, None
                    
                for frame in self.decoder_ctx.decode(packet):
                    # Kiểm tra cờ is_running
                    if not self.is_running:
                        logger.debug("Dừng lấy khung hình do is_running=False")
                        # Đóng container khi is_running = False
                        self.close_container()
                        return None, None
                        
                    # Tính thời gian từ khi mở stream đến khi có frame đầu tiên
                    if hasattr(self, '_stream_start_time') and not hasattr(self, '_first_frame_time'):
                        self._first_frame_time = time.time()
                        time_to_first_frame = self._first_frame_time - self._stream_start_time
                        logger.debug("Thời gian từ khi mở stream đến frame đầu tiên: %.2f giây", time_to_first_frame)
                    
                    # Cập nhật tham số resize nếu được cung cấp
                    if width is not None:
                        self._target_width = width
                    if height is not None:
                        self._target_height = height
                    if format is not None:
                        self._target_format = format
                    if interpolation is not None:
                        self._interpolation = interpolation
                    
                    # Sử dụng VideoReformatter nếu cần thay đổi kích thước hoặc định dạng
                    if (self._target_width is not None or self._target_height is not None):
                        # Tạo reformatter mới nếu chưa có hoặc tham số đã thay đổi
                        if self._reformatter is None:
                            self._reformatter = VideoReformatter()
                        
                        # Áp dụng reformat
                        reformatted_frame = self._reformatter.reformat(
                            frame,
                            width=self._target_width,
                            height=self._target_height,
                            format=self._target_format,
                            interpolation=self._interpolation
                        )
                        # Chuyển đổi frame thành mảng numpy
                        frame_array = reformatted_frame.to_ndarray()
                    else:
                        # Chuyển đổi frame thành mảng numpy mà không cần reformat
                        frame_array = frame.to_ndarray(format=self._target_format)
                    # Gọi callback nếu được cung cấp
                    if callback is not None:
                        callback(frame_array, frame.time)
                    else:
                        
                        return frame_array, frame.time
                    
            logger.debug("Không có khung hình khả dụng")
            return None, None
            
        except Exception as e:
            logger.error("Lỗi khi lấy khung hình: %s", str(e))
            # Đóng container khi có lỗi
            self.close_container()
            return None, None

    def __del__(self):
        """Dọn dẹp tài nguyên khi đối tượng bị hủy."""
        logger.debug("Đang hủy instance PyAVWrapper")
        self.close_stream() 
import QtQuick.Window 2.2

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 2.15
import models 1.0
import "math_utils.js" as MMath

Item {
    id: root
    property var instance: (function(){
        console.log("timeLineManager.timeLineController ",timeLineManager.timeLineController)
        return timeLineManager.timeLineController
    })()
    Loader {
        id: loader
        // anchors.leftMargin: 10
        // anchors.rightMargin: 10
        // anchors.bottomMargin: 10
        anchors.fill: parent
        // anchors.centerIn: parent

        // sourceComponent: instance.isTimeLine ? timeline : notimeline
        sourceComponent: (function(){
            // console.log("instance.isTimeLine ",instance.isTimeLine)
            return instance.isTimeLine ? timeline : notimeline
        })()
    }
    Component {
        id: notimeline
        Rectangle {
            width: parent
            height: parent
            color: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("main_background") : "#FAFAFA"
            Text {
                text: timeLineManager.timeLineController.cameraName
                color: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("text_color_all_app") : "#C2C2C2"
                font.pixelSize: 24  
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.verticalCenter: parent.verticalCenter 
            }
        }
    }
    Component {
        id: timeline
        Rectangle {
            width: parent.width
            height: parent.height
            color: instance.theme ? instance.get_color_theme_by_key("main_background") : "#FAFAFA"
            LeftControl {
                id: left_control
                width: 200
                height: 120
                color: "transparent"
                anchors{
                    bottom: parent.bottom
                }
            }
            TimeLine {
                id: timerplayback
                width: parent.width - 400
                height: parent.height
                color: "transparent"
                anchors{
                    left: left_control.right
                }
            }
            RightControl {
                width: 200
                height: 120
                color: "transparent"
                anchors{
                    bottom: parent.bottom
                    left: timerplayback.right
                }
            }

        }
    }
    // Dialog {
    //     id: calendarDialog
    //     modal: true
    //     title: "Chọn ngày"
    //     standardButtons: Dialog.Ok | Dialog.Cancel
    //     width: 450
    //     height: 600

    //     Text {
    //         id: calendarView
    //         text: "ahihi"
    //         anchors.fill: parent
    //     }

    //     onAccepted: {
    //         console.log("Ngày đã chọn: " )
    //     }
    // }
    Connections {
        target: timeLineManager
        function timeLineControllerChanged() {
            instance = timeLineManager.timeLineController  // Cập nhật khi myObject thay đổi
        }
    }
}

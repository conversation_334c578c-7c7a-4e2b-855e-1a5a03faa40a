import re

from PySide6.QtSvgWidgets import QSvgWidget

from src.common.controller.controller_manager import Controller
from src.common.model.group_tree_model import GroupTreeModel
from src.common.model.user_role_model import role_model_manager, ChildrenRoleType
from src.common.threads.image_downloader import ImageDownloader
from src.common.widget.custom_standard_item import CustomStandardItem, CustomQTreeWidgetItem
from src.common.widget.menus.custom_menus import SubMenuAIFlow
from src.common.widget.tree_view_widget import TreeViewType
from src.presentation.device_management_screen.widget.ai_state import AIType
import src.utils.log_utils as LogUtils
from src.common.model.aiflows_model import AIFlowType
from src.utils.theme_setting import theme_setting
import logging
from src.common.model.item_grid_model import ItemGridModel
from src.common.widget.widget_for_custom_grid.item_in_menu import ItemGridMenu, ButtonEditLayout
from typing import List
from PySide6.QtWidgets import QWidget, QGridLayout, QGroupBox, QApplication, QFrame, QStackedLayout, QLabel, \
    QStyledItemDelegate, \
    QSizePolicy, QPushButton, QSlider, QHBoxLayout, QMenu, QDialog, QCheckBox, QComboBox, QVBoxLayout, QLineEdit, \
    QWidgetAction, QSpinBox, QStackedWidget, QFileDialog, QTreeView, QTreeWidget, QTreeWidgetItem
from PySide6.QtCore import Qt, Signal, QSize, QPoint, QEvent, Property, Slot, QEasingCurve, QPropertyAnimation, \
    QSequentialAnimationGroup, QRectF, QPointF, QSize, Qt, QPoint, QRegularExpression, QUrl
from PySide6.QtGui import QAction, Qt, QPixmap, QPainter, QBrush, QPen, QColor, QFont, QIcon, QPainterPath, \
    QIntValidator, QPaintEvent, QStandardItemModel, QStandardItem, QDoubleValidator, QGuiApplication, \
    QRegularExpressionValidator
from src.common.widget.button_state import ButtonState
# from src.common.widget.fullscreen.camera_stream import CameraStream
from src.common.controller.main_controller import main_controller
from src.presentation.device_management_screen.widget.multidropdown import MultiDropDownDialog, CameraGroupType
from src.styles.style import Style
from src.common.model.group_model import Group, GroupModel
from src.common.model.camera_model import CameraModel
from src.common.model.device_models import GroupType, CameraType
from src.common.onvif_api.worker_thread import WorkerThread
from src.common.model.aiflows_model import AiFlow,aiflow_model_manager,AiFlowModel
logger = logging.getLogger(__name__)


class CustomHeader(QWidget):
    def __init__(self, parent=None, header_data={}):
        super().__init__(parent)
        self.header_data = header_data

        self.header_layout = QHBoxLayout()
        self.header_layout.setContentsMargins(0, 0, 0, 0)
        self.header_layout.setSpacing(0)
        self.header_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        # self.setContentsMargins(0, 0, 0, 0)
        for key,widget in self.header_data.items():
            col = QWidget()
            # col.setStyleSheet('border:1px solid #FFFFFF')
            layout = QVBoxLayout()
            layout.setContentsMargins(0, 0, 0, 0)
            layout.addWidget(widget)
            if key == CameraType().camera_name or key == CameraType().camera_group or key == GroupType().group_name or key == CameraType().camera_branch or key == CameraType().ip_address or key == CameraType().type or key == CameraType().model:
                pass
            else:
                layout.setAlignment(Qt.AlignCenter)
            col.setLayout(layout)
            # if widget.objectName() == 'widget_check_box':
            #     col.setFixedWidth(100)
            self.header_layout.addWidget(col)
        self.setLayout(self.header_layout)
        self.setObjectName("custom_header")
        # self.setStyleSheet(f'background-color: {Style.PrimaryColor.on_background}; color: Style.PrimaryColor.white;')
        self.setStyleSheet(
                    f'''
                    QWidget#custom_header {{
                        background-color: {Style.PrimaryColor.on_background};
                        color: {Style.PrimaryColor.white_2};
                    }}
                    QLabel {{
                        color: {Style.PrimaryColor.white_2};
                        
                    }}
                    '''
                )
    def update_header_data(self):
        while self.header_layout.count():
            child = self.header_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        for key,widget in self.header_data.items():
            col = QWidget()
            layout = QVBoxLayout()
            layout.setContentsMargins(0, 0, 0, 0)
            layout.addWidget(widget)
            if key == CameraType().camera_name or key == CameraType().camera_group or key == GroupType().group_name or key == CameraType().camera_branch or key == CameraType().ip_address or key == CameraType().type or key == CameraType().model:
                pass
            else:
                layout.setAlignment(Qt.AlignCenter)
            col.setLayout(layout)
            self.header_layout.addWidget(col)

class ImageAdjWidget(QWidget):
    def __init__(self, parent=None,list_sliders = None, callback = None):
        super().__init__(parent)
        self.setObjectName("image_adj_widget")
        self.callback = callback
        self.list_sliders = list_sliders
        self.layout_image_adj = QVBoxLayout(self)
        self.layout_image_adj.setContentsMargins(0,0,0,0)
        self.brightness = CustomSlider(
            title=self.tr('Brightness'), min=0, max=100, default=100)
        self.sharpness = CustomSlider(
            title=self.tr('Sharpness'), min=0, max=100, default=50)
        self.contrast = CustomSlider(
            title=self.tr('Contrast'), min=0, max=100, default=50)
        self.color_saturation = CustomSlider(
            title=self.tr('Saturation'), min=0, max=100, default=50)
        self.control_image_adj_layout = QHBoxLayout()
        self.btn_ok_image_adj = QPushButton('Ok')
        self.btn_ok_image_adj.setFixedSize(60,25)
        self.btn_ok_image_adj.setStyleSheet(Style.StyleSheet.button_style1)
        self.btn_ok_image_adj.clicked.connect(self.btn_ok_clicked)
        self.btn_cancel_image_adj = QPushButton('Cancel')
        self.btn_cancel_image_adj.setFixedSize(60,25)
        self.btn_cancel_image_adj.setStyleSheet(Style.StyleSheet.button_style9)
        self.btn_cancel_image_adj.clicked.connect(self.btn_cancel_clicked)
        self.control_image_adj_layout.addWidget(self.btn_ok_image_adj)
        self.control_image_adj_layout.addWidget(self.btn_cancel_image_adj)
        self.control_image_adj_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label = QLabel('Image Adjustment')
        label.setStyleSheet(f'font-size: 16px;color: #FFFFFF')
        self.layout_image_adj.addWidget(label)
        self.layout_image_adj.addWidget(self.brightness)
        self.layout_image_adj.addWidget(self.sharpness)
        self.layout_image_adj.addWidget(self.contrast)
        self.layout_image_adj.addWidget(self.color_saturation)
        self.layout_image_adj.addLayout(self.control_image_adj_layout)
        self.brightness.hide()
        self.sharpness.hide()
        self.contrast.hide()
        self.color_saturation.hide()
        self.setStyleSheet(f"QLabel {{ color:#FFFFFF;}}"
                           f"QWidget#image_adj_widget {{ background-color: transparent;border:1px solid {Style.PrimaryColor.on_background};}}")
        
    def update_slider(self,list_sliders):
        self.brightness.show()
        self.sharpness.show()
        self.contrast.show()
        self.color_saturation.show()
        self.list_sliders = list_sliders

        self.brightness.slider.setMinimum(int(self.list_sliders['brightness']['Min']))
        self.brightness.slider.setMaximum(int(self.list_sliders['brightness']['Max']))
        self.brightness.slider.setValue(int(self.list_sliders['brightness']['Value']))
        self.sharpness.slider.setMinimum(int(self.list_sliders['sharpness']['Min']))
        self.sharpness.slider.setMaximum(int(self.list_sliders['sharpness']['Max']))
        self.sharpness.slider.setValue(int(self.list_sliders['sharpness']['Value']))
        self.contrast.slider.setMinimum(int(self.list_sliders['contrast']['Min']))
        self.contrast.slider.setMaximum(int(self.list_sliders['contrast']['Max']))
        self.contrast.slider.setValue(int(self.list_sliders['contrast']['Value']))
        self.color_saturation.slider.setMinimum(int(self.list_sliders['saturation']['Min']))
        self.color_saturation.slider.setMaximum(int(self.list_sliders['saturation']['Max']))
        self.color_saturation.slider.setValue(int(self.list_sliders['saturation']['Value']))
    def btn_ok_clicked(self):
        logger.debug('btn_ok_clicked')
        if self.callback is not None:
            self.callback()

    def btn_cancel_clicked(self):
        logger.debug('btn_cancel_image_adj_clicked')
        if self.list_sliders is not None:
            self.brightness.slider.setValue(int(self.list_sliders['brightness']['Value']))
            self.sharpness.slider.setValue(int(self.list_sliders['sharpness']['Value']))
            self.contrast.slider.setValue(int(self.list_sliders['contrast']['Value']))
            self.color_saturation.slider.setValue(int(self.list_sliders['saturation']['Value']))

class CustomSlider(QWidget):
    def __init__(self, parent=None, title='', min=0, max=100, default=50):
        super().__init__(parent)
        self.title = QLabel(title)
        self.slider = NoScrollSlider(Qt.Horizontal, parent)
        # self.slider.setFocusPolicy(Qt.NoFocus)
        self.slider.setSliderDown(False)
        self.slider.setMinimum(min)
        self.slider.setMaximum(max)
        self.slider.setValue(default)
        self.layout_main = QHBoxLayout()
        self.layout_main.setContentsMargins(0, 0, 0, 0)
        self.layout_main.addWidget(self.title, 30)
        self.layout_main.addWidget(self.slider, 70)
        self.setLayout(self.layout_main)
        self.setStyleSheet(Style.PrimaryStyleSheet.get_custom_slider_style(theme_instance=main_controller))

    def setTitle(self, title):
        self.title.setText(title)


class NoScrollSlider(QSlider):
    def wheelEvent(self, event):
        # Không làm gì nếu có sự kiện cuộn chuột
        event.ignore()


class CustomSwitch(QCheckBox):

    _transparent_pen = QPen(Qt.transparent)
    _light_grey_pen = QPen(Qt.lightGray)

    def __init__(self,
        parent=None,
        bar_color=Qt.gray,
        checked_color=Style.PrimaryColor.primary,
        handle_color=Qt.white,
        pulse_unchecked_color=Style.PrimaryColor.pulse_toggle_color,
        pulse_checked_color=Style.PrimaryColor.pulse_toggle_color
        ):
        super().__init__(parent)
        self.setMaximumSize(58, 45)
        # Save our properties on the object via self, so we can access them later
        # in the paintEvent.
        self._bar_brush = QBrush(bar_color)
        self._bar_checked_brush = QBrush(QColor(checked_color).lighter())

        self._handle_brush = QBrush(handle_color)
        self._handle_checked_brush = QBrush(QColor(checked_color))

        self._pulse_unchecked_animation = QBrush(QColor(pulse_unchecked_color))
        self._pulse_checked_animation = QBrush(QColor(pulse_checked_color))

        # Setup the rest of the widget.

        self.setContentsMargins(8, 0, 8, 0)
        self._handle_position = 0

        self._pulse_radius = 0

        self.animation = QPropertyAnimation(self, b"handle_position", self)
        self.animation.setEasingCurve(QEasingCurve.InOutCubic)
        self.animation.setDuration(200)  # time in ms

        self.pulse_anim = QPropertyAnimation(self, b"pulse_radius", self)
        self.pulse_anim.setDuration(350)  # time in ms
        self.pulse_anim.setStartValue(10)
        self.pulse_anim.setEndValue(20)

        self.animations_group = QSequentialAnimationGroup()
        self.animations_group.addAnimation(self.animation)
        self.animations_group.addAnimation(self.pulse_anim)

        self.stateChanged.connect(self.setup_animation)

        # margin left, right
        self.setStyleSheet(
            f'''
            QCheckBox{{
                border: none;
                padding: 5px;
                margin: 5px;
            }}
            ''')
    def mousePressEvent(self, *args, **kwargs):
        # tick on and off set here
        if self.isChecked():
            self.setChecked(False)
        else:
            self.setChecked(True)
        if self.clicked != None:
            self.clicked()

    def sizeHint(self):
        return QSize(58, 45)

    def hitButton(self, pos: QPoint):
        return self.contentsRect().contains(pos)

    @Slot(int)
    def setup_animation(self, value):
        self.animations_group.stop()
        if value:
            self.animation.setEndValue(1)
        else:
            self.animation.setEndValue(0)
        self.animations_group.start()

    def paintEvent(self, e: QPaintEvent):

        contRect = self.contentsRect()
        handleRadius = round(0.24 * contRect.height())

        p = QPainter(self)
        p.setRenderHint(QPainter.Antialiasing)

        p.setPen(self._transparent_pen)
        barRect = QRectF(
            0, 0,
            contRect.width() - handleRadius, 0.40 * contRect.height()
        )
        barRect.moveCenter(contRect.center())
        rounding = barRect.height() / 2

        # the handle will move along this line
        trailLength = contRect.width() - 2 * handleRadius

        xPos = contRect.x() + handleRadius + trailLength * self._handle_position

        if self.pulse_anim.state() == QPropertyAnimation.Running:
            p.setBrush(
                self._pulse_checked_animation if
                self.isChecked() else self._pulse_unchecked_animation)
            p.drawEllipse(QPointF(xPos, barRect.center().y()),
                          self._pulse_radius, self._pulse_radius)

        if self.isChecked():
            p.setBrush(self._bar_checked_brush)
            p.drawRoundedRect(barRect, rounding, rounding)
            p.setBrush(self._handle_checked_brush)

        else:
            p.setBrush(self._bar_brush)
            p.drawRoundedRect(barRect, rounding, rounding)
            p.setPen(self._light_grey_pen)
            p.setBrush(self._handle_brush)

        p.drawEllipse(
            QPointF(xPos, barRect.center().y()),
            handleRadius, handleRadius)

        p.end()

    @Property(float)
    def handle_position(self):
        return self._handle_position

    @handle_position.setter
    def handle_position(self, pos):
        """change the property
        we need to trigger QWidget.update() method, either by:
            1- calling it here [ what we're doing ].
            2- connecting the QPropertyAnimation.valueChanged() signal to it.
        """
        self._handle_position = pos
        self.update()

    @Property(float)
    def pulse_radius(self):
        return self._pulse_radius

    @pulse_radius.setter
    def pulse_radius(self, pos):
        self._pulse_radius = pos
        self.update()


class SearchWidget(QWidget):
    # Signal to search items
    search_items_signal = Signal(str)

    def __init__(self, parent):
        super().__init__(parent)
        self.camera_screen = parent
        self.search_bar = QLineEdit()
        self.search_bar.setPlaceholderText(self.tr("Search"))
        self.search_bar.setFixedHeight(30)
        # self.search_bar.setStyleSheet(Path('src/common/widget/search_widget/search.qss').read_text())

        # link self.search_bar to self.tree_view
        self.search_bar.textChanged.connect(self.search_items)

        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        self.layout.addWidget(self.search_bar)
        self.setLayout(self.layout)

    # create a callback function to search items
    def search_items(self, text):
        # logger.debug(text)
        self.search_items_signal.emit(text)
        pass


# class CustomLabelTooltip(QWidget):
#     def __init__(self, parent=None, title='', tooltip=''):
#         super().__init__(parent)
#         layout = QHBoxLayout()
#         self.setLayout(layout)
#         self.label = QLabel(title)
#         self.label.setToolTip(tooltip)
#         layout.addWidget(self.label)

# class CustomLabelIcon(QLabel):
#     def __init__(self, parent=None, icon_bottom=None, title=None):
#         super().__init__(parent)
#         self.parent = parent
#         self.tilte = title
#         self.clicked = None
#         self.icon_bottom = Style.PrimaryImage.expand_bottom if icon_bottom == None else icon_bottom
#         self.pixmap_bottom = QPixmap(self.icon_bottom)
#         self.setPixmap(self.pixmap_bottom)
#         self.setFixedSize(self.pixmap_bottom.width(),
#                           self.pixmap_bottom.height())

# class CustomLabelIcon(QLabel):
#     def __init__(self, parent=None, icon_bottom=None, title=None):
#         super().__init__(parent)
#         self.parent = parent
#         self.tilte = title
#         self.clicked = None
#         self.icon_bottom = Style.PrimaryImage.expand_bottom if icon_bottom == None else icon_bottom
#         self.pixmap_bottom = QPixmap(self.icon_bottom)
#         self.setPixmap(self.pixmap_bottom)
#         self.setFixedSize(self.pixmap_bottom.width(),
#                           self.pixmap_bottom.height())
#
#     # Thêm xử lý tại đây
#     # Thêm xử lý tại đây
#     def enterEvent(self, event):
#         pass
#
#     def leaveEvent(self, event):
#         pass
#
#     def mousePressEvent(self, event):
#         if self.clicked != None:
#             self.clicked(event)


# class CustomIcon(QWidget):
#     def __init__(self, parent=None, icon=None, title='', icon_clicked=None):
#         super().__init__(parent)
#         layout = QHBoxLayout()
#         self.setLayout(layout)
#         self.icon_clicked = icon_clicked
#         self.button = QToolButton()
#         self.button.setIconSize(QSize(25, 25))
#         self.pixmap = QPixmap(icon)
#         # Tạo một QIcon từ QPixmap
#         self.icon = QIcon(self.pixmap)

#         # Thiết lập custom icon cho QToolButton
#         self.button.setIcon(self.icon)
#         self.button.setCheckable(False)
#         self.button.setChecked(True)
#         self.button.setToolTip(title)
#         self.button.clicked.connect(self.iconClicked)
#         layout.addWidget(self.button)
#         #self.setStyleSheet('border: none; background-color: transparent;')

#     def setDisabled(self, value):
#         self.button.setDisabled(value)

#     def iconClicked(self):
#         self.icon_clicked()

class CustomIcon(QWidget):
    def __init__(self, parent=None, icon=None, title='', icon_clicked=None,style = None, button_size = 28, icon_size = 28):
        super().__init__(parent)
        self.setMouseTracking(True)
        self.setFocusPolicy(Qt.StrongFocus)
        self.is_hover = False
        layout = QHBoxLayout()
        layout.setContentsMargins(0,0,0,0)
        layout.setSpacing(0)
        self.setLayout(layout)
        self.icon_clicked = icon_clicked
        self.button = QPushButton()
        self.button.setFixedSize(button_size,button_size)
        if style is not None:
            self.button.setStyleSheet(style)
        else:
            self.button.setStyleSheet(Style.StyleSheet.button_style4)
        self.button.setIconSize(QSize(icon_size, icon_size))
        self.pixmap = QPixmap(icon)
        # Tạo một QIcon từ QPixmap
        self.icon = QIcon(self.pixmap)

        # Thiết lập custom icon cho QToolButton
        self.button.setIcon(self.icon)
        self.button.clicked.connect(self.iconClicked)
        layout.addWidget(self.button)

    # need override all event press, release, enter, leave in here
    def enterEvent(self, event):
        self.is_hover = True

    def leaveEvent(self, event):
        self.is_hover = False

    def set_icon(self, icon = None):
        self.button.setIcon(QIcon(QPixmap(icon)))

    def setToolTipsCustom(self, tool_tips):
        self.button.setToolTip(tool_tips)

    def setDisabled(self, value):
        self.button.setDisabled(value)

    def iconClicked(self):
        self.icon_clicked()

class StatusWidget(QWidget):
    def __init__(self,parent = None,camera_model: CameraModel = None):
        super().__init__(parent)
        self.setMouseTracking(True)
        self.camera_model = camera_model
        if self.camera_model is not None:
            # self.camera_model.change_active_signal.connect(self.update_status)
            self.camera_model.change_model.connect(self.update_state)
        # camera_data_model.change_status_signal.connect(self.update_status)
        layout = QHBoxLayout(self)
        self.status_on = QPixmap(Style.PrimaryImage.state_online)
        self.status_off = QPixmap(Style.PrimaryImage.state_offline)
        pixmap = QPixmap(Style.PrimaryImage.state_online) if camera_model.data.state == "CONNECTED" else QPixmap(Style.PrimaryImage.state_offline)
        self.status = camera_model.data.active
        self.label = QLabel()
        self.label.setPixmap(pixmap) 
        layout.addWidget(self.label)
        layout.setAlignment(self.label, Qt.AlignCenter)

    def update_state(self, data):
        key, value, model = data
        if key == 'state':
            if value == "CONNECTED":
                self.label.setPixmap(self.status_on)
            elif value == "DISCONNECTED":
                self.label.setPixmap(self.status_off)

    def update_status(self, data):
        key, value, model = data
        if key == 'active':
            if value:
                self.label.setPixmap(self.status_on)
            else:
                self.label.setPixmap(self.status_off)


class NoScrollComboBox(QComboBox):
    def __init__(self, parent=None):
        super().__init__(parent)

        self.set_dynamic_stylesheet()

    def wheelEvent(self, event):
        # Không làm gì nếu có sự kiện cuộn chuột
        event.ignore()

    def set_dynamic_stylesheet(self):
        self.view().parentWidget().setStyleSheet(
            f'background: {main_controller.get_theme_attribute("Color", "main_background")}')
        self.setStyleSheet(f"""
            QComboBox{{
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                border-radius: 4px;
                background: transparent;
                padding: 4px 16px;
            }}
            QComboBox::disabled{{
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                border-radius: 4px;
                background: transparent;
            }}

            QComboBox QAbstractItemView {{
                 border: None;;
                 color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                 selection-background-color: {Style.PrimaryColor.primary};
                 background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                 border-radius: 4px;
                 padding: 4px;
                 width: 16px;
                height: 16px;
            }}

            QComboBox QAbstractItemView::indicator:checked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                background-color:  {main_controller.get_theme_attribute("Color", "main_background")};
                padding: 4px;
                width: 16px;
                height: 16px;
            }}

            QComboBox QAbstractItemView::indicator:unchecked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                background-color:  {main_controller.get_theme_attribute("Color", "main_background")};
                padding: 4px;
                width: 16px;
                height: 16px;
            }}
            
            QComboBox::item:selected {{
                background-color: {Style.PrimaryColor.primary};
                color: #FFFFFF;
                border-radius: 2px;
            }}
            QComboBox::down-arrow {{
                image: url({Style.PrimaryImage.down_arrow_linedit});
            }}
            QComboBox::drop-down {{
              border-left: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
              }}
              
            
        """)



class CustomComboBox(QWidget):
    def __init__(self, parent=None, title='', data=None, note=None, widget_type="combobox", layout_type="HBox", title_style=None):
        super().__init__(parent)
        self.data = data
        self.title = title
        self.layout_type = layout_type
        if self.layout_type == "HBox":
            self.main_layout = QVBoxLayout()
        else:
            self.main_layout = QHBoxLayout()

        self.label_title = QLabel(self.title)
        self.main_layout.addWidget(self.label_title)
        if widget_type != "combobox":
            self.warning_layout = QHBoxLayout()
            self.count = QLineEdit()
            self.count.setFixedWidth(40)
            self.count.setStyleSheet(f'''
                QLineEdit {{
                    background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                    border: 1px solid gray;
                    border-radius: 4px;
                    padding: 4px;
                    color: #333;
                }}
                
                QLineEdit:focus {{
                    border: 2px solid #B5122E;
                }}
                
                QLineEdit:disabled {{
                    background-color: #e0e0e0;
                    color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                }}''')
            self.warning_layout.addWidget(self.count)
            self.warning_layout.addWidget(QLabel(self.tr('Object Quantity')))

            self.main_layout.addLayout(self.warning_layout)
        else:
            self.combobox = QComboBox()
            self.combobox.wheelEvent = self.combobox_wheel_event
            font = QFont()
            self.combobox.setFont(font)
            self.combobox_clicked = None

            if self.data is not None:
                for item in self.data:
                    if item != self.data[-1]:
                        self.combobox.addItem(self.translate_item_value(item[1]))
            self.combobox.currentIndexChanged.connect(self.clicked)
            if note is not None:
                label = QLabel(note)
                font = QFont()
                font.setItalic(True)
                label.setFont(font)
                self.main_layout.addWidget(label)

            self.main_layout.addWidget(self.combobox)
        self.setup_stylesheet()
        self.setLayout(self.main_layout)

    def translate_item_value(self, item):
        match item:
            case 'Please select':
                item = self.tr('Please select')
                return item
            case 'High (Default)':
                item = self.tr('High (Default)')
                return item
            case 'Medium':
                item = self.tr('Medium')
                return item
            case 'Low':
                item = self.tr('Low')
                return item
            case 'x1 (Default)':
                item = self.tr('x1 (Default)')
                return item
            case '15 minutes (Default)':
                item = self.tr('15 minutes (Default)')
                return item
            case '20 minutes':
                item = self.tr('20 minutes')
                return item
            case '25 minutes':
                item = self.tr('25 minutes')
                return item
            case '56 (Default)':
                item = self.tr('56 (Default)')
                return item
            case '0.5 (Default)':
                item = self.tr('0.5 (Default)')
                return item
            case '0.48 (Default)':
                item = self.tr('0.48 (Default)')
                return item
            case '21 (Default)':
                item = self.tr('21 (Default)')
                return item
            case '5 (Default)':
                item = self.tr('5 (Default)')
                return item
            case '10 Object Quantity':
                item = self.tr('10 Object Quantity')
                return item
            case '20 Object Quantity':
                item = self.tr('20 Object Quantity')
                return item
            case '30 Object Quantity':
                item = self.tr('30 Object Quantity')
                return item
            case '40 Object Quantity':
                item = self.tr('40 Object Quantity')
                return item
            case '50 Object Quantity':
                item = self.tr('50 Object Quantity')
                return item
            case '60 Object Quantity':
                item = self.tr('60 Object Quantity')
                return item
            case '70 Object Quantity':
                item = self.tr('70 Object Quantity')
                return item
            case '80 Object Quantity':
                item = self.tr('80 Object Quantity')
                return item
            case '90 Object Quantity':
                item = self.tr('90 Object Quantity')
                return item
            case '100 Object Quantity':
                item = self.tr('100 Object Quantity')
                return item
            case '200 Object Quantity':
                item = self.tr('200 Object Quantity')
                return item
            case '300 Object Quantity':
                item = self.tr('300 Object Quantity')
                return item
            case '400 Object Quantity':
                item = self.tr('400 Object Quantity')
                return item
            case '500 Object Quantity':
                item = self.tr('500 Object Quantity')
                return item
            case default:
                    return item

    def combobox_wheel_event(self, event):
        event.ignore()

    def clicked(self, index):
        if self.combobox_clicked !=None:
            self.combobox_clicked(index)

    def setTitle(self, title):
        self.title = title
        self.label_title.setText(self.title)

    def setup_stylesheet(self):
        self.setStyleSheet(f'''
            QComboBox {{
                background-color: transparent;
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                border-radius: 4px;
                padding: 4px;
            }}
            QComboBox::down-arrow {{
                image: url({Style.PrimaryImage.down_arrow_linedit});
            }}
            QComboBox::drop-down {{
                border-left: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
            }}
            QLabel{{
                background-color: transparent;
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                }}
        ''')

class InputText(QWidget):
    def __init__(self, parent = None,title = ''):

        super().__init__(parent)
        self.title = title
        self.load_ui()
        self.setup_style_sheet()
        # self.load_data()

    def load_ui(self):
        self.main_layout = QVBoxLayout()
        # self.main_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # self.main_layout.setContentsMargins(0, 0, 0, 0)
        # self.main_layout.setSpacing(2)
        self.label_title = QLabel(self.title)
        self.label_title.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'label_title_1')}; "
                                       f"background-color: transparent;"
                                       f"font-weight: 500;")
        self.line_edit = QLineEdit()
        self.label_warning = QLabel("abc")
        self.label_warning.setStyleSheet(f"color: #d40e0e; "
                                       f"background-color: transparent;"
                                       f"font-weight: 500;"
                                       f"font-size: 12px;")
        self.label_warning.setContentsMargins(8, 0, 0, 0)
        self.label_warning.hide()
        self.line_edit.textChanged.connect(self.label_warning.hide)
        
        # self.line_edit.textChanged.connect(self.on_text_changed)
        # self.line_edit.setReadOnly(self.is_read_only)

        # if self.double_validator:
        #     double_validator = QDoubleValidator()
        #     double_validator.setNotation(QDoubleValidator.Notation.StandardNotation)
        #     self.line_edit.setValidator(double_validator)
        # if self.text_placeholder is not None:
        #     self.line_edit.setPlaceholderText(self.text_placeholder)
        # if self.show_arrow:
        #     self.arrow_action = QAction(QIcon(Style.PrimaryImage.down_arrow_linedit), self.tr("Show menu"), self)
        #     self.arrow_action.triggered.connect(self.icon_arrow_click)
        #     self.line_edit.addAction(self.arrow_action, QLineEdit.ActionPosition.TrailingPosition)
        #     self.line_edit.mousePressEvent = self.click_line_edit
        self.main_layout.addWidget(self.label_title)
        self.main_layout.addWidget(self.line_edit)
        self.main_layout.addWidget(self.label_warning)
        self.setLayout(self.main_layout)

    def setup_style_sheet(self):
        self.setStyleSheet(f'''
            /*-----QWidget-----*/
            QWidget{{
                background-color: {Style.PrimaryColor.on_background};}}
            /*-----QLabel-----*/
            QLabel{{
                background-color: transparent;
                color: {main_controller.get_theme_attribute('Color', 'label_title_1')};
                }}
            /*-----QLineEdit-----*/
            QLineEdit{{
                background-color: transparent;
                border-radius: 4px;
                color: {main_controller.get_theme_attribute('Color', 'label_title_1')};
                letter-spacing: 0em;
                text-align: left;
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                padding: 4px 16px;
                }}
            QLineEdit::focus{{
                border: 1px solid {main_controller.get_theme_attribute("Color", "add_server_lineedit_focused")};
                color: {main_controller.get_theme_attribute('Color', 'label_title_1')} }}
            QLineEdit:!focus{{
                color: {main_controller.get_theme_attribute('Color', 'label_title_1')};
                }}
            QLineEdit::placeholder{{
                color: {main_controller.get_theme_attribute("Color", "main_border")};
                }}

        ''')

class SearchComboBox(QWidget):
    def __init__(self, parent=None, title='', data=None,title_style = None,combobox_clicked = None):
        super().__init__(parent)
        self.data = data
        self.main_layout = QVBoxLayout()
        # self.main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.title = title
        if self.title != '':
            self.qlabel = QLabel(self.title)
            self.qlabel.setStyleSheet(title_style)
            self.main_layout.addWidget(self.qlabel)
        
        self.combobox = NoScrollComboBox()
        # font = self.combobox.font()
        font = QFont()
        font.setPixelSize(Style.Size.caption)
        self.combobox.setFont(font)
        self.combobox_clicked = combobox_clicked
        # model = QStandardItemModel(
        #     5, 1)
        # if self.data != None:
        #     for item in self.data:
        #         model.appendRow(QStandardItem(item))
        if self.data != None:
            for item in self.data:
                self.combobox.addItem(item)
                
        self.combobox.currentIndexChanged.connect(self.clicked)
        self.main_layout.addWidget(self.combobox)
        self.setLayout(self.main_layout)
        self.set_dynamic_stylesheet()

    def set_data(self,data):
        self.data = data
        self.combobox.clear()
        for item in self.data:
            self.combobox.addItem(item)

    def clicked(self,index):
        if self.combobox_clicked !=None:
            self.combobox_clicked(index)

    def setTitle(self, title):
        if self.title != '':
            self.title = title
            self.qlabel.setText(self.title)

    def set_dynamic_stylesheet(self):
        self.combobox.set_dynamic_stylesheet()
        self.setStyleSheet(f"""background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};""")


class ButtonDrawDialog(QPushButton):
    def __init__(self, text='', icon: QIcon = None, button_size: int = 28, icon_size: int = 24, type = None):
        super().__init__(icon, text)

        self.setIconSize(QSize(icon_size, icon_size))
        # set size
        self.setMaximumSize(button_size, button_size)
        self.setFixedSize(button_size, button_size)
        self.clicked = None

        if type == "check_ok":
            self.setStyleSheet(
                f'''
                    QPushButton {{
                        background-color: #B5122E;
                        border-radius: 4px;
                    }}
                    QPushButton:hover {{
                        color: white;
                        background-color: rgba(204, 80, 81, 1);
                        border-radius: 4px;
                    }}
                    QPushButton:pressed {{
                        color: white;
                        background-color: rgba(181, 18, 46, 1);
                        border-radius: 4px;
                    }}
                    '''
            )
        elif type == "draw":
            self.setStyleSheet(
                f'''
                    QPushButton {{
                        color: white;
                        background-color: rgba(48, 55, 80, 1);
                        border-radius: 4px;
                    }}
                    QPushButton:hover {{
                        color: white;
                        background-color: rgba(204, 80, 81, 1);
                        border-radius: 4px;
                    }}
                    QPushButton:pressed {{
                        color: white;
                        background-color: rgba(181, 18, 46, 1);
                        border-radius: 4px;
                    }}
                    '''
            )
        else:
            # create style sheet hover press and pressed
            self.setStyleSheet(
                f'''
                QPushButton {{
                    background-color: white;
                    border-radius: 4px;
                    border: 1px solid #B5122E;
                }}
                QPushButton:hover {{
                    background-color: rgba(204, 80, 81, 1);
                    border-radius: 4px;
                    border: 1px solid #B5122E;
                }}
                QPushButton:pressed {{
                    background-color: rgba(181, 18, 46, 1);
                    border-radius: 4px;
                    border: 1px solid #B5122E;
                }}
                '''
            )

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.clicked()

class SquareButton(QPushButton):
    action_signal = Signal(tuple)
    signal_emit_only_grid = Signal(tuple)

    # add icon
    def __init__(self, text='', icon: QIcon = None, button_size: int = 32, icon_size: int = 32,
                 type_button=ButtonState.ButtonType.ON_OFF, style: str = None,
                 list_menu_name: List = None, list_menu_icon: List = None, feature=None,
                 list_item_grid_model=None, button_state = ButtonState.OFF):
        super().__init__(icon, text)
        if feature is None:
            logger.debug('Feature Button must be not None, please set feature for button')
            return
        self.button_state = button_state
        self.feature = feature
        self.type = type_button
        self.list_item_grid_model = list_item_grid_model
        self.main_menu = None
        self.grid_custom = None
        self.grid_standard = None
        self.setIconSize(QSize(28, 28))
        # set size
        self.setMaximumSize(button_size, button_size)
        self.clicked = None
        if list_menu_name or list_menu_icon or list_item_grid_model:
            self.list_menu_name = list_menu_name
            self.list_menu_icon = list_menu_icon
            self.list_item_grid_model = list_item_grid_model
            self.type = ButtonState.ButtonType.MENU

        if style is not None:
            self.setStyleSheet(style)
        else:
            # create style sheet hover press and pressed
            self.setStyleSheet(
                f'''
                QPushButton {{
                    background-color: rgba(48, 55, 80, 1);
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    background-color: rgba(204, 80, 81, 1);
                    border-radius: 4px;
                }}
                QPushButton:pressed {{
                    background-color: rgba(181, 18, 46, 1);
                    border-radius: 4px;
                }}
                '''
            )
        if self.type == ButtonState.ButtonType.MENU:
            self.create_menu(list_menu_name=self.list_menu_name, list_icon=self.list_menu_icon, list_item_grid_model=list_item_grid_model)
        # set policy
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setup_style_sheet_menu()

    def mousePressEvent(self, event):
        if self.type == ButtonState.ButtonType.MENU:
            if event.button() == Qt.LeftButton:
                # Lấy vị trí của con trỏ chuột và hiển thị QMenu tại đó
                pos = self.mapToGlobal(event.pos())
                self.main_menu.exec(pos)
        elif self.type == ButtonState.ButtonType.ON_OFF:
            if self.clicked != None:
                self.clicked()
        # elif self.type == ButtonState.ButtonType.DIALOG:
        #     #if event.button() == Qt.LeftButton:
        #         self.volume_dialog = VolumeDialog()
        #         pos = self.mapToGlobal(QPoint(14 - self.volume_dialog.width()/2, -(self.volume_dialog.height() + 10)))
        #         self.volume_dialog.move(pos)
        #         self.volume_dialog.show()

    def create_menu(self, list_menu_name: List = None, list_icon: List = None, list_item_grid_model=None):
        self.main_menu = QMenu(self)
        self.main_menu.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.main_menu.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        # set style self.menu, item, enable, disable

        if list_item_grid_model:
            standard_grid, custom_grid = list_item_grid_model
            self.grid_standard = QGridLayout()
            self.grid_standard.setSpacing(2)
            self.grid_standard.setAlignment(Qt.AlignmentFlag.AlignLeft)
            # Add items to the grid layout
            row_standard, col_standard = 0, 0
            for data in standard_grid:
                item_widget = ItemGridMenu(model_grid=data, type_division="STANDARD_DIVISION")
                item_widget.emit_size_signal.connect(self.select_grid_type_value)
                self.grid_standard.addWidget(item_widget, row_standard, col_standard)
                col_standard += 1
                if col_standard == 6:
                    col_standard = 0
                    row_standard += 1

            self.grid_custom = QGridLayout()
            self.grid_custom.setSpacing(2)
            self.grid_custom.setAlignment(Qt.AlignmentFlag.AlignLeft)
            row_custom, col_custom = 0, 0
            for data_custom in custom_grid:
                widget_custom = ItemGridMenu(model_grid=data_custom, type_division="CUSTOM_DIVISIONS")
                widget_custom.emit_size_signal.connect(self.select_grid_type_value)
                self.grid_custom.addWidget(widget_custom, row_custom, col_custom)
                col_custom += 1
                if col_custom == 6:
                    col_custom = 0
                    row_custom += 1

            self.button_edit_grid = ButtonEditLayout(title=self.tr("Edit"), image_path=main_controller.get_theme_attribute("Image", "edit_layout_grid"))
            self.button_edit_grid.button_click_signal.connect(
                lambda: self.signal_emit_only_grid.emit((0, self.feature, "EDIT_LAYOUTS_TRIGGERED")))
            self.grid_custom.addWidget(self.button_edit_grid, 0, 4)

            custom_widget = QWidget(self)
            self.custom_layout = QVBoxLayout()
            self.label_standard = QLabel(self.tr("Standard Window Division"))
            self.label_custom = QLabel(self.tr("Custom Window Division"))

            self.custom_layout.addWidget(self.label_standard)
            self.custom_layout.addLayout(self.grid_standard)
            self.custom_layout.addWidget(self.label_custom)
            self.custom_layout.addLayout(self.grid_custom)

            custom_widget.setLayout(self.custom_layout)

            custom_action = QWidgetAction(self)
            custom_action.setDefaultWidget(custom_widget)
            self.main_menu.addAction(custom_action)
        else:
            length = 0
            if len(list_menu_name) > len(list_icon):
                length = len(list_menu_name)
            else:
                length = len(list_icon)

            # create list menu use for
            for i in range(length):
                # create action and set text
                if list_menu_name[i]:
                    action = QAction(list_menu_name[i], self)
                    if list_menu_name[i] == "AI Stream" or list_menu_name[i] == "Sub":
                        action.setDisabled(True)
                else:
                    action = QAction(self)
                # set icon
                if list_icon[i]:
                    icon = QIcon(list_icon[i])
                    # Set the size of the icon
                    size = QSize(16, 16)  # Example size (adjust as needed)
                    pixmap = icon.pixmap(size)

                    # Create a new QIcon with the resized pixmap
                    resized_icon = QIcon(pixmap)
                    action.setIcon(QIcon(resized_icon))

                # add action to menu

                self.main_menu.addAction(action)
                # connect trigger and send signal number of action
                action.triggered.connect(self.action_triggered)

    def update_menu_grid(self, new_data: List[ItemGridModel]):
        # find and remove item grid change
        for item in self.grid_custom.findChildren(ItemGridMenu):
            # find item.model_grid.name_grid in new_data then update to grid
            for new_item in new_data:
                if item.model_grid.name_grid == new_item.name_grid:
                    item.model_grid = new_item
                    item.update_ui()
                    break

    def select_grid_type_value(self, value):
        combined_arg = (0, self.feature, value)
        self.signal_emit_only_grid.emit(combined_arg)

    # action trigger and emit signal what action is clicked
    def action_triggered(self):
        # get current action triggered
        action = self.sender()
        index = self.main_menu.actions().index(action)
        data_emitted = action.data()
        idx = None
        camera_id = None
        ai_type = None
        if data_emitted is not None:
            idx,ai_type, camera_id = data_emitted
        if self.list_menu_icon:
            if index < 2: 
                self.setIcon(QIcon(self.list_menu_icon[index]))
            else:
                self.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_3_off")))
        combined_arg = (index, ai_type, camera_id)
        # emit signal
        self.action_signal.emit(combined_arg)

    def set_button_state(self, button_state):
        self.button_state = button_state
    
    def add_action(self, ai_type = None,model = None):
        name, icon = self.get_text(ai_type)
        action = QAction(name, self)
        action.setIcon(QIcon(icon))
        self.main_menu.addAction(action)
        action.setData((None,ai_type,model.data.id))
        action.triggered.connect(self.action_triggered)
        # self.list_menu_icon.append(ai_type)

    def get_text(self, ai_type):
        if ai_type == AIFlowType.RECOGNITION:
            return (self.tr("Recognition"),Style.PrimaryImage.ic_recognition_security)
        elif ai_type == AIFlowType.PROTECTION:
            return (self.tr("Protection"),Style.PrimaryImage.ic_recognition_security)
        elif ai_type == AIFlowType.FREQUENCY:
            return (self.tr("Frequency"),Style.PrimaryImage.ic_recognition_security)
        elif ai_type == AIFlowType.ACCESS:
            return (self.tr("Access"),Style.PrimaryImage.ic_recognition_security)
        elif ai_type == AIFlowType.MOTION:
            return (self.tr("Motion"),Style.PrimaryImage.ic_recognition_security)
        elif ai_type == AIFlowType.TRAFFIC:
            return (self.tr("Traffic"),Style.PrimaryImage.ic_risk_identification)
        elif ai_type == AIFlowType.WEAPON:
            return (self.tr("Weapon"),Style.PrimaryImage.ic_risk_identification)
        elif ai_type == AIFlowType.UFO:
            return (self.tr("UFO"),Style.PrimaryImage.ic_risk_identification)
        return None
    def setup_style_sheet_menu(self):
        if self.main_menu is not None:
            self.main_menu.setStyleSheet(
                f'''QMenu {{
                            background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                            border-radius: 5px;
                            color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                            padding: 2px 4px 4px 2px;
                        }}
                        QMenu::item {{
                            background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                            border-radius: 4px;
                            color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                            padding: 2px 4px 4px 2px;
                        }}
                        QMenu::item:selected {{
                            background-color: {Style.PrimaryColor.primary};
                            border-radius: 4px;
                            color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                            padding: 2px 4px 4px 2px;
                        }}
                        QMenu::item:disabled {{
                            background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                            border-radius: 4px;
                            color: #999999;
                            padding: 2px 4px 4px 2px;
                        }}
                        ''')
        if self.list_item_grid_model:
            if self.grid_standard is not None:
                row_count = self.grid_standard.rowCount()
                col_count = self.grid_standard.columnCount()
                for row in range(row_count):
                    for col in range(col_count):
                        item = self.grid_standard.itemAtPosition(row, col)
                        if item is not None:  # Check if the position is occupied
                            widget: ItemGridMenu = item.widget()
                            if widget:
                                widget.setup_dynamic_stylesheet()
            if self.grid_custom is not None:
                row_count = self.grid_custom.rowCount()
                col_count = self.grid_custom.columnCount()
                for row in range(row_count):
                    for col in range(col_count):
                        item = self.grid_custom.itemAtPosition(row, col)
                        if item is not None:  # Check if the position is occupied
                            widget: ItemGridMenu = item.widget()
                            if widget:
                                widget.setup_dynamic_stylesheet()
            self.button_edit_grid.setup_dynamic_stylesheet(image_path=main_controller.get_theme_attribute('Image', 'edit_layout_grid'))
            self.label_standard.setStyleSheet(
                f"color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; font-weight: 500 ")
            self.label_custom.setStyleSheet(
                f"color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; font-weight: 500 ")

    def change_list_grid_model(self, new_list_grid_model):
        self.list_item_grid_model = new_list_grid_model
        self.create_menu(list_item_grid_model=new_list_grid_model)
        self.setup_style_sheet_menu()



class CustomLineEdit(QGroupBox):
    def __init__(self,title = '',key = None, double_validator=False):
        super().__init__()
        self.setTitle(title)
        self.key = key
        self.setStyleSheet("""
            QGroupBox {
                font: bold;
                border: 1px solid silver;
                border-radius: 6px;
                margin-top: 6px;
                background-color: #FFFFFF;
                color: #000000;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 7px;
                padding: 0px 5px 0px 5px;
                color: #000000;
            }""")
        self.callback_on_text_changed = None
        self.line_edit = QLineEdit()
        if double_validator:
            double_validator = QDoubleValidator()
            double_validator.setNotation(QDoubleValidator.Notation.StandardNotation)
            self.line_edit.setValidator(double_validator)
        self.line_edit.textChanged.connect(self.on_text_changed)
        self.line_edit.setStyleSheet("border: none; background-color: #FFFFFF;font-size: 14px;")
        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(0,8,0,8)
        self.layout.addWidget(self.line_edit)
        self.setLayout(self.layout)
    def on_text_changed(self,text):
        if self.callback_on_text_changed is not None:
            self.callback_on_text_changed(text = text,key = self.key)
    def setText(self,text):
        self.line_edit.setText(text)
        

    def text(self):
        return self.line_edit.text()

class LabelLineEdit(QWidget):

    def __init__(self, title = '',width = 150,key = None):
        super().__init__()
        self.key = key
        self.callback_on_text_changed = None
        self.main_layout = QHBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        label = QLabel(title)
        self.line_edit = InputWithNewStyle()
        self.line_edit.setPlaceholderText(self.tr('Exp: 5, 13, 202,...'))
        self.line_edit.setValidator(QIntValidator())
        self.line_edit.textChanged.connect(self.on_text_changed)
        self.main_layout.addWidget(label)
        self.main_layout.addWidget(self.line_edit)
        self.setLayout(self.main_layout)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        # stylesheet
        label.setStyleSheet(f'color: {main_controller.get_theme_attribute("Color", "text_color_all_app")}')

    def on_text_changed(self, text):
        if self.callback_on_text_changed is not None:
            self.callback_on_text_changed(text = text,key = self.key)

    def setText(self,text):
        self.line_edit.setText(text)

    def text(self):
        return self.line_edit.text()
class CheckBoxWidget(QWidget):
    def __init__(self, title = '',content = None,key = None, style = None,callback_statechanged = None):
        super().__init__()
        self.key = key
        self.callback_statechanged = callback_statechanged
        self.main_layout = QVBoxLayout()
        self.main_layout = QGridLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.setLayout(self.main_layout)

        self.checkbox = QCheckBox()
        self.checkbox.setFixedSize(30,30)
        if style is not None:
            self.checkbox.setStyleSheet(style)

        self.checkbox.stateChanged.connect(self.statechanged)
        self.main_layout.addWidget(self.checkbox,0,0)
        if title is not None:
            self.title = QLabel(title)
            self.title.setFixedHeight(30)
            self.main_layout.addWidget(self.title,0,1)
        if content is not None:
            self.content = QLabel(content)
            self.main_layout.addWidget(self.content,1,1)
    def statechanged(self, state):
        if state == 2:
            if self.callback_statechanged is not None:
                self.callback_statechanged(key = self.key)

class IconButton(QPushButton):
    def __init__(self, text='', icon: QIcon = None, button_size: int = 28, icon_size: int = 24, tool_tip = None,style = None):
        super().__init__(icon, text)
        #self.button_size = button_size
        self.icon_size = icon_size
        self.setIconSize(QSize(icon_size, icon_size))
        self.setMaximumSize(button_size, button_size)
        if tool_tip is not None:
            self.setToolTip(tool_tip)
        if style is not None:
            self.setStyleSheet(style)
        if icon is not None:
            self.setIcon(icon)

class LabelWidget(QWidget):
    def __init__(self, icon_clicked = None):
        super().__init__()
        self.main_layout = QHBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.setLayout(self.main_layout)

        self.label = QLabel()
        # self.label.setStyleSheet(f"color:#FFFFFF; ")
        self.label.setStyleSheet(f"""
            QLabel {{
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                font-size: {Style.Size.caption}px;
            }}
        """)
        style = f'''
            QPushButton {{
                background-color: {Style.PrimaryColor.primary};
                border-radius: 2px;
            }}
            QPushButton:hover {{
                background-color: {Style.PrimaryColor.primary_hover};
                border-radius: 2px;
                border: 1px solid {Style.PrimaryColor.primary_hover};
            }}
            QPushButton:pressed {{
                background-color: {Style.PrimaryColor.primary_hover};
                border-radius: 2px;
                border: 1px solid {Style.PrimaryColor.primary_hover};
            }}
        '''
        self.btn_cancel = CustomIcon(icon= Style.PrimaryImage.close_tab, icon_clicked= icon_clicked,style=style,button_size = 15)
        self.main_layout.addWidget(self.label)
        self.main_layout.addWidget(self.btn_cancel)

    def set_dynamic_stylesheet(self):
        self.label.setStyleSheet(f"""
            QLabel {{
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                font-size: {Style.Size.caption}px;
            }}
        """)
class InputWithDataCallback(QWidget):
    multidropdown_dialog_result_signal = Signal(list)
    signal_change_data = Signal(tuple)

    def __init__(self, key=None, title=None, is_read_only=False, text_placeholder=None, model=None, list_tree_group: List[GroupTreeModel] = [],
                 list_group: List[GroupModel] = [], list_camera: List[CameraModel] = [], enable_groupbox=True,
                 tree_view_type=TreeViewType.GROUP, group_type=CameraGroupType.NONE,
                 enable_only_item_checkbox=False, enable_checkbox=True, text='', enable_multi_item_checkbox=True, double_validator=False, show_arrow=False):

        super().__init__()
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()
        self.key = key
        self.model = model
        self.enable_multi_item_checkbox = enable_multi_item_checkbox
        self.enable_checkbox = enable_checkbox
        self.enable_only_item_checkbox = enable_only_item_checkbox
        self.new_list_subgroup_after_select_parentgroup = None
        self.new_list_parentgroup_after_select_subgroup = None
        self.list_group = list_group
        self.list_camera = list_camera
        self.list_tree_group = list_tree_group
        self.tree_view_type = tree_view_type
        self.group_type = group_type
        self.text = text
        self.enable_groupbox = enable_groupbox
        self.double_validator = double_validator
        self.title = title
        self.is_read_only = is_read_only
        self.show_arrow = show_arrow
        self.text_placeholder = text_placeholder
        self.callback_on_text_changed = None
        self.load_ui()
        self.setup_style_sheet()
        self.load_data()

    def load_ui(self):
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(2)
        self.label_title = QLabel(self.title)
        self.label_title.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'dialog_text')}; "
                                       f"background-color: transparent;"
                                       f"font-weight: 500;")
        self.line_edit = QLineEdit()
        self.line_edit.textChanged.connect(self.on_text_changed)
        self.line_edit.setReadOnly(self.is_read_only)

        if self.double_validator:
            double_validator = QDoubleValidator()
            double_validator.setNotation(QDoubleValidator.Notation.StandardNotation)
            self.line_edit.setValidator(double_validator)
        if self.text_placeholder is not None:
            self.line_edit.setPlaceholderText(self.text_placeholder)
        if self.show_arrow:
            self.arrow_action = QAction(QIcon(main_controller.get_theme_attribute('Image', 'treeview_collapse_item')), self.tr("Show menu"), self)
            self.arrow_action.triggered.connect(self.icon_arrow_click)
            self.line_edit.addAction(self.arrow_action, QLineEdit.ActionPosition.TrailingPosition)
            self.line_edit.mousePressEvent = self.click_line_edit
        self.main_layout.addWidget(self.label_title)
        self.main_layout.addWidget(self.line_edit)
        self.setLayout(self.main_layout)

    def load_data(self):
        # if self.is_read_only:
        if self.text != '':
            self.line_edit.setText(self.text)
        self.list_id = []
        if self.model is not None:
            if self.tree_view_type == TreeViewType.GROUP:
                if self.model.data.cameraGroupIds is not None and len(self.model.data.cameraGroupIds) > 0:
                    for group_id in self.model.data.cameraGroupIds:
                        self.list_id.append(group_id)
            else:
                if self.model.data.cameraIds is not None and len(self.model.data.cameraIds) > 0:
                    for group_id in self.model.data.cameraIds:
                        self.list_id.append(group_id)

    def setup_style_sheet(self):
        self.setStyleSheet(f'''
            /*-----QWidget-----*/
            QWidget{{
                background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};}}
            /*-----QLabel-----*/
            QLabel{{
                background-color: transparent;
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                }}
            /*-----QLineEdit-----*/
            QLineEdit{{
                background-color: transparent;
                border-radius: 4px;
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                letter-spacing: 0em;
                text-align: left;
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                padding: 4px 16px;
                }}
            QLineEdit::focus{{
                border: 1px solid {main_controller.get_theme_attribute("Color", "add_server_lineedit_focused")};
                color: {main_controller.get_theme_attribute("Color", "dialog_text")} }}
            QLineEdit:!focus{{
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                }}
            QLineEdit::placeholder{{
                color: {main_controller.get_theme_attribute("Color", "main_border")};
                }}

        ''')

    def click_line_edit(self, event):
        if event.button() == Qt.LeftButton:
            # Lấy vị trí của con trỏ chuột và hiển thị MultiDropdown tại đó
            pos = self.mapToGlobal(event.pos())
            pos.setX(pos.x() + 5)
            self.multidropdown_dialog = MultiDropDownDialog(model=self.model, tree_view_type=self.tree_view_type, list_id_checked=self.list_id)
            self.multidropdown_dialog.item_selected_signal.connect(self.name_item_selected)
            if (self.desktop_screen_size.height() - pos.y()) < self.multidropdown_dialog.height():
                pos.setY(self.desktop_screen_size.height() - self.multidropdown_dialog.height())
            self.multidropdown_dialog.move(pos)
            self.multidropdown_dialog.exec()

    def icon_arrow_click(self):
        pos = self.line_edit.mapToGlobal(self.line_edit.rect().bottomRight())
        pos.setX(pos.x() - 24)
        self.multidropdown_dialog = MultiDropDownDialog(model=self.model, tree_view_type=self.tree_view_type, list_id_checked=self.list_id)
        self.multidropdown_dialog.item_selected_signal.connect(self.name_item_selected)
        if (self.desktop_screen_size.height() - pos.y()) < self.multidropdown_dialog.height():
            pos.setY(self.desktop_screen_size.height() - self.multidropdown_dialog.height())
        self.multidropdown_dialog.move(pos)
        self.multidropdown_dialog.exec()

    def name_item_selected(self, data):
        list_string = ''
        self.list_id = []
        list_name = []
        dict_temp = {}
        for id, name in data['data'].items():
            list_name.append(name)
            self.list_id.append(id)
            dict_temp[id] = name
            if list_string == '':
                list_string = name
            else:
                list_string = f'{list_string}, {name}'
        data_emit = (list_name, self.list_id)
        self.signal_change_data.emit(dict_temp)
        self.line_edit.setText(list_string)

    def set_text_data(self, list_text):
        list_string = ''
        for name in list_text:
            if list_string == '':
                list_string = name
            else:
                list_string = f'{list_string}, {name}'
        self.line_edit.setText(list_string)

    def on_text_changed(self, text):
        if self.callback_on_text_changed is not None:
            self.callback_on_text_changed(text=text, key=self.key)

    def set_title(self, title):
        self.label_title.setText(title)

class InputWithTitle(QWidget):
    signal_show_menu = Signal()

    def __init__(self, key=None, title=None, is_password_line=False, text_placeholder=None, is_read_only=False, is_use_arrow_menu=False, is_require_field=False):
        super().__init__()
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()
        self.key = key
        self.is_require_field = is_require_field
        self.title = title
        self.is_read_only = is_read_only
        self.is_password_line = is_password_line
        self.text_placeholder = text_placeholder
        self.is_use_arrow_menu = is_use_arrow_menu
        self.load_ui()
        self.set_dynamic_stylesheet()

    def load_ui(self):
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(6)
        self.label_title = QLabel()
        if self.is_require_field:
            title_text = self.title
            html_title = f'<span>{title_text} </span><span style="color: red;">*</span>'
        else:
            html_title = self.title
        self.label_title.setText(html_title)
        self.label_title.setAlignment(Qt.AlignmentFlag.AlignLeft)

        self.line_edit = QLineEdit()
        self.line_edit.setReadOnly(self.is_read_only)
        if self.text_placeholder is not None:
            self.line_edit.setPlaceholderText(self.text_placeholder)
        if self.is_password_line:
            self.line_edit.setEchoMode(QLineEdit.EchoMode.Password)
            self.showPassAction = QAction(QIcon(Style.PrimaryImage.eye_close), self.tr("Show password"), self)
            self.showPassAction.triggered.connect(self.togglePasswordVisibility)
            self.line_edit.addAction(self.showPassAction, QLineEdit.ActionPosition.TrailingPosition)
        if self.is_use_arrow_menu:
            self.show_menu_action = QAction(QIcon(Style.PrimaryImage.down_arrow_linedit), self.tr("Show menu"), self)
            self.show_menu_action.triggered.connect(self.signal_show_menu.emit)
            self.line_edit.addAction(self.show_menu_action, QLineEdit.ActionPosition.TrailingPosition)

        self.main_layout.addWidget(self.label_title)
        self.main_layout.addWidget(self.line_edit)
        self.setLayout(self.main_layout)

    def togglePasswordVisibility(self):
        if self.line_edit.echoMode() == QLineEdit.EchoMode.Password:
            self.line_edit.setEchoMode(QLineEdit.EchoMode.Normal)
            self.showPassAction.setIcon(QIcon(Style.PrimaryImage.eye))
            self.showPassAction.setToolTip(self.tr("Hide password"))
        else:
            self.line_edit.setEchoMode(QLineEdit.EchoMode.Password)
            self.showPassAction.setIcon(QIcon(Style.PrimaryImage.eye_close))
            self.showPassAction.setToolTip(self.tr("Show password"))

    def set_dynamic_stylesheet(self):
        self.setStyleSheet(f'''
            /*-----QWidget-----*/
            QWidget{{
                background-color: {Style.PrimaryColor.on_background};}}
            /*-----QLabel-----*/
            QLabel{{
                background-color: transparent;
                
                color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")};}}
            /*-----QLineEdit-----*/
            QLineEdit{{
                background-color: transparent;
                border-radius: 4px;
                color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")};
                letter-spacing: 0em;
                text-align: left;

                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                padding: 4px 16px;}}
            QLineEdit::focus{{
                border: 1px solid {main_controller.get_theme_attribute("Color", "add_server_lineedit_focused")};
                color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")} }}
            QLineEdit:!focus{{
                color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")};}}
            QLineEdit::placeholder{{
                color: {main_controller.get_theme_attribute("Color", "main_border")};}}

        ''')

    def text(self):
        return self.line_edit.text()

    def setText(self, text):
        self.line_edit.setText(text)

    def set_title(self, title):
        self.label_title.setText(title)


class InputCallbackWithMessage(QWidget):
    multidropdown_dialog_result_signal = Signal(list)
    signal_change_data = Signal(tuple)

    def __init__(self, key=None, title=None, is_read_only=False, text_placeholder=None, model=None, list_tree_group: List[GroupTreeModel] = [],
                 list_group: List[GroupModel] = [], list_camera: List[CameraModel] = [], enable_groupbox=True,
                 tree_view_type=TreeViewType.GROUP, group_type=CameraGroupType.NONE,
                 enable_only_item_checkbox=False, enable_checkbox=True, text='', enable_multi_item_checkbox=True, double_validator=False, show_arrow=False,
                 suggestion_message=None, just_view_infor=False):

        super().__init__()
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()
        self.key = key
        self.model = model
        self.enable_multi_item_checkbox = enable_multi_item_checkbox
        self.enable_checkbox = enable_checkbox
        self.enable_only_item_checkbox = enable_only_item_checkbox
        self.new_list_subgroup_after_select_parentgroup = None
        self.new_list_parentgroup_after_select_subgroup = None
        self.list_group = list_group
        self.list_camera = list_camera
        self.list_tree_group = list_tree_group
        self.tree_view_type = tree_view_type
        self.group_type = group_type
        self.text = text
        self.enable_groupbox = enable_groupbox
        self.double_validator = double_validator
        self.title = title
        self.is_read_only = is_read_only
        self.show_arrow = show_arrow
        self.text_placeholder = text_placeholder
        self.callback_on_text_changed = None
        self.suggestion_message = suggestion_message
        self.just_view_infor = just_view_infor
        self.load_ui()
        self.setup_style_sheet()
        self.load_data()

    def load_ui(self):
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(6)
        self.label_title = QLabel(self.title)
        self.label_title.setStyleSheet(f"""color: {main_controller.get_theme_attribute("Color", "dialog_text")}; 
                                       background-color: transparent;
                                       font-weight: 500;""")
        self.label_title.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.line_edit = QLineEdit()
        self.line_edit.textChanged.connect(self.on_text_changed)
        self.line_edit.setReadOnly(self.is_read_only)

        if self.double_validator:
            double_validator = QDoubleValidator()
            double_validator.setNotation(QDoubleValidator.Notation.StandardNotation)
            self.line_edit.setValidator(double_validator)
        if self.text_placeholder is not None:
            self.line_edit.setPlaceholderText(self.text_placeholder)
        if self.show_arrow:
            self.arrow_action = QAction(QIcon(main_controller.get_theme_attribute("Image", "treeview_collapse_item")), self.tr("Show menu"), self)
            self.arrow_action.triggered.connect(self.icon_arrow_click)
            self.line_edit.addAction(self.arrow_action, QLineEdit.ActionPosition.TrailingPosition)
            self.line_edit.mousePressEvent = self.click_line_edit
        # Suggestion message
        self.label_suggestion = QLabel()
        self.label_suggestion.setWordWrap(True)
        self.label_suggestion.setStyleSheet(f'''
                color: {Style.PrimaryColor.text_unselected};
                font-size: 11px;
            ''')
        if self.suggestion_message is not None:
            self.label_suggestion.setText(self.suggestion_message)
        else:
            self.label_suggestion.setText(' \n ')
        self.main_layout.addWidget(self.label_title)
        self.main_layout.addWidget(self.line_edit)
        self.main_layout.addWidget(self.label_suggestion)
        self.setLayout(self.main_layout)

    def load_data(self):
        # if self.is_read_only:
        if self.text != '':
            self.line_edit.setText(self.text)
        self.list_id = []
        self.list_role_ids = []
        if self.model is not None:
            if self.tree_view_type == TreeViewType.GROUP:
                if self.model.data.cameraGroupIds is not None and len(self.model.data.cameraGroupIds) > 0:
                    for group_id in self.model.data.cameraGroupIds:
                        self.list_id.append(group_id)
            elif self.tree_view_type == TreeViewType.CAMERA:
                if self.model.data.cameraIds is not None and len(self.model.data.cameraIds) > 0:
                    for camera_id in self.model.data.cameraIds:
                        self.list_id.append(camera_id)
            elif self.tree_view_type == TreeViewType.USER_ROLE:
                if self.model.data.userRoles is not None and len(self.model.data.userRoles) > 0:
                    for item_role in self.model.data.userRoles:
                        if isinstance(item_role, dict):
                            item = {'roleId': item_role['roleId']}
                            self.list_id.append(item_role['roleId'])
                        else:
                            item = {'roleId': item_role.roleId}
                            self.list_id.append(item_role.roleId)
                        if item not in self.list_role_ids:
                            self.list_role_ids.append(item)

    def setup_style_sheet(self):
        self.setStyleSheet(f'''
            /*-----QWidget-----*/
            QWidget{{
                background-color: {Style.PrimaryColor.on_background};}}
            /*-----QLabel-----*/
            QLabel{{
                background-color: transparent;
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                }}
            /*-----QLineEdit-----*/
            QLineEdit{{
                background-color: transparent;
                border-radius: 4px;
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                letter-spacing: 0em;
                text-align: left;
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                padding: 4px 16px;
                }}
            QLineEdit::focus{{
                border: 1px solid {Style.PrimaryColor.border_line_edit};
                color: {main_controller.get_theme_attribute("Color", "dialog_text")} }}
            QLineEdit:!focus{{
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                }}
            QLineEdit::placeholder{{
                color: {main_controller.get_theme_attribute("Color", "main_border")};
                }}

        ''')

    def click_line_edit(self, event):
        if self.just_view_infor:
            return
        if event.button() == Qt.LeftButton:
            # Lấy vị trí của con trỏ chuột và hiển thị MultiDropdown tại đó
            pos = self.mapToGlobal(event.pos())
            pos.setX(pos.x() + 5)
            self.multidropdown_dialog = MultiDropDownDialog(model=self.model, tree_view_type=self.tree_view_type, list_id_checked=self.list_id)
            self.multidropdown_dialog.item_selected_signal.connect(self.name_item_selected)
            if (self.desktop_screen_size.height() - pos.y()) < self.multidropdown_dialog.height():
                pos.setY(self.desktop_screen_size.height() - self.multidropdown_dialog.height())
            self.multidropdown_dialog.move(pos)
            self.multidropdown_dialog.exec()

    def icon_arrow_click(self):
        if self.just_view_infor:
            return
        pos = self.line_edit.mapToGlobal(self.line_edit.rect().bottomRight())
        pos.setX(pos.x() - 24)
        self.multidropdown_dialog = MultiDropDownDialog(model=self.model, tree_view_type=self.tree_view_type, list_id_checked=self.list_id)
        self.multidropdown_dialog.item_selected_signal.connect(self.name_item_selected)
        if (self.desktop_screen_size.height() - pos.y()) < self.multidropdown_dialog.height():
            pos.setY(self.desktop_screen_size.height() - self.multidropdown_dialog.height())
        self.multidropdown_dialog.move(pos)
        self.multidropdown_dialog.exec()

    def name_item_selected(self, data):
        list_string = ''
        self.list_id = []
        self.list_role_ids = []
        list_name = []
        for item_id, name in data['data'].items():
            list_name.append(name)
            self.list_id.append(item_id)
            if self.tree_view_type == TreeViewType.USER_ROLE:
                item = {'roleId': item_id}
                if item not in self.list_role_ids:
                    self.list_role_ids.append(item)
            if list_string == '':
                list_string = name
            else:
                list_string = f'{list_string}, {name}'
        data_emit = (list_name, self.list_id)
        self.signal_change_data.emit(data_emit)
        self.line_edit.setText(list_string)

    def set_text_data(self, list_text):
        list_string = ''
        for name in list_text:
            if list_string == '':
                list_string = name
            else:
                list_string = f'{list_string}, {name}'
        self.line_edit.setText(list_string)

    def on_text_changed(self, text):
        if self.callback_on_text_changed is not None:
            self.callback_on_text_changed(text=text, key=self.key)

    def set_title(self, title):
        self.label_title.setText(title)

class IPLineEdit(QLineEdit):
    def __init__(self, placeholder=None, parent=None):
        super().__init__(parent)
        if placeholder is not None:
            self.setPlaceholderText(placeholder)
        ip_regex = QRegularExpression(r'^(\d{1,3}\.){0,3}\d{0,3}$')
        self.setValidator(QRegularExpressionValidator(ip_regex, self))

    def keyPressEvent(self, event):
        if event.key() in [Qt.Key.Key_0, Qt.Key.Key_1, Qt.Key.Key_2, Qt.Key.Key_3,
                           Qt.Key.Key_4, Qt.Key.Key_5, Qt.Key.Key_6, Qt.Key.Key_7,
                           Qt.Key.Key_8, Qt.Key.Key_9]:
            current_text = self.text()
            segments = current_text.split('.')
            last_segment = segments[-1]

            if len(last_segment) == 2 and len(segments) < 4:
                super().keyPressEvent(event)
                self.setText(self.text() + '.')
            else:
                super().keyPressEvent(event)
        else:
            super().keyPressEvent(event)

class InputWithTitleAndIPValidator(QWidget):
    signal_show_menu = Signal()

    def __init__(self, key=None, title=None, text_placeholder=None):
        super().__init__()
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()
        self.key = key
        self.title = title
        self.text_placeholder = text_placeholder
        self.load_ui()
        self.setup_style_sheet()

    def load_ui(self):
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(6)
        self.label_title = QLabel(self.title)
        self.label_title.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'dialog_text')}; "
                                       f""
                                       f"background-color: transparent;"
                                       f"font-weight: 500;")
        self.line_edit = IPLineEdit(self.text_placeholder)
        self.main_layout.addWidget(self.label_title)
        self.main_layout.addWidget(self.line_edit)
        self.setLayout(self.main_layout)

    def setup_style_sheet(self):
        self.setStyleSheet(f'''
            /*-----QWidget-----*/
            QWidget{{
                background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};}}
            /*-----QLabel-----*/
            QLabel{{
                background-color: transparent;

                color: {main_controller.get_theme_attribute("Color", "dialog_text")};}}
            /*-----QLineEdit-----*/
            QLineEdit{{
                background-color: transparent;
                border-radius: 4px;
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                letter-spacing: 0em;
                text-align: left;

                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                padding: 4px 16px;}}
            QLineEdit::focus{{
                border: 1px solid {main_controller.get_theme_attribute("Color", "add_server_lineedit_focused")};
                color: {main_controller.get_theme_attribute("Color", "dialog_text")} }}
            QLineEdit:!focus{{
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};}}
            QLineEdit::placeholder{{
                color: {main_controller.get_theme_attribute("Color", "main_border")};}}

        ''')

    def text(self):
        return self.line_edit.text()

class InputWithNewStyle(QLineEdit):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet(f'''
            QLineEdit{{
                background-color: transparent;
                border-radius: 4px;
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                letter-spacing: 0em;
                text-align: left;
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                padding: 4px 16px;}}
            QLineEdit::focus{{
                border: 1px solid {main_controller.get_theme_attribute("Color", "add_server_lineedit_focused")};
                color: {main_controller.get_theme_attribute("Color", "dialog_text")}; }}
            QLineEdit:!focus{{
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};}}
            QLineEdit::placeholder{{
                color: {main_controller.get_theme_attribute("Color", "main_border")};}}
        ''')

class InputWithRequireField(QWidget):
    def __init__(self, parent=None, limit_length: int = 50, title=None, is_required_fields=False, text_placeholder=None, key=None,
                 just_view_infor=False, is_password_line=False, suggestion_message=None, focus_out_callback=None):
        super().__init__(parent)
        self.limit_length = limit_length
        self.title = title
        self.is_required_fields = is_required_fields
        self.text_placeholder = text_placeholder
        self.is_password_line = is_password_line
        self.suggestion_message = suggestion_message
        self.just_view_infor = just_view_infor
        self.focus_out_callback = focus_out_callback
        self.load_ui()

    def load_ui(self):
        # create layout
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(6)

        self.label_title = QLabel()
        if self.is_required_fields and not self.just_view_infor:
            title_text = self.title
            html_title = f'<span>{title_text} </span><span style="color: red;">*</span>'
        else:
            html_title = self.title
        self.label_title.setText(html_title)
        self.label_title.setStyleSheet(f"color:{main_controller.get_theme_attribute('Color', 'dialog_text')};")
        self.label_title.setAlignment(Qt.AlignmentFlag.AlignLeft)

        self.layout_content = QHBoxLayout()

        layout_content = QHBoxLayout()
        layout_content.setSpacing(4)
        layout_content.setContentsMargins(8, 5, 8, 5)

        self.line_edit = QLineEdit()
        if self.focus_out_callback is not None:
            self.line_edit.editingFinished.connect(self.focus_out_callback)
        self.line_edit.setMaxLength(self.limit_length)
        self.line_edit.setFocusPolicy(Qt.FocusPolicy.ClickFocus)
        self.line_edit.textChanged.connect(self.line_edit_text_change)
        self.line_edit.setReadOnly(self.just_view_infor)
        if self.text_placeholder is not None:
            self.line_edit.setPlaceholderText(self.text_placeholder)
        if self.is_password_line:
            self.line_edit.setEchoMode(QLineEdit.EchoMode.Password)
            self.showPassAction = QAction(QIcon(Style.PrimaryImage.eye_close), self.tr("Show password"), self)
            self.showPassAction.triggered.connect(self.togglePasswordVisibility)
            self.line_edit.addAction(self.showPassAction, QLineEdit.ActionPosition.TrailingPosition)

        widget_number = QWidget()
        layout_numer = QHBoxLayout()
        layout_numer.setSpacing(2)
        layout_numer.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout_numer.setContentsMargins(0, 0, 0, 0)
        svg_widget = QSvgWidget()
        svg_widget.enterEvent = self.enter_event_svg_widget
        svg_widget.mousePressEvent = self.clear_click
        svg_widget.load(Style.PrimaryImage.clear_input)
        svg_widget.setFixedSize(12, 12)
        self.label_limit = QLabel(f'/{str(self.limit_length)}')
        self.label_limit.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignCenter)

        self.label_amount = QLabel('0')
        self.label_amount.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignCenter)
        self.label_amount.setFixedWidth(18)

        layout_amount = QHBoxLayout()
        layout_amount.setContentsMargins(0, 0, 0, 0)
        layout_amount.setSpacing(0)
        layout_amount.addWidget(self.label_amount)
        layout_amount.addWidget(self.label_limit)
        widget_amount = QWidget()
        widget_amount.enterEvent = self.enter_event_widget_numer
        widget_amount.setLayout(layout_amount)
        if not self.just_view_infor:
            layout_numer.addWidget(svg_widget)
        layout_numer.addWidget(widget_amount)
        widget_number.setLayout(layout_numer)

        layout_content.addWidget(self.line_edit, 90)
        layout_content.addWidget(widget_number, 10)

        widget_content = QWidget()
        widget_content.setObjectName('main_widget')
        widget_content.setLayout(layout_content)

        # Suggestion message
        self.label_suggestion = QLabel()
        self.label_suggestion.setWordWrap(True)
        self.label_suggestion.setStyleSheet(
                f"color: {Style.PrimaryColor.error}; font-weight: 400; font-style: italic; font-size: 11px")
        if self.suggestion_message is not None:
            self.label_suggestion.setText(self.suggestion_message)
        else:
            self.label_suggestion.setText(' \n ')

        self.main_layout.addWidget(self.label_title)
        self.main_layout.addWidget(widget_content)
        self.main_layout.addWidget(self.label_suggestion)
        self.setLayout(self.main_layout)
        self.setup_stylesheet()

    def line_edit_text_change(self, text):
        self.label_amount.setText(f'{len(text)}')
        if self.is_password_line:
            # self.validate_password(text)
            pass

    def enter_event_svg_widget(self, event):
        self.setCursor(Qt.CursorShape.PointingHandCursor)

    def enter_event_widget_numer(self, event):
        self.setCursor(Qt.CursorShape.IBeamCursor)

    def mousePressEvent(self, event):
        self.line_edit.setFocus()
        super().mousePressEvent(event)

    def clear_click(self, event):
        self.line_edit.clear()
        self.label_amount.setText(f'0')

    def togglePasswordVisibility(self):
        if self.line_edit.echoMode() == QLineEdit.EchoMode.Password:
            self.line_edit.setEchoMode(QLineEdit.EchoMode.Normal)
            self.showPassAction.setIcon(QIcon(Style.PrimaryImage.eye))
            self.showPassAction.setToolTip(self.tr("Hide password"))
        else:
            self.line_edit.setEchoMode(QLineEdit.EchoMode.Password)
            self.showPassAction.setIcon(QIcon(Style.PrimaryImage.eye_close))
            self.showPassAction.setToolTip(self.tr("Show password"))

    def text(self):
        return self.line_edit.text()

    def setText(self, text):
        self.line_edit.setText(text)

    def set_title(self, title):
        self.label_title.setText(title)

    def setup_stylesheet(self):
        self.setStyleSheet(
            f'''
                QWidget#main_widget {{
                    background-color: transparent;
                    color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                    border-radius: 4px;
                    border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                }}

                QLineEdit:disabled {{
                    color: gray;
                    background-color: lightgray;
                }}

                QLineEdit {{
                    color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                    background-color: transparent;
                    border: none;
                    border-radius: 4px;
                }}

                QLineEdit:focus {{
                    color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                    background-color: transparent;
                    border: none;
                    border-radius: 4px;
                }}
                QLabel {{
                    color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                }}
        ''')

class ComboBoxWithRequireField(QWidget):
    def __init__(self, key=None, title=None, text_placeholder=None, is_required_fields=False, list_data=None, suggestion_message=None,
                 just_view_infor=False):
        super().__init__()
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()
        self.key = key
        self.is_required_fields = is_required_fields
        self.title = title
        self.text_placeholder = text_placeholder
        self.list_data = list_data
        self.suggestion_message = suggestion_message
        self.just_view_infor = just_view_infor
        self.load_ui()
        # self.setup_style_sheet()

    def load_ui(self):
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(6)
        self.label_title = QLabel()
        if self.is_required_fields and not self.just_view_infor:
            title_text = self.title
            html_title = f'<span style="color: {main_controller.get_theme_attribute("Color", "dialog_text")};">{title_text} </span><span style="color: red;">*</span>'
        else:
            html_title = self.title
        self.label_title.setText(html_title)
        self.label_title.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.label_title.setStyleSheet(f"""color: {main_controller.get_theme_attribute("Color", "dialog_text")}; 
                                       background-color: transparent;
                                       font-weight: 500;""")

        self.combo_box = ComboBoxNewStyle()
        self.combo_box.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        if self.list_data is not None:
            for key, item_data in self.list_data.items():
                self.combo_box.addItem(key, item_data)
        self.combo_box.clearEditText()
        self.combo_box.setPlaceholderText(self.tr('Choose one'))
        self.combo_box.setDisabled(self.just_view_infor)
        # Suggestion message
        self.label_suggestion = QLabel()
        self.label_suggestion.setWordWrap(True)
        self.label_suggestion.setStyleSheet(f'''
            color: {Style.PrimaryColor.text_unselected};
            font-size: 11px;
        ''')
        if self.suggestion_message is not None:
            self.label_suggestion.setText(self.suggestion_message)
        else:
            self.label_suggestion.setText(' \n \n')

        self.main_layout.addWidget(self.label_title)
        self.main_layout.addWidget(self.combo_box)
        self.main_layout.addWidget(self.label_suggestion)
        self.setLayout(self.main_layout)

    def setValue(self, value):
        # Iterate through all items in the combo box
        for i in range(self.combo_box.count()):
            # Get the itemData for each index
            item_data = self.combo_box.itemData(i)

            # Check if itemData matches the value
            if item_data == value:
                # Set the current index to the matched item's index
                self.combo_box.setCurrentIndex(i)
                break
        # if value == 0:
        #     self.combo_box.setCurrentIndex(1)
        # elif value == 1:
        #     self.combo_box.setCurrentIndex(0)
        # else:
        #     # Get the number of items in the combo box
        #     item_count = self.combo_box.count()
        #     # Iterate through all the items in the combo box
        #     for i in range(item_count):
        #         # Get the item text at index i
        #         item_text = self.combo_box.itemText(i)
        #         # Check if the item text matches the value
        #         if item_text == value:
        #             # Set the current index of the combo box to the matched item
        #             self.combo_box.setCurrentIndex(i)
        #             break

class SpinBoxWithTitle(QWidget):
    def __init__(self, key=None, title=None, default_value=80, max_min_range=(0, 65535)):
        super().__init__()
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()
        self.key = key
        self.default_value = default_value
        self.max_min_range = max_min_range
        self.title = title
        self.load_ui()
        self.setup_style_sheet()

    def load_ui(self):
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(6)
        self.label_title = QLabel(self.title)
        self.spin_box = QSpinBox()
        self.spin_box.setRange(self.max_min_range[0], self.max_min_range[1])
        self.spin_box.setValue(self.default_value)
        self.main_layout.addWidget(self.label_title)
        self.main_layout.addWidget(self.spin_box)
        self.setLayout(self.main_layout)

    def setup_style_sheet(self):
        self.setStyleSheet(f'''
            /*-----QWidget-----*/
            QWidget{{
                background-color: {Style.PrimaryColor.on_background};
            }}
            /*-----QLabel-----*/
            QLabel{{
                background-color: transparent;
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                
            }} 
            /*-----QSpinBox-----*/
            QSpinBox {{
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                border-radius: 4px;
                padding: 4px 8px 4px 8px;
                background-color: transparent;
                color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")} 
            }}
            
            QSpinBox::disabled {{
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                border-radius: 4px;
                padding: 4px 8px 4px 8px;
                background-color: transparent;
                color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")} 
            }}
            
            QSpinBox::focus{{
                border: 1px solid {Style.PrimaryColor.border_line_edit};
                color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")} 
            }}
            QSpinBox::up-button {{
                background-color: transparent;
                border: none;
                border-top-right-radius: 4px;
                width: 16px;
            }}
            QSpinBox::up-arrow {{
                image: url({Style.PrimaryImage.up_arrow_spinbox});
            }}
            QSpinBox::down-button {{
                background-color: transparent;
                border: none;
                border-bottom-right-radius: 4px;
                width: 16px;
            }}
            QSpinBox::down-arrow {{
                image: url({Style.PrimaryImage.down_arrow_spinbox});
            }}
            QSpinBox::up-button:pressed, QSpinBox::down-button:pressed {{
                background-color: {Style.PrimaryColor.on_hover_secondary};
            }}
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
                background-color: {Style.PrimaryColor.on_hover_secondary};
            }}

        ''')

    def text(self):
        return self.spin_box.text()

class ComboBoxNewStyle(QComboBox):
    def __init__(self, parent=None, use_line_arrow=False):
        super().__init__(parent)
        self.set_style_sheet_combobox()

    def set_style_sheet_combobox(self):
        self.view().parentWidget().setStyleSheet(
            f'background-color: {main_controller.get_theme_attribute("Color", "main_background")};')
        self.setStyleSheet(f'''
                    QComboBox{{
                        border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                        color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                        border-radius: 4px;
                        background: transparent;
                        padding: 4px 16px;
                    }}
                    QComboBox::disabled{{
                        border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                        border-radius: 4px;
                        background: transparent;
                    }}

                    QComboBox QAbstractItemView {{
                         border: none;
                         color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                         selection-background-color: {Style.PrimaryColor.primary};
                         background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                         border-radius: 4px;
                         padding: 4px;
                    }}

                    QComboBox QAbstractItemView::indicator:checked {{
                        border: none;
                        image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                        background-color: transparent;
                        padding: 4px;
                    }}

                    QComboBox QAbstractItemView::indicator:unchecked {{
                        border: none;
                        image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                        background-color: transparent;
                        padding: 4px;
                    }}

                    QComboBox::item:selected {{
                        background-color: {Style.PrimaryColor.primary};
                        color: #FFFFFF;
                        border-radius: 2px;
                        border: 1px solid #2E2E2E;
                    }}
                    QComboBox::down-arrow {{
                        image: url({Style.PrimaryImage.down_arrow_linedit});
                    }}
                    QComboBox::drop-down {{
                      border-left: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                      }}

                ''')


class SpinBoxNewStyle(QSpinBox):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet(f'''
            QSpinBox {{
                border: 1px solid {main_controller.get_theme_attribute("Color", "common_border")};
                border-radius: 4px;
                padding: 4px 8px 4px 8px;
                background-color: transparent;
                color: {main_controller.get_theme_attribute("Color", "dialog_text")} ;
            }}
            QSpinBox::focus{{
                border: 1px solid {Style.PrimaryColor.border_line_edit};
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
            }}
            QSpinBox::up-button {{
                background-color: transparent;
                border: none;
                border-top-right-radius: 4px;
                width: 16px;
            }}
            QSpinBox::up-arrow {{
                image: url({Style.PrimaryImage.up_arrow_spinbox});
            }}
            QSpinBox::down-button {{
                background-color: transparent;
                border: none;
                border-bottom-right-radius: 4px;
                width: 16px;
            }}
            QSpinBox::down-arrow {{
                image: url({Style.PrimaryImage.down_arrow_spinbox});
            }}
            QSpinBox::up-button:pressed, QSpinBox::down-button:pressed {{
                background-color: {Style.PrimaryColor.on_hover_secondary};
            }}
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
                background-color: {Style.PrimaryColor.on_hover_secondary};
            }}
        ''')

class PickImageArea(QWidget):
    view_pixmap_signal = Signal(QPixmap)

    def __init__(self, key=None, data_available=False, just_view_image=False, controller: Controller = None):
        super().__init__()
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()
        self.pixmap = None
        self.image_file = None
        self.label_avatar = QLabel()
        self.key = key
        self.controller = controller
        self.just_view_image = just_view_image
        self.data_available = data_available
        self.pick_file_model = None
        self.load_ui()
        self.setup_style_sheet()

    def load_ui(self):
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.main_layout.setContentsMargins(0, 0, 0, 0)

        self.stacked_avatar_area = QStackedWidget()
        self.stacked_avatar_area.setObjectName('stacked_avatar')
        widget_icon_avt = QWidget()
        layout_icon_avt = QVBoxLayout(widget_icon_avt)
        layout_icon_avt.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_add_avatar = QSvgWidget()
        icon_add_avatar.load(Style.PrimaryImage.add_avatar)
        icon_add_avatar.setFixedSize(20, 20)
        layout_icon_avt.addWidget(icon_add_avatar)
        self.stacked_avatar_area.addWidget(widget_icon_avt)

        if self.data_available:
            pixmap = QPixmap(Style.PrimaryImage.loading_image)
            # Resize the pixmap to fit into the avatar area (if needed)
            self.label_avatar.setPixmap(
                pixmap.scaled(24, 24, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))
            self.label_avatar.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.stacked_avatar_area.addWidget(self.label_avatar)
            self.stacked_avatar_area.setCurrentIndex(1)

        self.main_widget = QWidget()
        self.main_widget.setFixedSize(100, 100)
        self.main_widget.mousePressEvent = self.click_add_avatar
        self.main_widget.setObjectName('widget_main')
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        layout.setSpacing(0)
        layout.addWidget(self.stacked_avatar_area)
        self.main_widget.setLayout(layout)

        self.label_suggestion_avatar = QLabel()
        self.label_suggestion_avatar.setStyleSheet(
            f"color: {Style.PrimaryColor.error}; font-weight: 400; font-style: italic; font-size: 11;")

        self.main_layout.addWidget(self.main_widget)
        self.main_layout.addWidget(self.label_suggestion_avatar)
        self.setLayout(self.main_layout)
        self.setFixedHeight(130)  # ????

        layout_view = QHBoxLayout()
        layout_view.setSpacing(4)
        icon_see = QSvgWidget()
        icon_see.setStyleSheet('background-color: transparent')
        icon_see.load(Style.PrimaryImage.eye_in_table)
        icon_see.setFixedSize(16, 16)
        icon_see.mousePressEvent = self.icon_see_clicked

        icon_delete_image = QSvgWidget()
        icon_delete_image.setStyleSheet('background-color: transparent')
        icon_delete_image.load(Style.PrimaryImage.trash)
        icon_delete_image.setFixedSize(16, 16)
        icon_delete_image.mousePressEvent = self.icon_delete_clicked

        layout_view.addWidget(icon_see)
        if not self.just_view_image:
            layout_view.addWidget(icon_delete_image)
        self.widget_view = QWidget(self)
        self.widget_view.setObjectName('widget_view')
        self.widget_view.setStyleSheet(f'''
                QWidget#widget_view{{
                    background-color: rgba(0, 0, 0, 150); 
                    border-radius: 4px; 
                    border: 1px solid {Style.PrimaryColor.border_line_edit}
                }}
            
            ''')
        self.widget_view.setLayout(layout_view)
        self.widget_view.setGeometry(0, 0, 100, 100)
        self.widget_view.setVisible(False)

    def load_image(self, image_path):
        # Create and start the downloader thread
        self.downloader_thread = ImageDownloader(image_path)
        self.downloader_thread.image_downloaded_signal.connect(self.display_image)
        self.downloader_thread.start()

    def display_image(self, pixmap: QPixmap):
        self.pixmap = pixmap
        # Resize the pixmap to fit into the avatar area (if needed)
        self.label_avatar.setPixmap(
            pixmap.scaled(96, 96, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))
        self.label_avatar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.stacked_avatar_area.addWidget(self.label_avatar)
        # Optionally switch to the avatar image widget (index 0)
        self.stacked_avatar_area.setCurrentIndex(1)

    def setup_style_sheet(self):
        self.setStyleSheet(f'''
            QWidget#widget_main{{
                border: 2px dashed {main_controller.get_theme_attribute("Color", "dialog_text")}; 
                border-radius: 4px; 
                padding: 4px;
            }}
        ''')

    def click_add_avatar(self, event):
        self.label_avatar = QLabel()
        if self.just_view_image:
            return
        file_dialog = QFileDialog()
        file_dialog.setNameFilter("Ảnh (*.png *.jpg *.jpeg)")
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFile)
        if file_dialog.exec():  # Check if the user has selected a file
            selected_file = file_dialog.selectedFiles()[0]  # Get the selected file path
            with open(selected_file, 'rb') as file:
                binary_data = file.read()
            files = {'file': binary_data, 'rootPath': selected_file}  # Open the file in binary mode
            self.controller.upload_file_to_minio(parent=self, file=files)
            pixmap = QPixmap(selected_file)

            # Resize the pixmap to fit into the avatar area (if needed)
            self.label_avatar.setPixmap(
                pixmap.scaled(96, 96, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))
            self.label_avatar.setAlignment(Qt.AlignmentFlag.AlignCenter)

            self.stacked_avatar_area.addWidget(self.label_avatar)

            # Optionally switch to the avatar image widget (index 0)
            self.stacked_avatar_area.setCurrentIndex(1)

    def enterEvent(self, event):
        if self.label_avatar is not None and not self.label_avatar.pixmap().isNull():
            self.widget_view.setVisible(True)

    def leaveEvent(self, event):
        if self.label_avatar is not None and not self.label_avatar.pixmap().isNull():
            self.widget_view.setVisible(False)

    def icon_see_clicked(self, event):
        if self.pixmap is not None:
            self.view_pixmap_signal.emit(self.pixmap)

    def icon_delete_clicked(self, event):
        if self.label_avatar is not None and isinstance(self.stacked_avatar_area.currentWidget(), QLabel):
            self.stacked_avatar_area.removeWidget(self.label_avatar)
            self.label_avatar.deleteLater()
            self.label_avatar = None
            self.widget_view.setVisible(False)

            self.stacked_avatar_area.setCurrentIndex(0)

class RecursiveTreeview(QWidget):
    def __init__(self, key=None, data_list=None, is_camera_tree=False,
                 camera_list=None, group_list=None, is_view_only=False, role_model=None, recursive_tree_type=None):
        super().__init__()
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()
        self.key = key
        self.is_view_only = is_view_only
        self.data_list = data_list
        self.is_camera_tree = is_camera_tree
        self.camera_list = camera_list
        self.group_list = group_list
        self.role_model = role_model
        self.recursive_tree_type = recursive_tree_type
        self.role_groups_ids = []
        self.role_cameras_ids = []
        self.menu_ids = []
        if self.role_model is not None:
            self.role_groups_ids = role_model_manager.get_children_id(self.role_model.data.id, ChildrenRoleType.ROLE_GROUP)
            self.role_cameras_ids = role_model_manager.get_children_id(self.role_model.data.id, ChildrenRoleType.ROLE_CAMERAS)
            self.menu_ids = role_model_manager.get_children_id(self.role_model.data.id, ChildrenRoleType.ROLE_MENUS)
        self.load_ui()

    def load_ui(self):
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.main_layout.setContentsMargins(0, 0, 0, 0)

        # Create a QTreeView
        self.tree_widget = QTreeWidget()
        self.tree_widget.setHeaderHidden(True)
        scroll_bar_ver = self.tree_widget.verticalScrollBar()
        scroll_bar_ver.setStyleSheet(Style.StyleSheet.scrollbar_ver_style)
        self.tree_widget.setFrameShape(QTreeView.Shape.NoFrame)
        self.tree_widget.setFrameShadow(QFrame.Shadow.Plain)
        self.tree_widget.setAlternatingRowColors(False)
        self.tree_widget.setDragEnabled(False)
        self.tree_widget.setDragDropMode(QTreeView.DragDropMode.DragOnly)
        self.tree_widget.setSelectionMode(QTreeView.SelectionMode.ExtendedSelection)
        self.tree_widget.setSelectionBehavior(QTreeView.SelectionBehavior.SelectRows)
        self.tree_widget.setDragDropOverwriteMode(False)
        self.tree_widget.setDropIndicatorShown(False)
        self.tree_widget.setStyleSheet(
            f"""
                    QTreeWidget {{
                        background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                        border: None;
                        color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                    }}
                    QTreeWidget::item {{
                        padding: 4px; 
                        color: {main_controller.get_theme_attribute("Color", "dialog_text")}; 
                    }}
                    QTreeWidget::item::selected {{
                        background-color: {main_controller.get_theme_attribute("Color", "treeview_item_background_selected")};
                        color: {main_controller.get_theme_attribute("Color", "dialog_text")}; 
                    }}
                    QTreeWidget::branch:has-children:closed {{
                        image: url({main_controller.get_theme_attribute("Image", "treeview_expand_item")});
                    }}
                    QTreeWidget::branch:has-children:open {{
                        image: url({main_controller.get_theme_attribute("Image", "treeview_collapse_item")});
                    }}
                    QTreeWidget::section {{ 
                        background-color: {Style.PrimaryColor.background}; 
                        color: {main_controller.get_theme_attribute("Color", "dialog_text")}; 
                    }}
                    QTreeWidget::indicator:unchecked {{
                        border: none;
                        image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                        background-color: transparent;
                        width: 16px;
                        height: 16px;
                    }}
                    QTreeWidget::indicator:checked {{
                        border: none;
                        image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                        background-color: transparent;
                        width: 16px;
                        height: 16px;
                    }}
                    QTreeWidget::indicator:indeterminate {{
                        border: none;
                        image: url({main_controller.get_theme_attribute("Image", "checkbox_partially_checked")});
                        background-color: transparent;
                        width: 16px;
                        height: 16px;
                    }}
                """)
        # Set the initial scrollbar policies
        self.tree_widget.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        # self.tree_widget.pressed.connect(self.on_tree_widget_pressed)

        # Add the root item "ALL"
        root_item = CustomQTreeWidgetItem(self.tree_widget)
        root_item.setText(0, self.tr('ALL'))
        root_item.setFlags(root_item.flags() | Qt.ItemFlag.ItemIsAutoTristate | Qt.ItemFlag.ItemIsUserCheckable)

        if self.is_camera_tree:
            self.populate_camera_model(group_list=self.group_list, camera_list=self.camera_list, parent_item=root_item)
        else:
            if self.data_list is not None:
                # Recursively populate the tree with FunctionMenu data
                list_data_compare = []
                if self.recursive_tree_type == 'tree_type_menu':
                    list_data_compare = self.menu_ids
                elif self.recursive_tree_type == 'tree_type_profile_group':
                    list_data_compare = self.role_groups_ids
                self.populate_model(self.data_list, root_item, list_data_compare)

        root_index = self.tree_widget.indexFromItem(root_item)
        self.tree_widget.setExpanded(root_index, True)

        self.main_widget = QWidget()
        self.main_widget.setObjectName('widget_main')
        layout = QVBoxLayout()
        layout.addWidget(self.tree_widget)
        self.main_widget.setLayout(layout)

        self.main_layout.addWidget(self.main_widget)
        self.setLayout(self.main_layout)

    def populate_model(self, data_list, parent_item: CustomQTreeWidgetItem, list_data_compare):
        """Recursively populates the tree model with FunctionMenu data."""
        if isinstance(data_list, dict):
            for data_id, data_item in data_list.items():
                item = CustomQTreeWidgetItem(parent=parent_item, item_model=data_item)
                item.setText(0, data_item.data.name)
                item.setData(0, Qt.UserRole, data_item.data.id)
                item.setFlags(item.flags() | Qt.ItemFlag.ItemIsAutoTristate | Qt.ItemFlag.ItemIsUserCheckable)
                item.setCheckState(0, Qt.CheckState.Unchecked)
                item.setDisabled(self.is_view_only)
                if data_item.data.id in list_data_compare:
                    item.setCheckState(0, Qt.CheckState.Checked)

                if data_item.children:
                    self.populate_model(data_item.data.children, item, list_data_compare)

        if isinstance(data_list, list):
            for data_item in data_list:
                item = CustomQTreeWidgetItem(parent=parent_item, item_model=data_item)
                item.setText(0, data_item.name)
                item.setFlags(item.flags() | Qt.ItemFlag.ItemIsAutoTristate | Qt.ItemFlag.ItemIsUserCheckable)
                item.setCheckState(0, Qt.CheckState.Unchecked)
                item.setDisabled(self.is_view_only)
                if data_item.id in list_data_compare:
                    item.setCheckState(0, Qt.CheckState.Checked)
                if data_item.children:
                    self.populate_model(data_item.children, item, list_data_compare)

    def populate_camera_model(self, group_list, camera_list, parent_item: CustomQTreeWidgetItem):
        camera_non_group_list = []
        for camera_id, camera_model in camera_list.items():
            if camera_model.data.cameraGroupIds is None or len(camera_model.data.cameraGroupIds) == 0:
                camera_non_group_list.append(camera_model)

        for group_id, group_item in group_list.items():
            item_group = CustomQTreeWidgetItem(parent=parent_item, item_model=None)
            item_group.setIcon(0, QIcon(main_controller.get_theme_attribute('Image', 'group_camera_treeview')))
            item_group.setText(0, group_item.data.name)
            item_group.setFlags(item_group.flags() | Qt.ItemFlag.ItemIsAutoTristate | Qt.ItemIsUserCheckable)
            item_group.setCheckState(0, Qt.CheckState.Unchecked)
            item_group.setDisabled(self.is_view_only)
            if len(group_item.data.cameraIds) > 0:
                for camera_ids in group_item.data.cameraIds:
                    camera_child = camera_list[camera_ids]
                    self.populate_camera_inside_group(parent_group=item_group, camera_item=camera_child)

        for camera_item in camera_non_group_list:
            item_camera = CustomQTreeWidgetItem(parent=parent_item, item_model=camera_item)

            item_camera.setIcon(0, QIcon(Style.PrimaryImage.camera_active_icon_green))
            item_camera.setText(0, camera_item.data.name)
            item_camera.setFlags(item_camera.flags() | Qt.ItemIsUserCheckable)
            item_camera.setCheckState(0, Qt.CheckState.Unchecked)
            if camera_item.data.id in self.role_cameras_ids:
                item_camera.setCheckState(0, Qt.CheckState.Checked)
            item_camera.setDisabled(self.is_view_only)
            # parent_item.appendRow(item_camera)

    def populate_camera_inside_group(self, parent_group: CustomQTreeWidgetItem, camera_item):
        item = CustomQTreeWidgetItem(parent=parent_group, item_model=camera_item)
        item.setText(0, camera_item.data.name)
        item.setIcon(0, QIcon(Style.PrimaryImage.camera_active_icon_green))
        item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
        item.setDisabled(self.is_view_only)
        item.setCheckState(0, Qt.CheckState.Unchecked)
        if camera_item.data.id in self.role_cameras_ids:
            item.setCheckState(0, Qt.CheckState.Checked)
        # parent_group.appendRow(item)

    def get_id_item_checked(self):
        checked_items = []

        # Helper function to recursively traverse the tree and collect checked items
        def traverse_items(item):
            # Check if the current item is checked
            if item.checkState(0) == Qt.CheckState.Checked:
                # Add the item's id to the list
                if hasattr(item, 'item_model') and item.item_model is not None:
                    if hasattr(item.item_model, "data"):
                        checked_items.append(item.item_model.data.id)
                    else:
                        checked_items.append(item.item_model.id)

            # Traverse the child items recursively
            for child_item in range(item.childCount()):
                traverse_items(item.child(child_item))

        # Start traversal from the root item
        root_item = self.tree_widget.invisibleRootItem()
        for i in range(root_item.childCount()):
            traverse_items(root_item.child(i))

        return checked_items


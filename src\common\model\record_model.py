from typing import List
from dataclasses import dataclass
from dataclasses import dataclass, asdict, fields
from PySide6.QtCore import QObject, Signal
import datetime
from typing import Callable
from queue import Queue
import uuid
import threading
from src.utils.utils import Utils
import logging
logger = logging.getLogger(__name__)

@dataclass
class Record:
    id: str = None
    start: str = None
    start_time:datetime.datetime = None
    start_duration: int = None
    end: str = None
    end_time:datetime.datetime = None
    end_duration: int = None
    url: str = None
    cameraId: str = None

    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        return cls(**filtered_dict)

    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}
    
class RecordModel(QObject):
    recordModelChanged = Signal()
    def __init__(self,record: Record = None):
        super().__init__()
        self.data = record
        
class RecordData(QObject):
    recordDataChanged = Signal()
    focus_timeline_signal = Signal()
    def __init__(self,camera_id:str = None, player = None,start_duration:int = 0, end_duration:int = 0):
        super().__init__()
        self.camera_id = camera_id
        self.player = player
        self.data = []
        self.start_position = None
        self.end_position = None
        self.start_duration = start_duration
        self.end_duration = end_duration
        self.registered_widgets = []

    def register_signal(self,widget = None):
        if widget is not None:
            if hasattr(widget, 'recordDataChanged'):
                self.recordDataChanged.connect(widget.recordDataChanged)

            self.registered_widgets.append(widget)

    def unregister_signal(self,widget = None):
        if widget in self.registered_widgets:
            self.registered_widgets.remove(widget)
        return len(self.registered_widgets) == 0

    def get_record(self, position=None, camera_id=None):
        # Returns:
        #     record: trả về thông tin luồng record
        #     duration_time_need_seek: khoảng thời gian cần seek trong trường hợp người dùng click vào giữa khoảng start_time và end_time của record này
        #     duration_to_move: khoảng duration cần di chuyển thanh Scroll của timeline sao cho điểm start_time nằm đúng điểm click
        # """
        if not position:
            print("No position provided")
            return None
        filtered_records = [record for record in self.data if record.data.cameraId == camera_id]
        if not filtered_records:
            print(f"No records found for camera_id: {camera_id}")
            return None
        for record in filtered_records:
            if record.data.start_duration is None or record.data.end_duration is None:
                continue
                # không xử lý case None
            
            if record.data.start_duration <= position <= record.data.end_duration:
                duration_time_need_seek = position - record.data.start_duration
                self.start_position = record.data.start_duration
                self.end_position = record.data.end_duration
                duration_to_move = -1
                return record, duration_time_need_seek, duration_to_move
            elif position < record.data.start_duration:
                duration_time_need_seek = 0
                self.start_position = record.data.start_duration
                self.end_position = record.data.end_duration
                duration_to_move = record.data.start_duration - position
                return record, 0, duration_to_move
        return None
    
    def get_next_record(self, end_duration = 0, camera_id = None):
        filtered_records = [record for record in self.data if record.data.cameraId == camera_id]
        if not filtered_records:
            print(f"No records found for camera_id: {camera_id}")
            return None
        for record in filtered_records:
            if record.data.start_duration is None or record.data.end_duration is None:
                continue
            if record.data.start_duration > end_duration:
                self.start_position = record.data.start_duration
                self.end_position = record.data.end_duration
                return record
        return None
    
    def get_previous_record(self, start_duration = 0, camera_id = None):
        filtered_records = [record for record in self.data if record.data.cameraId == camera_id]
        if not filtered_records:
            print(f"No records found for camera_id: {camera_id}")
            return None
        result = None
        for record in filtered_records:
            if record.data.start_duration is None or record.data.end_duration is None:
                continue
            if record.data.end_duration < start_duration:
                self.start_position = record.data.start_duration
                self.end_position = record.data.end_duration
                result = record

        return result  
       
    def update_record(self,record: RecordModel):
        list_record = record_model_manager.array_record([record])
        self.data[record.id] = list_record[0]

class RecordModelManager(QObject):
    add_records_signal = Signal(str)
    close_record_signal = Signal(str)
    __instance = None
    def __init__(self):
        super().__init__()
        self.options = self.get_options()
        self.configs = {}
        self.record_data = {}
        self.list_widgets ={}
        self.input_queue = Queue()
        # self.threads = self.start_threads(64,self.process_data)
    @staticmethod
    def get_instance():
        if RecordModelManager.__instance is None:
            RecordModelManager.__instance = RecordModelManager()
        return RecordModelManager.__instance
    
    def register_record_data(self,widget = None):
        if widget is not None:
            record_data:RecordData = self.getRecordData(camera_id=widget.camera_model.data.id)
            if record_data is not None:
                record_data.register_signal(widget)
            return record_data
        return None
    
    def unregister_record_data(self,widget = None):
        if widget is not None:
            camera_model = widget.camera_model
            record_data = widget.record_data
            ok = record_data.unregister_signal(widget=widget)
            if ok:
                if camera_model.data.id in self.record_data:
                    del self.record_data[camera_model.data.id]
    
    def add_records(self, record_list = [], dateFrom=None, dateTo=None):
        if record_list is not None and len(record_list) == 0:
            self.add_records_signal.emit(0)
            return
        data_list = []
        temp = {}
        for index, item in enumerate(record_list):
            temp[item['id']] = item
        camera_id = record_list[0]["cameraId"]
        record_data:RecordData = self.record_data.get(camera_id,None)
        if record_data is not None:
            for data in record_data.data:
                if data.data.id in temp:
                    data.data = Record.from_dict(temp[data.data.id])
                    del temp[data.data.id]
            for id, item in temp.items():
                record = Record.from_dict(item)
                recordModel = RecordModel(record=record)
                record_data.data.append(recordModel)

            record_list = self.array_record(record_data.data)
            if not record_list:
                return
            camera_id = record_list[0].data.cameraId
            if dateFrom is None or dateTo is None:
                first_record = record_list[0]
                start_duration = first_record.data.start_duration
                # Lấy thời gian hiện tại dưới dạng ISO format
                now_iso = datetime.datetime.now().isoformat()
                # Chuyển đổi từ ISO format về datetime object
                dt = datetime.datetime.fromisoformat(now_iso)
                end_duration = int(dt.timestamp() * 1000)
            else:
                first_record = record_list[0]
                end_record = record_list[-1]
                start_duration = first_record.data.start_duration
                end_duration = end_record.data.end_duration
            if camera_id:
                print(f'add_records = {camera_id} - {start_duration} - {end_duration}')
                record_data.start_duration = start_duration
                record_data.end_duration = end_duration
                self.add_records_signal.emit(camera_id)


    def array_record(self, record_list: List[RecordModel] = None):
        if record_list is None:
            record_list = []
        record_list_output = {}
        
        for record in record_list:
            record.data.start_duration = Utils.convert_string_to_duration(record.data.start)
            if record.data.end is not None:
                record.data.end_duration = Utils.convert_string_to_duration(record.data.end)
            else:
                record.data.end_duration = None
        record_list.sort(key=lambda x: x.data.start_duration)
        record_list_output = {record.data.id: record for idx, record in enumerate(record_list)}
        
        return record_list
    def get_options(self):
        return None
    def getRecordData(self,camera_id = None):
        if camera_id in self.record_data:
            return self.record_data[camera_id]
        self.record_data[camera_id] = RecordData(camera_id=camera_id)
        return self.record_data[camera_id]
    
    def process_data(self)-> None:
        while True:
            target = self.input_queue.get()
            if target is None:
                break
            id, camera_id, status = target
            if status:
                widget = self.list_widgets[id]
                widget.start_subcribe(camera_id)
            else:
                widget = self.list_widgets[id]
                widget.stop_subcribe(camera_id)
                del self.list_widgets[id]
            self.input_queue.task_done()

    def start_subcribe(self,camera_id = None,widget = None):
        id = uuid.uuid4()
        self.list_widgets[id] = widget
        self.input_queue.put((id,camera_id, True))

    def stop_subcribe(self,camera_id = None,widget = None):
        for id, item in self.list_widgets.items():
            if widget == item:
                self.input_queue.put((id,camera_id, False))
                break

    def start_threads(self,number: int, target: Callable, *args) -> List[threading.Thread]:
        threads = []
        for _ in range(number):
            thread = threading.Thread(target=target, args=args)
            thread.daemon = True
            threads.append(thread)
            thread.start()
        return threads
record_model_manager = RecordModelManager.get_instance()

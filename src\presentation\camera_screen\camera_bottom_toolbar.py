import logging
from typing import List
from src.common.model.aiflows_model import <PERSON><PERSON><PERSON>,AiFlowModel,aiflow_model_manager
from src.common.model.item_grid_model import ItemGridModel
from src.common.widget.widget_for_custom_grid.item_in_menu import ButtonEditLayout, ItemGridMenu
from src.common.model.tab_model import TabModel,tab_model_manager,GridItem,ItemType,SignalType
from PySide6.QtWidgets import QWidget, QHBoxLayout, QFrame, QPushButton
from PySide6.QtGui import QIcon
from src.common.widget.button_state import ButtonState, GridButtonModel
from src.styles.style import Style
from PySide6.QtCore import Signal, QPoint
from src.presentation.device_management_screen.widget.list_custom_widgets import SquareButton
from src.common.controller.main_controller import main_controller
from src.common.model.camera_model import Camera
from src.utils.config import Config
from PySide6.QtCore import Qt
from src.common.model.device_models import TabType
from src.utils.camera_qsettings import Camera_Qsettings, DeleteType
from src.common.widget.camera_widget import CameraWidget,StreamCameraType
from src.common.camera.grid_item_selected import grid_item_selected
from src.common.controller.controller_manager import Controller, controller_manager
import uuid
from src.common.model.camera_model import Camera, CameraModel, camera_model_manager
from src.utils.theme_setting import ThemeSettings, theme_setting

logger = logging.getLogger(__name__)

class CameraBottomToolbarWidget(QWidget):
    signal_reload_grid = Signal(object)
    signal_update_grid = Signal(tuple)
    current_full_screen_value = ButtonState.FullscreenType.EXIT_FULLSCREEN
    current_exit_stream_value = ButtonState.ExitStreamType.DISABLE
    current_grid_value = GridButtonModel.StandardGrid.DIVISIONS_1
    current_stream_flow_value = ButtonState.StreamFlowType.DISABLE

    def __init__(self, parent=None):
        super(CameraBottomToolbarWidget, self).__init__(parent)
        self.list_custom_grid_model = None
        self.exit_all = None
        self.stream_flow = None
        self.grid = None
        grid_item_selected.widget_changed.connect(self.widget_changed)
        main_controller.stream_status_signal.connect(self.stream_status_signal)
        self.temp=self.get_grid_list()
        self.setup_ui()
        main_controller.open_map_2D_signal.connect(self.button_save_map.show)

    def setup_ui(self):
        # create a horizontal layout
        self.main_layout = QHBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        self.background = QFrame(self)
        self.background.setStyleSheet(
            "QFrame { border: 1px solid transparent; border-radius: 4px;}")
        self.background.setLayout(self.main_layout)

        # create a layout contain background
        self.background_layout = QHBoxLayout()
        self.background_layout.setContentsMargins(0, 0, 0, 0)
        self.background_layout.setSpacing(0)
        self.background_layout.addWidget(self.background)
        self.setLayout(self.background_layout)

        # right side toolbar
        self.right_toolbar_layout = QHBoxLayout()
        self.right_toolbar_layout.setContentsMargins(4, 0, 4, 0)
        self.right_toolbar_layout.setSpacing(4)
        self.right_toolbar_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        right_toolbar_widget = QWidget()
        right_toolbar_widget.setLayout(self.right_toolbar_layout)
        self.main_layout.addWidget(right_toolbar_widget, 20)

        self.button_save_map = QPushButton(self.tr('Save'))
        self.button_save_map.setStyleSheet(Style.StyleSheet.style_button_add)
        self.button_save_map.clicked.connect(self.button_save_map_clicked)
        self.button_save_map.hide()

        # Exit Stream
        exit_all_icon = main_controller.get_theme_attribute("Image", "exit_stream_off")
        self.exit_all = SquareButton(icon=QIcon(exit_all_icon), feature=ButtonState.ButtonFeature.EXIT_STREAM)
        self.exit_all.clicked = self.exit_all_clicked
        self.exit_all.setToolTip(self.tr('Exit all'))

        # Grid
        standard_grid_models = [
            GridButtonModel.StandardGrid.DIVISIONS_1,
            GridButtonModel.StandardGrid.DIVISIONS_4,
            GridButtonModel.StandardGrid.DIVISIONS_9,
            GridButtonModel.StandardGrid.DIVISIONS_16,
            GridButtonModel.StandardGrid.DIVISIONS_36,
            GridButtonModel.StandardGrid.DIVISIONS_64,
        ]
        list_item_grid_model = (standard_grid_models, self.list_custom_grid_model)
        grid_icon = main_controller.get_theme_attribute("Image", "grid_off")
        self.grid = SquareButton(icon=QIcon(grid_icon), feature=ButtonState.ButtonFeature.GRID, list_item_grid_model=list_item_grid_model)
        self.grid.setToolTip(self.tr('Grid'))
        self.grid.signal_emit_only_grid.connect(lambda data: self.update_grid(data))

        # Video Stream
        stream_icon = main_controller.get_theme_attribute("Image", "stream_flow_off")
        stream_icon_2 = main_controller.get_theme_attribute("Image", "stream_flow_2_off")
        self.list_stream_flow = [self.tr("Main"), self.tr("Sub")]
        self.list_icon_stream_flow = [stream_icon, stream_icon_2]
        self.stream_flow = SquareButton(icon=QIcon(stream_icon), list_menu_name=self.list_stream_flow,
                                        list_menu_icon=self.list_icon_stream_flow,
                                        feature=ButtonState.ButtonFeature.STREAM_FLOW)
        self.stream_flow.setToolTip(self.tr('Video Stream'))
        main_controller.stream_status_signal.emit((ButtonState.Status.DISABLE,None))

        # add to right side toolbar
        self.right_toolbar_layout.addWidget(self.button_save_map)
        self.right_toolbar_layout.addWidget(self.exit_all)
        self.right_toolbar_layout.addWidget(self.grid)
        self.right_toolbar_layout.addWidget(self.stream_flow)

    def update_control_status(self,tab_model:TabModel):
        if tab_model.data.type == TabType.MapView or tab_model.data.type == TabType.FloorView:
            self.exit_all.hide()
            self.grid.hide()
            self.stream_flow.hide()
            self.button_save_map.show()
        else:
            self.exit_all.show()
            self.grid.show()
            self.stream_flow.show()
            self.button_save_map.hide()

    def update_grid(self, data):
        index, button_feature, data_merged = data
        new_data = (index, button_feature, data_merged, self.camera_grid_widget)
        self.signal_update_grid.emit(new_data)

    def reload_grid_menu(self, list_reload):
        json_list_custom_model = [item.to_dict() for item in list_reload]
        tab_model: TabModel = tab_model_manager.get_tab_model(tab_name=self.main_controller.current_tab_name)
        tab_model.data.currentGrid = self.current_grid_value.to_dict()
        tab_model.data.listGridCustomData = json_list_custom_model

        self.update_ui_grid_menu(list_reload, self.current_grid_value)
        self.reload_current_grid_preview(list_reload)

    def reload_current_grid_preview(self, list_reload):
        current_grid_type = self.camera_grid_widget.current_grid_type
        for item in list_reload:
            if current_grid_type == item.grid_type:
                data = (item, list_reload, self.camera_grid_widget)
                self.signal_reload_grid.emit(data)

    def update_ui_grid_menu(self, new_data, current_grid_value):
        for item_grid in reversed(range(self.grid.grid_custom.count())):
            widget = self.grid.grid_custom.itemAt(item_grid).widget()
            self.grid.grid_custom.removeWidget(widget)
            widget.setParent(None)

        row_custom, col_custom = 0, 0
        for data_custom in new_data:
            widget_custom = ItemGridMenu(model_grid=data_custom, type_division="CUSTOM_DIVISIONS")
            widget_custom.emit_size_signal.connect(self.grid.select_grid_type_value)
            self.grid.grid_custom.addWidget(widget_custom, row_custom, col_custom)
            col_custom += 1
            if col_custom == 6:
                col_custom = 0
                row_custom += 1

        self.button_edit_grid = ButtonEditLayout(title=self.tr("Edit"), image_path=main_controller.get_theme_attribute("Image", "edit_layout_grid"))
        self.button_edit_grid .button_click_signal.connect(lambda: self.grid.signal_emit_only_grid.emit((0, self.grid.feature, "EDIT_LAYOUTS_TRIGGERED")))
        self.grid.grid_custom.addWidget(self.button_edit_grid, 0, 4)

        # update current_grid_value
        self.current_grid_value = current_grid_value
        # update icon
        self.grid.setIcon(QIcon(current_grid_value.image_url))

    def get_ai_activate(self, aiFlowIds:List = [], controller=None):
        list_ai_activate = {}
        for aiFlowId in aiFlowIds:
            aiflow: AiFlowModel = aiflow_model_manager.get_aiflow_model(id = aiFlowId)
            if aiflow is not None and aiflow.data.is_apply():
                list_ai_activate[aiFlowId] = aiflow
        return list_ai_activate
    
    def clear_all_ai_flow_stream(self):
        # clear action Video Stream except first action and second action
        list_action = self.stream_flow.main_menu.actions()
        if len(list_action) > 2:
            for i in range(2,len(list_action)):
                self.stream_flow.main_menu.removeAction(list_action[i])

    def widget_changed(self,data):
        camera_widget = data
        if isinstance(camera_widget,CameraWidget):
            main_controller.stream_status_signal.emit((ButtonState.Status.ENABLE,camera_widget))
        else:
            main_controller.stream_status_signal.emit((ButtonState.Status.DISABLE,None))

    def stream_status_signal(self, data):
        # Cập nhật trạng thái button stream_flow mỗi khi người dùng click vào 1 gridItem hoặc người dùng thay đổi luồng stream
        status,widget = data
        if status == ButtonState.Status.DISABLE:
            self.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_off")))
            self.stream_flow.setDisabled(True)
            list_action = self.stream_flow.main_menu.actions()
            for i in list_action:
                if i.text() == "AI Stream" or i.text() == "Sub":
                    i.setDisabled(True)
        elif status == ButtonState.Status.ENABLE and widget is not None:
            list_action = self.stream_flow.main_menu.actions()
            for index, action in enumerate(list_action):
                if index == 0 and action.text() == self.tr("Main"):
                    action.setData((index,None, widget.camera_model.data.id))
                if action.text() == self.tr("Sub"):
                    action.setData((index,None, widget.camera_model.data.id))
                    action.setDisabled(False)
            self.add_aiflow_stream_action_to_menu(aiFlowIds=widget.camera_model.data.aiFlowIds, controller=widget.controller,
                                                  camera_model=widget.camera_model)
            
            if widget.video_capture is not None:
                if widget.stream_type == StreamCameraType.main_stream:
                    self.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_off")))
                elif widget.stream_type == StreamCameraType.sub_stream:
                    self.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_2_off")))
                else:
                    self.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_3_off")))
            self.stream_flow.setDisabled(False)

    def add_aiflow_stream_action_to_menu(self, aiFlowIds=None, controller=None, camera_model=None):
        # Xóa tất cả action trừ 2 action đầu tiên và tạo lại list action ai mới
        self.clear_all_ai_flow_stream()
        if len(camera_model.data.features) > 0:
            for ai_type in camera_model.data.features:
                self.stream_flow.add_action(ai_type=f"{ai_type}", model=camera_model)

    def exit_all_clicked(self,is_emit = True,is_websocket_used = False):
        for index,grid_item in self.camera_grid_widget.tab_model.data.listGridData.items():
            
            if grid_item.widget != None:
                grid_item.widget.stack_item.grid_item_unclicked() 
                if isinstance(grid_item.widget, CameraWidget):
                    if grid_item.widget is not None and grid_item.widget.video_capture is not None:
                        grid_item.widget.close_widget()
                        grid_item.widget.stop_video()
                    if grid_item.virtual_widget is not None and grid_item.virtual_widget.video_capture is not None:
                        grid_item.virtual_widget.close_widget()
                        grid_item.virtual_widget.stop_video()
                    # kiem tra xem trong grid co camera dang duoc chon
                    if grid_item_selected.data['tab_index'] is not None and grid_item_selected.is_tab_index(main_controller.current_tab):
                        grid_item_selected.clear()
                        if Config.ENABLE_STREAM_FLOW_BUTTON:
                            main_controller.stream_status_signal.emit((ButtonState.Status.DISABLE,None))

        self.current_grid_value = GridButtonModel.StandardGrid.DIVISIONS_1
        self.camera_grid_widget.tab_model.data.currentGrid = self.current_grid_value.to_dict()
        self.camera_grid_widget.tab_model.data.listGridData.clear()
        self.camera_grid_widget.change_grid_view(self.current_grid_value)
        self.camera_grid_widget.camera_screen.default_timelinecontroller()
        if self.camera_grid_widget.tab_model.data.type != TabType.Invalid:
            if is_emit:
                self.camera_grid_widget.tab_model.change_grid_view_signal.emit(self.camera_grid_widget.tab_model.data.currentGrid)
                if not is_websocket_used:
                    controller = controller_manager.get_controller(server_ip=self.camera_grid_widget.tab_model.data.server_ip)
                    self.camera_grid_widget.tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.RemoveAllItem,'data':None}
                    controller.update_tabmodel_by_put(parent=self, tab= self.camera_grid_widget.tab_model.data)
        main_controller.gc_collect(self.camera_grid_widget)

    def get_grid_list(self):
        list_custom_grid_model = [
            ItemGridModel(name_grid='6 ' + self.tr(f"Divisions"), data=[{(0, 1), (1, 0), (1, 1), (0, 0)}], row=3,
                          column=3,
                          total_grid_count=9,
                          image_url=main_controller.get_theme_attribute("Image", "custom_6_grid"),
                          grid_type=ButtonState.GridType.GRID_6_CUSTOM, divisions=6,
                          divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS,
                          grid_path="custom_6_grid"),

            ItemGridModel(name_grid='8 ' + self.tr(f"Divisions"),
                          data=[{(0, 1), (1, 2), (2, 1), (0, 0), (1, 1), (2, 0), (0, 2), (2, 2), (1, 0)}],
                          row=4, column=4, total_grid_count=16,
                          image_url=main_controller.get_theme_attribute("Image", "custom_8_grid"),
                          grid_type=ButtonState.GridType.GRID_8_CUSTOM, divisions=8,
                          divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS,
                          grid_path="custom_8_grid"),

            ItemGridModel(name_grid='10 ' + self.tr(f"Divisions"),
                          data=[{(0, 1), (1, 0), (1, 1), (0, 0)}, {(1, 2), (0, 2), (0, 3), (1, 3)}],
                          row=4, column=4, total_grid_count=16,
                          image_url=main_controller.get_theme_attribute("Image", "custom_10_grid"),
                          grid_type=ButtonState.GridType.GRID_10_CUSTOM, divisions=10,
                          divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS,
                          grid_path="custom_10_grid"),

            ItemGridModel(name_grid='13 ' + self.tr(f"Divisions"), data=[{(1, 1), (1, 2), (2, 1), (2, 2)}],
                          row=4, column=4, total_grid_count=16,
                          image_url=main_controller.get_theme_attribute("Image", "custom_13_grid"),
                          grid_type=ButtonState.GridType.GRID_13_CUSTOM, divisions=13,
                          divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS,
                          grid_path="custom_13_grid")
        ]
        standard_grid_models = [
            ItemGridModel(name_grid="1 Divisions", data=[], total_grid_count=1, row=1, column=1,
                          image_url=main_controller.get_theme_attribute("Image", "grid_1x1"),
                          grid_type=ButtonState.GridType.GRID_1,
                          divisions=1, divisions_type=ButtonState.DivisionType.STANDARD_DIVISIONS,
                          grid_path="grid_1x1"),
            ItemGridModel(name_grid="4 Divisions", data=[], total_grid_count=4, row=2, column=2,
                          image_url=main_controller.get_theme_attribute("Image", "grid_2x2"),
                          grid_type=ButtonState.GridType.GRID_4,
                          divisions=4, divisions_type=ButtonState.DivisionType.STANDARD_DIVISIONS,
                          grid_path="grid_2x2"),
            ItemGridModel(name_grid="9 Divisions", data=[], total_grid_count=9, row=3, column=3,
                          image_url=main_controller.get_theme_attribute("Image", "grid_3x3"),
                          grid_type=ButtonState.GridType.GRID_9,
                          divisions=9, divisions_type=ButtonState.DivisionType.STANDARD_DIVISIONS,
                          grid_path="grid_3x3"),
            ItemGridModel(name_grid="16 Divisions", data=[], total_grid_count=16, row=4, column=4,
                          image_url=main_controller.get_theme_attribute("Image", "grid_4x4"),
                          grid_type=ButtonState.GridType.GRID_16,
                          divisions=16, divisions_type=ButtonState.DivisionType.STANDARD_DIVISIONS,
                          grid_path="grid_4x4"),
            ItemGridModel(name_grid="36 Divisions", data=[], total_grid_count=36, row=6, column=6,
                          image_url=main_controller.get_theme_attribute("Image", "grid_6x6"),
                          grid_type=ButtonState.GridType.GRID_36,
                          divisions=36, divisions_type=ButtonState.DivisionType.STANDARD_DIVISIONS,
                          grid_path="grid_6x6"),
            ItemGridModel(name_grid="64 Divisions", data=[], total_grid_count=64, row=8, column=8,
                          image_url=main_controller.get_theme_attribute("Image", "grid_8x8"),
                          grid_type=ButtonState.GridType.GRID_64,
                          divisions=64, divisions_type=ButtonState.DivisionType.STANDARD_DIVISIONS,
                          grid_path="grid_8x8")
        ]
        if self.list_custom_grid_model is None:
            self.list_custom_grid_model = list_custom_grid_model
            list_item_grid_model = (standard_grid_models, list_custom_grid_model)
        else:
            for index, item in enumerate(self.list_custom_grid_model):
                if index == 0:
                    item.image_url = main_controller.get_theme_attribute("Image", "custom_6_grid")
                elif index == 1:
                    item.image_url = main_controller.get_theme_attribute("Image", "custom_8_grid")
                elif index == 2:
                    item.image_url = main_controller.get_theme_attribute("Image", "custom_10_grid")
                elif index == 3:
                    item.image_url = main_controller.get_theme_attribute("Image", "custom_13_grid")

            list_item_grid_model = (standard_grid_models, self.list_custom_grid_model)

        return list_item_grid_model

    def retranslateUi(self):
        if Config.ENABLE_CLOSE_STREAMING_BUTTON:
            self.exit_all.setToolTip(self.tr('Exit all'))
        if Config.ENABLE_ASPECT_RATIO_BUTTON:
            self.window_size.setToolTip(self.tr('Window size'))
        if Config.ENABLE_GRID_BUTTON:
            self.grid.setToolTip(self.tr('Grid'))
        if Config.ENABLE_STREAM_FLOW_BUTTON:
            self.stream_flow.setToolTip(self.tr('Video Stream'))
        if Config.ENABLE_RECORD_BUTTON:
            self.record.setToolTip(self.tr('Record Video'))
        if Config.ENABLE_CAPTURE_BUTTON:
            self.capture.setToolTip(self.tr('Capture'))
        if Config.ENABLE_MICROPHONE_BUTTON:
            self.microphone.setToolTip(self.tr('Microphone'))
        if Config.ENABLE_SPEAKER_BUTTON:
            self.volume.setToolTip(self.tr('Volume'))
        if Config.ENABLE_FULL_SCREEN_BUTTON:
            self.fullscreen.setToolTip(self.tr('Full screen'))
        # self.button_edit_grid.label_title.setText(QCoreApplication.translate("CameraBottomToolbarWidget", u'Edit', None))

    def restyle_camera_bottom_toolbar(self):
        exit_all_icon = main_controller.get_theme_attribute("Image", "exit_stream_off")
        stream_icon = main_controller.get_theme_attribute("Image", "stream_flow_off")
        grid_icon = main_controller.get_theme_attribute('Image', self.current_grid_value.grid_path)
        self.exit_all.setStyleSheet(Style.PrimaryStyleSheet.get_tool_button_style(theme_instance=main_controller, icon=exit_all_icon))
        self.grid.change_list_grid_model(new_list_grid_model=self.get_grid_list())
        self.grid.setStyleSheet(Style.PrimaryStyleSheet.get_tool_button_style(theme_instance=main_controller, icon=grid_icon))
        self.stream_flow.setStyleSheet(Style.PrimaryStyleSheet.get_tool_button_style(theme_instance=main_controller, icon=stream_icon))

    def button_save_map_clicked(self):
        tab_model = self.camera_grid_widget.tab_model

        if tab_model is not None:
            if tab_model.data.type == TabType.FloorView:
                grid_item = tab_model.data.listGridData.get(0,None)
                if grid_item is not None:
                    grid_item.widget.map_controller.saveCameraListSignal.emit()
            if tab_model.data.type == TabType.MapView:
                grid_item = tab_model.data.listGridData.get(0,None)
                if grid_item is not None:
                    grid_item.widget.update_map_model()

    def set_camera_grid_widget(self, camera_grid_widget):
        self.camera_grid_widget = camera_grid_widget
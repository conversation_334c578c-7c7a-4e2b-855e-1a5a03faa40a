from PySide6.QtCore import QObject, Property, Signal
from src.common.model.camera_model import Camera, CameraModel, camera_model_manager

class ScheduleModel(QObject):
    cameraIdChanged = Signal()
    scheduleTimeChanged = Signal()
    handleTypeChanged = Signal()
    def __init__(self,data:dict = {}):
        super().__init__()
        self.data = data if isinstance(data, dict) else {}

    def get_property(self, key, default=None):
        """Lấy giá trị từ self.data, nếu không có thì trả về default."""
        if not isinstance(self.data, dict):
            return default
        return self.data.get(key, default)

    def set_property(self, key, value):
        """Cập nhật giá trị trong self.data."""
        if not isinstance(self.data, dict):
            self.data = {}
        self.data[key] = value

    @Property(str, notify=cameraIdChanged)
    def cameraId(self)->str:
        return self.get_property("cameraId", None)
       
    @Property(str,notify=scheduleTimeChanged)
    def scheduleTime(self):
        return self.get_property("scheduleTime", None)
    
    @scheduleTime.setter
    def scheduleTime(self, value: str):
        if self.get_property("scheduleTime", None) != value:
            self.set_property("scheduleTime",value)
            self.scheduleTimeChanged.emit() 

    @Property(str,notify=handleTypeChanged)
    def handleType(self):
        return self.get_property("handleType", None)
    
    @handleType.setter
    def handleType(self, value: str):
        if self.get_property("handleType", None) != value:
            self.set_property("handleType",value)
            self.handleTypeChanged.emit() 
   
class ScheduleManager(QObject):
    __instance = None
    def __init__(self):
        super().__init__()
        self.data = {}

    @staticmethod
    def get_instance():
        if ScheduleManager.__instance is None:
            ScheduleManager.__instance = ScheduleManager()
        return ScheduleManager.__instance
    
    def add_schedule_list(self,data = []):
        for item in data:
            scheduleModel = ScheduleModel(data=item)
            if scheduleModel.cameraId is not None:
                if scheduleModel.cameraId not in self.data:
                    self.data[scheduleModel.cameraId] = scheduleModel
                else:
                    self.data[scheduleModel.cameraId].data = item

    def add_schedule(self,data = {}):
        scheduleModel = ScheduleModel(data=data)
        if scheduleModel.cameraId is not None:
            if scheduleModel.cameraId not in self.data:
                self.data[scheduleModel.cameraId] = scheduleModel
            else:
                self.data[scheduleModel.cameraId].data = data
                
            cameraModel:CameraModel = camera_model_manager.get_camera_model(id = scheduleModel.cameraId)
            if cameraModel is not None:
                cameraModel.recordSetting = True if scheduleModel.handleType == "START" else False   

    def get_schedule_model(self,cameraId = None):
        if cameraId is not None and cameraId in self.data:
            return self.data[cameraId]
        return None
    
schedule_manager = ScheduleManager.get_instance()
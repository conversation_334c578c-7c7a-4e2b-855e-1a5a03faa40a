from PySide6.QtCore import QObject, Property, Signal, QTimer
from typing import Optional
from src.common.qml.models.ruler_context import RulerContext
from src.common.model.record_model import Record,RecordModel,record_model_manager
import logging
logger = logging.getLogger(__name__)
class DurationPresent(QObject):
    # Signals
    relativeXChanged = Signal()
    relativeWidthChanged = Signal()

    def __init__(self, ctx: RulerContext, recordModel:RecordModel = None):
        """
        Initialize DurationPresent
        Args:
            ctx: RulerContext instance
            start_pos: Start position in milliseconds
            end_pos: End position in milliseconds
        """
        super().__init__(ctx)
        self._ctx = ctx
        self._start_pos = 0 if recordModel is None else recordModel.data.start_duration 
        self._end_pos = 0 if recordModel is None else recordModel.data.end_duration
        self.recordModel = recordModel
        if self.recordModel:
            self.recordModel.recordModelChanged.connect(self.recordModelChanged)
        # Connect signals from RulerContext
        self._ctx.baseChanged.connect(self.relativeXChanged)
        self._ctx.baseChanged.connect(self.relativeWidthChanged)

    def recordModelChanged(self):
        logger.info(f'recordModelChanged')
        self._end_pos = self.recordModel.data.end_duration
        self.calculateRelativeWidth()
        
    def calculateRelativeX(self) -> float:
        """Calculate relative X position"""
        absolute_start = self._ctx.absoluteStart
        absolute_stop = self._ctx.absoluteStop

        if self._start_pos < absolute_start:
            return 0

        if self._start_pos > absolute_stop:
            return self._ctx.visibleWidth()

        return (self._start_pos - absolute_start) * self._ctx.widthPerMili

    def calculateRelativeWidth(self) -> float:
        """Calculate relative width"""
        absolute_start = self._ctx.absoluteStart
        absolute_stop = self._ctx.absoluteStop

        if (self._start_pos > self._end_pos or 
            self._end_pos < absolute_start or 
            self._start_pos > absolute_stop):
            return 0

        temp_start = max(self._start_pos, absolute_start)
        temp_end = min(self._end_pos, absolute_stop)

        return (temp_end - temp_start) * self._ctx.widthPerMili

    def setStartPos(self, start_pos: int) -> bool:
        """
        Set start position
        Args:
            start_pos: New start position in milliseconds
        Returns:
            bool: True if position changed, False otherwise
        """
        if self._start_pos == start_pos:
            return False

        self._start_pos = start_pos
        return True

    def setEndPos(self, end_pos: int) -> bool:
        """
        Set end position
        Args:
            end_pos: New end position in milliseconds
        Returns:
            bool: True if position changed, False otherwise
        """
        if self._end_pos == end_pos:
            return False

        self._end_pos = end_pos
        return True

    @Property(float, notify=relativeXChanged)
    def relativeX(self) -> float:
        """Get relative X position"""
        return self.calculateRelativeX()

    @relativeX.setter
    def relativeX(self, x: float):
        """
        Set relative X position
        Args:
            x: New relative X position
        """

        time_from_x = (x / self._ctx.widthPerMili) if self._ctx.widthPerMili !=0 else 0
        new_start_pos = self._ctx.absoluteStart + int(time_from_x)
        if self.setStartPos(new_start_pos):
            self.relativeXChanged.emit()

    @Property(float, notify=relativeWidthChanged)
    def relativeWidth(self) -> float:
        """Get relative width"""
        # if self.last_index:
        #     now_iso = datetime.datetime.now().isoformat()
        #     # Chuyển đổi từ ISO format về datetime object
        #     dt = datetime.datetime.fromisoformat(now_iso)

        #     # Lấy timestamp (milliseconds từ Epoch)
        #     end_duration = int(dt.timestamp() * 1000)
        #     self._end_pos = end_duration
        return self.calculateRelativeWidth()

    @relativeWidth.setter
    def relativeWidth(self, width: float):
        """
        Set relative width
        Args:
            width: New relative width
        """
        time_from_width = width / self._ctx.widthPerMili
        new_end_pos = self._start_pos + int(time_from_width)
        if self.setEndPos(new_end_pos):
            self.relativeWidthChanged.emit()
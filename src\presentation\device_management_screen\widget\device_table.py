from PySide6 import QtWidgets
from PySide6.QtQuickWidgets import QQuickWidget
from PySide6.QtWidgets import QWidget, QCheckBox, QHBoxLayout
from PySide6.QtCore import Qt, QCoreApplication,QUrl
from src.common.controller.main_controller import main_controller,connect_slot
from src.common.model.camera_model import CameraModel,camera_model_manager
from src.common.model.group_model import GroupModel, group_model_manager
from src.styles.style import Style
from src.common.model.device_models import CameraType, FilterType
from src.common.onvif_api.worker_thread import WorkerThread
from src.common.controller.controller_manager import Controller
from queue import Queue
from src.presentation.device_management_screen.widget.ai_state import AIType,AIFlowType
from src.common.widget.pagination.page_indicator.page_indicator import Pagination
from src.common.qml.models.device_controller import DeviceController,ListModel,ModelType,AIState,ItemRoles
from src.common.model.door_model import Door,DoorModel,door_model_manager
from src.common.model.aiflows_model import aiflow_model_manager
from typing import List
import logging

logger = logging.getLogger(__name__)
class DeviceTable(QWidget):
    def __init__(self, parent=None,controller:Controller = None, items_per_page=15, data=None, row=10, column=7, header_data=[],width = None, height = None):
        super().__init__(parent)
        self.setObjectName('DeviceTable')
        self.controller = controller
        self.page_indicator = None
        self.tab_divice_widget = parent
        self.widget_width = width
        self.widget_height = height
        self.finalPageButton = None
        self.nextButton = None
        self.prevButton = None
        self.firsePageButton = None
        self.pageSpinBox = None
        self.totalPapes = None
        main_controller.list_parent['DeviceTable'] = self
        self.controller.update_state_delegate = self.update_state_delegate
        self.header_data = header_data
        self.items_per_page = items_per_page
        self.data = data if data is not None else []
        ##############################
        self.camera_group_list = [{'id':None, 'data': 'All'}]
        self.filter_device_group = False
        self.filter_status = False
        self.filter_ai = False
        self.filter_device_type = False
        self.filter_device_name = False
        self.is_loading = False
        self.filter_queue = Queue()
        self.fitler_camera_thread = WorkerThread(parent=self, target= self.update_data_to_table,args=(True,), callback=self.callback_filter_device)
        self.table = None
        ###############################
        self.setup_ui()
        self.set_dynamic_stylesheet()
        self.is_updating = False
        self.connect_slot()

    def connect_slot(self):
        connect_slot(
            (self.controller.device_item_expanded_signal, self.device_item_expanded_signal)
        )

    def setup_ui(self):
        layout = QtWidgets.QVBoxLayout()
        # add QML
        self.device_controller = DeviceController(parent=self)
        self.quick_widget = QQuickWidget()
        self.quick_widget.engine().rootContext().setContextProperty('device_controller', self.device_controller)
        self.quick_widget.setSource(QUrl("qrc:src/common/qml/device_table/DeviceTable.qml"))
        self.quick_widget.setResizeMode(QQuickWidget.SizeRootObjectToView)
        self.current_page = 1
        # Page Indicator:
        self.page_indicator = self.create_pagination()
        self.widget_indicator = QWidget()
        self.widget_indicator.setObjectName('widget_indicator')
        layout_indicator = QHBoxLayout()
        layout_indicator.setContentsMargins(0, 0, 0, 0)
        layout_indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_indicator.addWidget(self.page_indicator)
        self.widget_indicator.setLayout(layout_indicator)

        # add to layout
        layout.addWidget(self.quick_widget)
        # layout.addWidget(self.scroll_widget)
        layout.addWidget(self.widget_indicator)
        layout.setStretch(0, 94)
        layout.setStretch(1, 6)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        self.setLayout(layout)
    
    def create_pagination(self):
        page_indicator = Pagination(rows_per_page=14)
        page_indicator.signal_update_table.connect(self.signal_update_table)
        if page_indicator is not None:
            page_indicator.set_total_rows_and_total_pages(len(self.controller.device_data_filtered))
        return page_indicator
    
    def signal_update_table(self, data):
        self.update_table()

    def callback_filter_device(self, data=None):
        self.update_table()

    def put_filter_queue(self, msg):
        self.filter_queue.put(msg)
        self.update_data_to_table()

    def update_data_to_table(self, is_thread = True):
        group_list = group_model_manager.get_group_list(server_ip=self.controller.server.data.server_ip)
        camera_list = camera_model_manager.get_camera_list(server_ip=self.controller.server.data.server_ip)
        doors = door_model_manager.get_doors(server_ip=self.controller.server.data.server_ip)
        data = []
        for ai_box in group_list.values():
            # if ai_box.data.type == 'AI_BOX' and ai_box.data.name == "BOX CAMERA TEST 100":
            if ai_box.data.type == 'AI_BOX':
                data.append(ai_box)
        for camera_model in camera_list.values():
            data.append(camera_model)
        
        for door_model in doors.values():
            data.append(door_model)

        if is_thread:
            while not self.filter_queue.empty():
                msg = self.filter_queue.get()
                if not self.is_loading:
                    self.is_loading = True
                    self.controller.device_data_filtered = []
                    if not self.filter_device_name and not self.filter_device_group and not self.filter_status and not self.filter_ai and not self.filter_device_type:
                        self.controller.device_data_filtered = data
                        self.is_loading = False
                        self.callback_filter_device()
                        return
                    # device_screen = main_controller.list_parent['DeviceScreen']
                    # filter AI ###########################################
                    device_type_dist = []
                    if self.filter_device_type:
                        device_type = self.tab_divice_widget.search_device_type.combobox.currentIndex()
                        # device_type = self.get_device_type(device_type)
                        for item in data:
                            if isinstance(item,CameraModel) and device_type == 1:
                                device_type_dist.append(item)
                            elif isinstance(item,GroupModel) and device_type == 2:
                                device_type_dist.append(item)
                            elif isinstance(item,DoorModel) and device_type == 3:
                                # logger.debug(f'doors = {doors}')
                                device_type_dist.append(item)

                    else:
                        device_type_dist = data 
                    ai_dist = []
                    if self.filter_ai:
                        ai_index = self.tab_divice_widget.search_ai.combobox.currentIndex()
                        for item in device_type_dist:
                            if isinstance(item,CameraModel):
                                for ai_type in item.data.features:
                                    ok = self.get_ai_type(ai_index,ai_type)
                                    if ok:
                                        ai_dist.append(item)
                                        break
                                # for id in item.data.aiFlowIds:
                                #     aiflow:AiFlowModel = aiflow_model_manager.get_aiflow_model(id = id)
                                #     if aiflow is not None and aiflow.data.is_apply() and aiflow.data.type == ai_type:
                                #         ai_dist.append(item)
                                #         break
                            elif isinstance(item,GroupModel):
                                # is_human,is_vehicle = item.data.aiApply()
                                # if ai_type == is_human or ai_type == is_vehicle:
                                #     ai_dist.append(item)
                                pass
                            # elif isinstance(item,DoorModel):
                            #     # logger.debug(f'doors = {doors}')
                            #     device_type_dist.append(item)
                    else:
                        ai_dist = device_type_dist 
                    # filter camera group ##########################
                    device_group_dist = []

                    if self.filter_device_group:
                        camera_group_type = self.tab_divice_widget.search_group.combobox.currentIndex()
                        for index, group in enumerate(self.camera_group_list):
                            if camera_group_type == index:
                                for item in ai_dist:
                                    if isinstance(item,CameraModel):
                                        if item.data.cameraGroupIds is not None and group["id"] in item.data.cameraGroupIds:
                                            device_group_dist.append(item)
                                    elif isinstance(item,GroupModel):
                                        if item.data.parentGroupId is not None and group["id"] in item.data.parentGroupId:
                                            device_group_dist.append(item)
                                    # elif isinstance(item,DoorModel):
                                    #     # logger.debug(f'doors = {doors}')
                                    #     device_type_dist.append(item)

                    else:
                        device_group_dist = ai_dist
                    # filter camera name ###################################
                    device_name_dist = []
                    if self.filter_device_name:
                        text = msg[FilterType.SearchCameraName].lower()
                        for item in device_group_dist:
                            # camera_model = item.get_key(CameraType().camera_model)
                            if text in item.data.name.lower():
                                device_name_dist.append(item)
                    else:
                        device_name_dist = device_group_dist
                    status_dist = []
                    if self.filter_status:
                        status_type = self.tab_divice_widget.search_status.combobox.currentIndex()
                        if status_type == 1:
                            for item in device_name_dist:
                                if item.data.state == "CONNECTED":
                                    status_dist.append(item)
                        elif status_type == 2:
                            for item in device_name_dist:
                                if item.data.state == "DISCONNECTED" or item.data.state == "CONNECTING":
                                    status_dist.append(item)
                        else:
                            status_dist = device_name_dist
                    else:
                        status_dist = device_name_dist
                    # thông tin danh sách camera cuối cùng được show lên UI
                    self.controller.device_data_filtered = status_dist
                self.is_loading = False
                self.filter_queue.task_done()
        else:
            self.controller.device_data_filtered = data
        self.callback_filter_device()

    def get_device_type(self,type = 0):
        if type != 0:
            if type == 1:
                return "Camera"
            elif type == 2:
                return "AIBox"
            elif type == 3:
                return "Gate"
        return None
    
    def get_ai_type(self,index = 0,ai_type = AIFlowType.RECOGNITION):
        if index != 0:
            if index == 1:
                return ai_type == AIFlowType.RECOGNITION or ai_type == AIFlowType.PROTECTION or ai_type == AIFlowType.FREQUENCY or ai_type == AIFlowType.ACCESS or ai_type == AIFlowType.MOTION or ai_type == AIFlowType.TRAFFIC
            elif index == 2:
                return ai_type == AIFlowType.WEAPON or ai_type == AIFlowType.UFO
        return False
    
    def update_state_delegate(self):
        for row in range(self.controller.total_camera_items):
            index = self.table.model().index(row, 0)
            editor = self.table.indexWidget(index)
            if editor != None:
                check_box_delegate = editor.findChild(QCheckBox, 'checkbox')
                check_box_delegate.setCheckState(self.controller.all_checkbox)


    def convert_data(self,model,child):
        data = {
            ItemRoles.RECOGNITION:  {'id': None,'state':AIState.AIOFF},
            ItemRoles.PROTECTION:  {'id': None,'state':AIState.AIOFF},
            ItemRoles.FREQUENCY: {'id': None,'state':AIState.AIOFF},
            ItemRoles.ACCESS: {'id': None,'state':AIState.AIOFF},
            ItemRoles.MOTION: {'id': None,'state':AIState.AIOFF},
            ItemRoles.TRAFFIC: {'id': None,'state':AIState.AIOFF},
            ItemRoles.WEAPON: {'id': None,'state':AIState.AIOFF},
            ItemRoles.UFO: {'id': None,'state':AIState.AIOFF},
            ItemRoles.RECOGNITION_PROTECTION: 0,
            ItemRoles.RISK_IDENTIFICATION: 0,
        }
        logger.debug(f"convert_data model = {type(model)}")
        if isinstance(model,CameraModel):
            # Update AI states based on available features
            logger.debug(f"model.data.aiFlowDTOList = {model.data.aiFlowIds}")
            if model.data.aiFlowIds:
                for ai_flow_id in model.data.aiFlowIds:
                    aiflow = aiflow_model_manager.get_aiflow_model(id = ai_flow_id)
                    if aiflow is not None:
                        logger.debug(f"aiflow = {type(aiflow)}")
                        from src.common.model.aiflows_model import AiFlow
                        aiflow: AiFlow = aiflow.data
                        logger.debug(f"aiflow.id = {aiflow.id} - aiflow.is_apply() = {aiflow.is_apply()}")
                        if aiflow.is_recognition():
                            data.update({ItemRoles.RECOGNITION: {"id": aiflow.id, "state": AIState.AION if aiflow.is_apply() else AIState.AIOFF}})
                        if aiflow.is_protection():
                            data.update({ItemRoles.PROTECTION: {"id": aiflow.id, "state": AIState.AION if aiflow.is_apply() else AIState.AIOFF}})
                        if aiflow.is_frequency():
                            data.update({ItemRoles.FREQUENCY: {"id": aiflow.id, "state": AIState.AION if aiflow.is_apply() else AIState.AIOFF}})
                        if aiflow.is_access():
                            data.update({ItemRoles.ACCESS: {"id": aiflow.id, "state": AIState.AION if aiflow.is_apply() else AIState.AIOFF}})
                        if aiflow.is_motion():
                            data.update({ItemRoles.MOTION: {"id": aiflow.id, "state": AIState.AION if aiflow.is_apply() else AIState.AIOFF}})
                        if aiflow.is_traffic():
                            data.update({ItemRoles.TRAFFIC: {"id": aiflow.id, "state": AIState.AION if aiflow.is_apply() else AIState.AIOFF}})
                        if aiflow.is_weapon():
                            data.update({ItemRoles.WEAPON: {"id": aiflow.id, "state": AIState.AION if aiflow.is_apply() else AIState.AIOFF}})
                        if aiflow.is_ufo():
                            data.update({ItemRoles.UFO: {"id": aiflow.id, "state": AIState.AION if aiflow.is_apply() else AIState.AIOFF}})
            logger.debug(f"Threat detection count = {model.data.threatDetectionCount}")
            logger.debug(f"Protection recognition count = {model.data.protectionRecognitionCount}")
            # Update camera metadata
            data.update({
                ItemRoles.ID: model.data.id,
                ItemRoles.TYPE: ModelType.Camera,
                ItemRoles.NAME: model.data.name,
                ItemRoles.BRANCH: model.data.cameraBranch,
                ItemRoles.MODEL: model.data.cameraModel,
                ItemRoles.IPADDRESS: model.data.ipAddress,
                ItemRoles.MACADDRESS: model.data.ipAddress,
                ItemRoles.PARTNER: model.data.ipAddress,
                ItemRoles.GROUP: model.data.ipAddress,
                ItemRoles.CHILD: child,
                ItemRoles.RECOGNITION_PROTECTION: model.data.protectionRecognitionCount,
                ItemRoles.RISK_IDENTIFICATION: model.data.threatDetectionCount,
            })
            
            return data
        elif isinstance(model,DoorModel):
            data.update({
                ItemRoles.ID:  model.data.id,
                ItemRoles.TYPE:  ModelType.Door,
                ItemRoles.NAME: model.data.name,
                ItemRoles.IPADDRESS: model.data.ipAddress,
                ItemRoles.MACADDRESS: None,
                ItemRoles.PARTNER: None,
                ItemRoles.GROUP: None,
                ItemRoles.CHILD: None,
            })
            return data
        elif isinstance(model,GroupModel):
            # Helper function to update AI state based on count
            def update_ai_state(count, role):
                state = AIState.AION if count is not None and count > 0 else AIState.AIOFF
                data.update({role: {"id": None, "state": state}})

            # Update AI states based on counts
            update_ai_state(model.data.recognitionCount, ItemRoles.RECOGNITION)
            update_ai_state(model.data.protectionCount, ItemRoles.PROTECTION)
            update_ai_state(model.data.frequencyCount, ItemRoles.FREQUENCY)
            update_ai_state(model.data.accessCount, ItemRoles.ACCESS)
            update_ai_state(model.data.motionCount, ItemRoles.MOTION)
            update_ai_state(model.data.trafficCount, ItemRoles.TRAFFIC)
            update_ai_state(model.data.weaponCount, ItemRoles.WEAPON)
            update_ai_state(model.data.ufoCount, ItemRoles.UFO)

            # Update model metadata
            data.update({
                ItemRoles.ID: model.data.id,
                ItemRoles.TYPE: ModelType.AIBox,
                ItemRoles.NAME: model.data.name,
                ItemRoles.IPADDRESS: model.data.ip,
                ItemRoles.MACADDRESS: model.data.mac,
                ItemRoles.PARTNER: model.data.partnerId,
                ItemRoles.GROUP: model.data.ip,
                ItemRoles.CHILD: child,
                ItemRoles.RECOGNITION_PROTECTION: model.data.protectionRecognitionCount,
                ItemRoles.RISK_IDENTIFICATION: model.data.threatDetectionCount,
            })
            return data

    def create_table(self):
        logger.debug(f"Creating table, device_data_filtered count: {len(self.controller.device_data_filtered)}")
        if self.page_indicator is not None:
            min_index = (self.page_indicator.current_page - 1) * self.page_indicator.rows_per_page
            max_index = min_index + self.page_indicator.rows_per_page
            logger.debug(f"Page range: {min_index} to {max_index}")
            for idx,model in enumerate(self.controller.device_data_filtered):
                if idx >= min_index and idx < max_index:
                    if isinstance(model,CameraModel):
                        data = self.convert_data(model,None)
                        logger.debug(f"Adding camera model: {model.data.name}")
                        self.device_controller.add_after_index(idx = len(self.device_controller.listmodel._data),model = model,type = ModelType.Camera,child = None,data = data)
                    if isinstance(model,DoorModel):
                        data = self.convert_data(model,None)
                        logger.debug(f"Adding door model: {model.data.name}")
                        self.device_controller.add_after_index(idx = len(self.device_controller.listmodel._data),model = model,type = ModelType.Door,child = None,data = data)
                    elif isinstance(model,GroupModel):
                        camera_list_model = None
                        logger.debug(f"Processing group model: {model.data.name}")
                        if model.data.cameraIds is not None and len(model.data.cameraIds) > 0:
                            camera_list_model = ListModel()
                            for id in model.data.cameraIds:
                                camera_model:CameraModel = camera_model_manager.get_camera_model(id = id)
                                if camera_model is not None:
                                    data = self.convert_data(camera_model,None)
                                    camera_list_model._add_item(model = camera_model, type=ModelType.Camera, data = data)
                            data = self.convert_data(model,camera_list_model)
                            self.device_controller.add_after_index(idx = len(self.device_controller.listmodel._data),model = model,type = ModelType.AIBox,child = camera_list_model,data = data)  
                        else:
                            data = self.convert_data(model,None)
                            self.device_controller.add_after_index(idx = len(self.device_controller.listmodel._data),model = model,type = ModelType.AIBox,data = data,child = ListModel())
                if idx > max_index:
                    break
        logger.debug(f"Table creation complete, listmodel data count: {len(self.device_controller.listmodel._data)}")
    
    def update_table(self):
        if self.page_indicator is not None:
            self.page_indicator.set_total_rows_and_total_pages(len(self.controller.device_data_filtered))
        self.device_controller.clear()
        # self.quick_widget.setSource(QUrl.fromLocalFile(os.path.abspath(self.qml_file_path)))
        self.quick_widget.setSource(QUrl("qrc:src/common/qml/device_table/DeviceTable.qml"))
        self.table = self.create_table()

    def device_item_expanded_signal(self,data):
        if self.table is not None:
            for item in self.table.layout().count():
                logger.debug(f'item = {item}')

    def delete_camera_model(self, camera_list:List[CameraModel] = []):
        self.update_data_to_table(is_thread=False)
        self.update_table()

    def add_camera_signal(self, camera:CameraModel = None):
        self.update_data_to_table(is_thread=False)
        self.update_table()

    def add_cameras_signal(self, camera_list:List[CameraModel] = None):
        self.update_data_to_table(is_thread=False)
        self.update_table()

    def retranslateUi_camera_table(self):
        if self.totalPapes is not None:
            self.totalPapes.setText(QCoreApplication.translate("DeviceTable", u"Total page: ", None))
        if self.firsePageButton is not None:
            self.firsePageButton.setToolTipsCustom(QCoreApplication.translate("DeviceTable", u"Go to first page", None))
        if self.prevButton is not None:
            self.prevButton.setToolTipsCustom(QCoreApplication.translate("DeviceTable", u"Previous page", None))
        if self.nextButton is not None:
            self.nextButton.setToolTipsCustom(QCoreApplication.translate("DeviceTable", u"Next page", None))
        if self.finalPageButton is not None:
            self.finalPageButton.setToolTipsCustom(QCoreApplication.translate("DeviceTable", u"Go to last page", None))
        # if self.display_page_number is not None:
        #     self.display_page_number.setText(QCoreApplication.translate("DeviceTable", u"Show record/page", None))
    def set_dynamic_stylesheet(self):
        self.widget_indicator.setStyleSheet(
            f"""
                QWidget {{
                background-color: {main_controller.get_theme_attribute("Color", "table_page_indicator_background")};
                color: {Style.PrimaryColor.text_unselected};
            }}
            """)
        
        self.page_indicator.set_dynamic_stylesheet()

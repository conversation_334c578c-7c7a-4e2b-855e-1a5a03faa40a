import logging
import datetime
from PySide6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QHBoxLayout, QLabel, QListView, QStyledItemDelegate, QSizePolicy, QPushButton, QDialog, QCheckBox,QApplication
from PySide6.QtGui import QStandardItemModel, QStandardItem, QPainter, QIcon, QColor, QPaintEvent, QPixmap,QDrag,QCursor
from PySide6.QtCore import Qt, QModelIndex, QCoreApplication, QSize, QDateTime, Signal,QThreadPool,QMimeData
from PySide6.QtWidgets import QStyleOptionViewItem, QStyle
from pydantic import Json
from src.common.widget.custom_calendar import CalendarPickerWidget
from src.common.widget.image_widget import ImageWidget
from src.common.widget.search_widget.search_bar import SearchBar
from src.common.model.event_data_model import <PERSON><PERSON><PERSON>, EventData, event_manager
# from src.common.widget.dialog_ai_event_widget import EventDialog
from src.common.slideshow.event_dialog import EventDialog
from src.styles.style import Style
from src.common.controller.main_controller import main_controller,connect_slot
from src.common.controller.controller_manager import controller_manager
from concurrent.futures import ThreadPoolExecutor
from src.common.widget.event.list_button import ListButton, CustomButtonEventType
from src.common.widget.event.event_combobox import EventComboBox
from src.common.widget.event.calendar_combobox import CalendarComboBox
from src.common.widget.dialogs.time_filter_event_dialog import TimeFilterEventDialog
from src.common.widget.dialogs.filter_event_dialog import FilterEventDialog
from src.utils.config import Config
from src.common.widget.event.filter_mode import FilterMode
from typing import List
import pickle
from src.common.model.group_model import group_model_manager
from src.common.model.camera_model import camera_model_manager
from src.common.widget.menus.custom_menus import CustomMenuForEventRightClick
from src.common.model.tab_model import tab_model_manager,ItemType
from src.utils.theme_setting import theme_setting
from src.utils.utils import Utils
from src.common.slideshow.slideShow import SlideShow
from src.common.model.camera_model import Camera, CameraModel, camera_model_manager
from src.presentation.device_management_screen.widget.list_custom_widgets import SearchComboBox
logger = logging.getLogger(__name__)

class CustomStandardItemModel(QStandardItemModel):
    def mimeData(self, indexes):
        logger.debug(f'indexes = {indexes}')
        if len(indexes) == 1:
            mime_data = super(CustomStandardItemModel, self).mimeData(indexes)
            if indexes:
                mime_data.setText(indexes[0].data())
                mime_data.setObjectName(indexes[0].data(Qt.UserRole))
        else:
            mime_data = super(CustomStandardItemModel, self).mimeData(indexes)
            text_list = []
            object_name_list = []
            text_and_user_role_dict = {}
            for index in indexes:
                if index.isValid():
                    text_data = index.data()
                    user_role_data = index.data(Qt.UserRole)
                    text_list.append(text_data)
                    object_name_list.append(user_role_data)
                    text_and_user_role_dict[text_data] = user_role_data
            mime_data.setText("\n".join(text_list))
            byte_array = pickle.dumps(text_and_user_role_dict)
            mime_data.setData("application/multidata", byte_array)
            mime_data.setObjectName("Multi_selection_item")
        return mime_data
    
class EventItemDelegate(QStyledItemDelegate):
    def paint(self, painter: QPainter, option: QStyleOptionViewItem, index: QModelIndex) -> None:
        # Set the background color for the item
        painter.save()
        if option.state & QStyle.State_Selected:
            painter.fillRect(option.rect, option.palette.highlight())
        painter.restore()
        # Call the base class paint method
        super().paint(painter, option, index)


class EventBar(QWidget):
    filter_mode = Signal(dict)
    combobox_hover_signal = Signal(tuple)
    def __init__(self, parent=None):
        super(EventBar, self).__init__(parent)
        main_controller.list_parent['EventBar'] = self
        self.list_screens = QApplication.screens()
        self.text_color = Style.PrimaryColor.white
        self.background_color = Style.PrimaryColor.background
        self.parent = parent
        self.is_search_items = False
        self.text_search = ''
        self.page_idx = 0
        self.event_list_view = None
        self.setup_ui()
        self.setup_stylesheet()
        self.previus_filter_selected = {}
        self.is_reset_event = False
        self.connect_slot()
        
        # Initial check for events - if any exist, hide the message and show the list
        if event_manager.event_list and 0 in event_manager.event_list and event_manager.event_list[0] is not None:
            if (event_manager.event_list[0].data and 
                event_manager.event_list[0].data.content and 
                len(event_manager.event_list[0].data.content) > 0):
                self.hide_not_found_message()
            else:
                self.show_not_found_message()
        else:
            self.show_not_found_message()

    def setup_ui(self):
        # title event number
        event_title_layout = QHBoxLayout()
        event_title_layout.setAlignment(Qt.AlignmentFlag.AlignLeft|Qt.AlignmentFlag.AlignCenter)
        event_title_layout.setContentsMargins(5, 0, 5, 0)
        self.list_button = ListButton()
        self.list_button.add_item(item={
            'object_name': 'alert',
            'icon_on': main_controller.get_theme_attribute('Image', 'alert_on'),
            'icon_off': main_controller.get_theme_attribute('Image', 'alert_off'),
            'title':self.tr("Realtime Events")
        })
        self.list_button.add_item({
            'object_name': 'warning',
            'icon_on': main_controller.get_theme_attribute('Image', 'lightning_on'),
            'icon_off': main_controller.get_theme_attribute('Image', 'lightning_off'),
            'title': self.tr("Warning")
        })
        self.list_button.changed.connect(self.changed_signal)
        # self.list_button.set_number(0,10)
        # self.number_event_widget = QLabel("0")
        # self.number_event_widget.setMinimumWidth(30)
        # self.number_event_widget.setAlignment(Qt.AlignCenter)
        # self.number_event_widget.setWordWrap(True)
        
        # event_title_layout.addWidget(self.number_event_widget)

        # self.event_title_widget = QLabel(self.tr("Real-time events"))
        # event_title_layout.addWidget(self.event_title_widget)
        event_title_layout.addWidget(self.list_button)
        self.server_combobox = SearchComboBox(data=["1","2"],
                                           combobox_clicked=self.server_combobox_clicked)
        # create divider
        # self.divider = QWidget()
        # self.divider.setFixedHeight(1)

        # create filter and history button icon
        self.filter_button = QPushButton()
        self.filter_button.setObjectName("filter_button")
        self.filter_button.setFixedSize(28, 28)
        self.filter_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'ic_filter')))
        self.filter_button.setIconSize(QSize(24, 24))
        self.filter_button.setToolTip(self.tr("Filter"))
        self.filter_button.mousePressEvent = lambda event: self.show_filter_dialog(event)

        self.refresh = QPushButton()
        self.refresh.setObjectName("refresh")
        self.refresh.setFixedSize(28, 28)
        self.refresh.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'refresh')))
        self.refresh.setIconSize(QSize(24, 24))
        self.refresh.setToolTip(self.tr("refresh"))
        self.refresh.clicked.connect(self.refresh_clicked)

        self.filter_and_history_button_layout = QHBoxLayout()
        self.filter_and_history_button_layout.setAlignment(
            Qt.AlignmentFlag.AlignRight)
        self.filter_and_history_button_layout.setContentsMargins(5, 0, 5, 0)
        self.filter_and_history_button_layout.setSpacing(2)
        self.filter_and_history_button_layout.addWidget(self.refresh)

        self.filter_and_history_button_layout.addWidget(self.filter_button)

        self.layout_contain_button = QHBoxLayout()
        self.layout_contain_button.setContentsMargins(0, 0, 0, 0)

        self.layout_contain_button.addLayout(self.filter_and_history_button_layout)


        self.filter_event_dialog = FilterEventDialog()

        # create search bar
        self.search_bar = SearchBar(parent=self)
        self.search_bar.search_items_signal.connect(self.search_items)
        self.event_list_view = EventListView(self)
        self.event_list_view.setObjectName("event_list_view")
        self.event_list_view.verticalScrollBar().valueChanged.connect(self.load_more_data)
        self.event_list_view.verticalScrollBar().setStyleSheet(
            f'''    
                QScrollBar:vertical {{
                    background-color: {Style.PrimaryColor.background};
                    width: 10px;
                    margin: 0px 0px 0px 0px;
                }}
                QScrollBar::handle:vertical {{
                    background-color: {Style.PrimaryColor.on_background};
                    border-radius: 5px;
                    min-height: 20px;
                }}
                QScrollBar::add-line:vertical {{
                    background: none;
                }}
                QScrollBar::sub-line:vertical {{
                    background: none;
                }}
                QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                    background: none;
                }}
                QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {{
                    width: 0px;
                    height: 0px;
                    background: none;
                }}
            '''
        )

        # remove horizontal scrollbar
        # self.event_list_view.setHorizontalScrollBarPolicy(
        #     Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        # set style for horizontal scrollbar
        self.event_list_view.horizontalScrollBar().setStyleSheet(
            f'''
            QScrollBar::horizontal {{
                background-color: {Style.PrimaryColor.background};
                height: 10px;
                margin: 0px 0px 0px 0px;
            }}
            QScrollBar::handle:horizontal {{
                background-color: {Style.PrimaryColor.on_background};
                border-radius: 5px;
                min-width: 20px;
            }}
            QScrollBar::add-line:horizontal {{
                background: none;
            }}
            QScrollBar::sub-line:horizontal {{
                background: none;
            }}
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                background: none;
            }}
            QScrollBar::left-arrow:horizontal, QScrollBar::right-arrow:horizontal {{
                width: 0px;
                height: 0px;
                background: none;
            }}
            '''
        )
        self.layout_search_and_filter = QVBoxLayout()
        self.layout_search_and_filter.setContentsMargins(0, 5, 0, 0)
        self.layout_search_and_filter.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_search_and_filter.addWidget(self.search_bar)
        # self.layout_search_and_filter.addLayout(self.layout_contain_button)
        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        # self.main_layout.addLayout(event_title_layout, 1)
        # self.main_layout.addWidget(self.divider)
        # self.main_layout.addWidget(self.server_combobox)
        self.main_layout.addLayout(self.layout_search_and_filter, 5)
        
        # Create a container for the "No results found" message with an icon
        self.not_found_container = QWidget()
        not_found_layout = QVBoxLayout(self.not_found_container)
        not_found_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Create the icon label
        self.not_found_icon = QLabel()
        self.not_found_icon.setFixedSize(48, 48)
        self.not_found_icon.setScaledContents(True)
        self.not_found_icon.setPixmap(QPixmap(main_controller.get_theme_attribute('Image', 'search_not_found')))
        
        # Create the text label
        self.not_found_label = QLabel(self.tr("No search results"))
        self.not_found_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Add the icon and text to the layout
        not_found_layout.addWidget(self.not_found_icon, 0, Qt.AlignmentFlag.AlignCenter)
        not_found_layout.addWidget(self.not_found_label, 0, Qt.AlignmentFlag.AlignCenter)
        
        # Add event list view first (will be hidden initially)
        self.main_layout.addWidget(self.event_list_view, 90)
        
        # Add the not found container to the main layout
        self.main_layout.addWidget(self.not_found_container, 90)
        
        # Hide event list view, show not found message by default
        self.event_list_view.setVisible(False)
        self.not_found_container.setVisible(True)
        
        self.background = QWidget()
        self.background.setLayout(self.main_layout)
        self.background.setObjectName("background")

        layout = QHBoxLayout()
        layout.addWidget(self.background)
        layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(layout)
        self.setObjectName('event_bar_widget')

    def connect_slot(self):
        connect_slot(
            (event_manager.add_event_list_signal,self.add_event_list_signal),
            (event_manager.add_event_signal,self.add_event_signal),
            (self.filter_mode,self.filter_mode_signal))
        
    def setup_stylesheet(self):
        style_sheet_btn = f'''
            QPushButton {{
                background-color: transparent;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {main_controller.get_theme_attribute('Color', 'hover_button')};
            }}
            QPushButton:pressed {{    
                background-color: {main_controller.get_theme_attribute('Color', 'hover_button')};
            }}
            '''
        self.filter_button.setStyleSheet(style_sheet_btn)
        self.refresh.setStyleSheet(style_sheet_btn)
        # border left to on_background color
        self.background.setStyleSheet(
            f'''
            #background {{
                background-color: transparent;
                border-left: 1px solid {main_controller.get_theme_attribute('Color','common_border')};
                border-top: 1px solid {main_controller.get_theme_attribute('Color','common_border')};
            }}
            '''
        )
        # self.divider.setStyleSheet(f"background-color: {main_controller.get_theme_attribute('Color','common_border')};")

        self.event_list_view.setStyleSheet(
            f"#event_list_view {{"
            f"background-color: {main_controller.get_theme_attribute('Color', 'main_background')}; "
            f"color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; border: none; margin-left: 5px }}"
        )
        # text_search_bar_event self.searchbar
        self.search_bar.set_dynamic_stylesheet()
        
        # Style for the "not found" label
        self.not_found_label.setStyleSheet(
            f"font-size: 14px; "
            f"color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; "
            f"padding: 20px;"
        )
    def add_event_signal(self,id):
        # if self.list_button.current_index() == 1:
        event:EventAI = event_manager.get_event(id = id)
        # start_time = self.filter_event_dialog.filter_selected[FilterMode.TimeRange]['start_time']
        # start_time_convert_datetime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        # end_time = self.filter_event_dialog.filter_selected[FilterMode.TimeRange]['end_time']
        # end_time_convert_datetime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
        # thoiGianXuatHienConverted = datetime.datetime.strptime(event.createdAtLocalDate, "%Y-%m-%d %H:%M:%S.%f")
        # start_time_convert_datetime = Utils.convert_same_time_utc(start_time_convert_datetime, thoiGianXuatHienConverted)
        # end_time_convert_datetime = Utils.convert_same_time_utc(end_time_convert_datetime, thoiGianXuatHienConverted)
        # if thoiGianXuatHienConverted >= start_time_convert_datetime and thoiGianXuatHienConverted <= end_time_convert_datetime:
        self.insert_event_ai(event)
        # Cấu hình hiển thị thông báo lên camera stream ở đây
        camera_model = camera_model_manager.get_camera_model(id = event.cameraId)
        if camera_model is not None:
            camera_model.set_warning(event_ai=event)

    def add_event_list_signal(self,data):
        filter_selected = self.filter_event_dialog.filter_selected
        if self.page_idx == 0:
            event_manager.event_list.clear()
            event_manager.event_list[self.page_idx] = event_manager.event_page_list
        else:
            event_manager.event_list[self.page_idx] = event_manager.event_page_list
        
        self.update_list_view_event(event_manager.event_list[self.page_idx])
        
        # Check if there are results after updating
        if self.event_list_view.list_view_model.rowCount() == 0:
            self.show_not_found_message()
        else:
            self.hide_not_found_message()
                            
    def update_list_view_event(self, event_data: EventData = None):
        """Update the list view with event data and handle the 'no results found' message"""
        if not Config.ENABLE_EVENT_BAR:
            return
        
        # Stop any ongoing updates
        main_controller.stop_update_ai_event = False
        
        # Clear the view if reset is requested
        if self.is_reset_event:
            # More efficient clearing of the list view
            self.event_list_view.list_view_model.clear()
            self.is_reset_event = False
        
        # Track if we have valid events to display
        has_events = False
        
        # Process event data if available
        if event_data and event_data.data and event_data.data.content:
            content_count = len(event_data.data.content)
            
            if content_count > 0:
                has_events = True
                # Batch update for better performance
                for event in event_data.data.content:
                    if main_controller.stop_update_ai_event:
                        break
                    self.event_list_view.update_ai_event_below(event)
        
        # Show/hide appropriate UI elements based on event availability
        if has_events:
            self.hide_not_found_message()
        else:
            self.show_not_found_message()

    def get_filter_params(self):
        filter_selected = self.filter_event_dialog.filter_selected
        status = None
        type = None
        if filter_selected[FilterMode.Status][0] == self.tr('All'):
            status = None
        else:
            status = []
            for item in filter_selected[FilterMode.Status]:
                pass
            # tạm thời backend chưa query trường này lên để status = None (get all status)
            status = None
        if filter_selected[FilterMode.AI][0] == self.tr('All'):
            type = None
        else:
            type = []
            for item in filter_selected[FilterMode.AI]:
                pass
            # tạm thời backend chưa query trường này lên để type = None (get all type)
            type = None
        groupCameraIds = None
        if filter_selected[FilterMode.Group][0] == self.tr('All'):
            groupCameraIds = None
        else:
            groupCameraIds = []
            for item in filter_selected[FilterMode.Group]:
                group_model = group_model_manager.get_group_model(name = item)
                if group_model is not None:
                    groupCameraIds.append(group_model.data.id)
        cameraIds = None
        if filter_selected[FilterMode.Camera][0] == self.tr('All'):
            cameraIds = None
        else:
            cameraIds = []
            for item in filter_selected[FilterMode.Camera]:
                camera_model = camera_model_manager.get_camera_model(name = item)
                if camera_model is not None:
                    cameraIds.append(camera_model.data.id)
                            
        start_date = filter_selected[FilterMode.TimeRange]['start_time']
        end_date = filter_selected[FilterMode.TimeRange]['end_time']
        return {'start_date':start_date, 'end_date': end_date,'cameraIds': cameraIds,'groupCameraIds':groupCameraIds,'type': type, 'status':status}
    
    def refresh_clicked(self):
        self.is_reset_event = True
        self.page_idx = 0
        index = self.list_button.current_index()
        result = self.get_filter_params()
        isWarningConfig = 2 if index == 0 else 1
        # main_controller.get_events(parent=self, page=self.page_idx, size=Utils.MaxEvent,
        #                           dateFrom=result['start_date'],dateTo=result['end_date'],
        #                           cameraIds=result['cameraIds'],groupCameraIds=result['groupCameraIds'], 
        #                           isWarningConfig=isWarningConfig,status=result['status'],type=result['type'])
        
        # If there's no data after refresh, ensure message is shown
        if self.event_list_view.list_view_model.rowCount() == 0:
            self.show_not_found_message()

    def filter_mode_signal(self,data):
        self.is_reset_event = True
        self.page_idx = 0
        if self.previus_filter_selected != data:
            self.previus_filter_selected = data
            logger.debug(f"filter_mode_signal = {data}")
            index = self.list_button.current_index()
            # filter_selected = self.filter_event_dialog.filter_selected
            result = self.get_filter_params()
            isWarningConfig = 2 if index == 0 else 1
            main_controller.get_events(parent=self, page=self.page_idx, size=Utils.MaxEvent,dateFrom = result['start_date'],dateTo = result['end_date'],cameraIds=result['cameraIds'],groupCameraIds=result['groupCameraIds'], isWarningConfig=isWarningConfig,status=result['status'],type=result['type'])
    def server_combobox_clicked(self, index):
        pass
    def changed_signal(self,index):
        self.is_reset_event = True
        self.page_idx = 0
        result = self.get_filter_params()
        if index == 0:
            main_controller.get_events(parent=self, page=self.page_idx, size=Utils.MaxEvent,dateFrom = result['start_date'],dateTo = result['end_date'],cameraIds=result['cameraIds'],groupCameraIds=result['groupCameraIds'], isWarningConfig=2,status=result['status'],type=result['type'])

        elif index == 1:
            main_controller.get_events(parent=self, page=self.page_idx, size=Utils.MaxEvent,dateFrom = result['start_date'],dateTo = result['end_date'],cameraIds=result['cameraIds'],groupCameraIds=result['groupCameraIds'], isWarningConfig=1,status=result['status'],type=result['type'])
            
    def create_event_data(self):
        self.is_reset_event = True
        start_date = datetime.date.today().strftime("%Y-%m-%d 00:00:00")
        end_date = datetime.date.today().strftime("%Y-%m-%d 23:59:59")
        main_controller.get_events(parent=self, page=self.page_idx, size=Utils.MaxEvent,dateFrom = start_date,dateTo = end_date,isWarningConfig = 2)

    def combobox_hover(self, data):
        self.combobox_hover_signal.emit(data)

    def load_more_data(self):
        scroll_bar = self.event_list_view.verticalScrollBar()
        max_value = scroll_bar.maximum()
        current_value = scroll_bar.value()
        
        if current_value == max_value:
            # Increment page index for next batch of data
            next_page_idx = self.page_idx + 1
            
            # Check if we already have data for the next page in event_manager
            if next_page_idx in event_manager.event_list and event_manager.event_list[next_page_idx] is not None:
                self.page_idx = next_page_idx
                event_data = event_manager.event_list[self.page_idx]
                
                # If we're doing a search, filter the next page's content
                if self.text_search != '':
                    if event_data and event_data.data and event_data.data.content:
                        filtered_content = []
                        for event in event_data.data.content:
                            # Apply same search filter as in search_items
                            if (self.text_search.lower() in event.name.lower() or 
                                self.text_search.lower() in event.cameraName.lower() or
                                self.text_search.lower() in event.type.lower()):
                                filtered_content.append(event)
                        
                        # Add filtered events to the view
                        for event in filtered_content:
                            self.event_list_view.update_ai_event_below(event)
                else:
                    # If not searching, just add all events from the next page
                    if event_data and event_data.data and event_data.data.content:
                        for event in event_data.data.content:
                            self.event_list_view.update_ai_event_below(event)
            else:
                # If we don't have the next page data already, we need to fetch it with API
                # This is commented out as per your request to use only existing data
                # index = self.list_button.current_index()
                # result = self.get_filter_params()
                # isWarningConfig = 2 if index == 0 else 1
                # main_controller.get_events(...)
                pass

    def insert_event_ai(self, event: EventAI):
        # logger.debug(f"Inserting event = {event}")
        if not self.is_search_items:
            # case listview dang khong o trang thai search
            if self.event_list_view.list_view_model.rowCount() > Utils.MaxEvent:
                self.event_list_view.remove_ai_event_below()
            self.event_list_view.update_ai_event_above(event)
            # Hide the "no results" message since we've added an event
            self.hide_not_found_message()
        else:
            # case listview dang o trang thai search
            if self.text_search != '':
                if self.text_search.lower() in event.soCmt.lower():
                    self.event_list_view.remove_ai_event_below()
                    self.event_list_view.update_ai_event_above(event)
                    # Hide the "no results" message since we've added an event
                    self.hide_not_found_message()

    def search_items(self, text):
        main_controller.stop_update_ai_event = True
        self.text_search = text
        self.is_reset_event = True
        
        # Clear the current view
        for index in range(self.event_list_view.model().rowCount()):
            widget = self.event_list_view.indexWidget(
                self.event_list_view.model().index(index, 0))
            if widget is not None:
                widget.deleteLater()
        self.event_list_view.list_view_model.clear()
        
        if text != '':
            # Search in existing data without making API call
            found_events = False
            
            # Check if there are events in the event_manager
            for page_idx, event_data in event_manager.event_list.items():
                if event_data and event_data.data and event_data.data.content:
                    filtered_content = []
                    
                    # Filter events based on search text
                    for event in event_data.data.content:
                        # Search in relevant event fields (name, cameraName, etc.)
                        if (text.lower() in event.name.lower() or 
                            text.lower() in event.cameraName.lower() or
                            text.lower() in event.type.lower()):
                            filtered_content.append(event)
                            found_events = True
                    
                    # Display filtered events
                    for event in filtered_content:
                        self.event_list_view.update_ai_event_below(event)
            
            # Show/hide "no results" message
            if not found_events:
                self.show_not_found_message()
            else:
                self.hide_not_found_message()
        else:
            # If search text is empty, show all events from the first page
            if 0 in event_manager.event_list and event_manager.event_list[0] is not None:
                self.update_list_view_event(event_manager.event_list[0])
                self.hide_not_found_message()
            else:
                # If no events exist, show the not found message
                self.show_not_found_message()
        
        self.is_reset_event = False

    def update_ai_event(self, event: EventAI):
        # start_time = self.filter_event_dialog.filter_selected[FilterMode.TimeRange]['start_time']
        # start_time_convert_datetime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        # end_time = self.filter_event_dialog.filter_selected[FilterMode.TimeRange]['end_time']
        # end_time_convert_datetime = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
        # thoiGianXuatHienConverted = Utils.convert_time_format_utc(event.createdAt, "%Y-%m-%dT%H:%M:%S.%f%z")
        # start_time_convert_datetime = Utils.convert_same_time_utc(start_time_convert_datetime, thoiGianXuatHienConverted)
        # end_time_convert_datetime = Utils.convert_same_time_utc(end_time_convert_datetime, thoiGianXuatHienConverted)
        # # thoiGianXuatHienConverted = datetime.datetime.strptime(event.createdAt, "%Y-%m-%dT%H:%M:%S.%f%Z")
        # # logger.debug(f'start_time_convert_datetime = {start_time_convert_datetime,thoiGianXuatHienConverted}')
        # if thoiGianXuatHienConverted >= start_time_convert_datetime and thoiGianXuatHienConverted <= end_time_convert_datetime:
        self.insert_event_ai(event)


    def retranslateUi(self):
        self.search_bar.retranslateUi_searchbar()
        for index in range(self.event_list_view.list_view_model.rowCount()):
            item = self.event_list_view.list_view_model.item(index, 0)
            item.retransUi()
        for key, value in self.list_button.list_action.items():
            if key == 0:
                widget_button: CustomButtonEventType = value.get('widget')
                widget_button.title_label.setText(self.tr('Realtime Events'))
            elif key == 1:
                widget_button: CustomButtonEventType = value.get('widget')
                widget_button.title_label.setText(self.tr('Warning'))
        
        # Add translation for not found label
        self.not_found_label.setText(self.tr("No search results"))

    def restyle_even_bar(self):
        self.filter_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'ic_filter')))
        self.refresh.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'refresh')))
        self.filter_event_dialog.setup_dynamic_style_sheet()
        self.setup_stylesheet()
        self.search_bar.set_dynamic_stylesheet()
        for key, value in self.list_button.list_action.items():
            if key == 0:
                widget_button: CustomButtonEventType = value.get('widget')
                widget_button.restyle_button(icon_on=main_controller.get_theme_attribute('Image', 'alert_on'),
                                             icon_off=main_controller.get_theme_attribute('Image', 'alert_off'))
            else:
                widget_button: CustomButtonEventType = value.get('widget')
                widget_button.restyle_button(icon_on=main_controller.get_theme_attribute('Image', 'lightning_on'),
                                             icon_off=main_controller.get_theme_attribute('Image', 'lightning_off'))

        # Update the no results icon for theme changes
        self.not_found_icon.setPixmap(QPixmap(main_controller.get_theme_attribute('Image', 'search_not_found')))

    def show_filter_dialog(self,event):
        position = event.globalPos()
        # button_pos = self.filter_button.mapToGlobal(
        #     self.filter_button.rect().bottomLeft())
        self.filter_event_dialog.showAt(position)

    def show_not_found_message(self):
        """Show a message with icon when no search results are found"""
        # Hide the list view
        if self.event_list_view:
            self.event_list_view.setVisible(False)
        
        # Style the message
        self.not_found_label.setStyleSheet(
            f"font-size: 14px; "
            f"color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; "
            f"padding: 10px;"
        )
        
        # Show the not found container
        self.not_found_container.setVisible(True)

    def hide_not_found_message(self):
        """Hide the not found message and show the list view"""
        if self.event_list_view:
            self.event_list_view.setVisible(True)
        
        self.not_found_container.setVisible(False)

class EventListView(QListView):
    def __init__(self, parent=None):
        super(EventListView, self).__init__(parent)
        self.event_bar: EventBar = parent
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        self.setObjectName("event_list_view")
        self.setup_ui()
        self.setup_stylesheet()

    def setup_ui(self):
        # Set the custom delegate for item view
        self.setItemDelegate(EventItemDelegate())
        self.list_view_model = QStandardItemModel()
        self.setModel(self.list_view_model)
        self.setSpacing(0)
        
        # disable selection
        self.setSelectionMode(QListView.SelectionMode.NoSelection)
        # disable focus
        self.setFocusPolicy(Qt.FocusPolicy.NoFocus)

    def setup_stylesheet(self):
        pass

    def update_ai_event_above(self, event: EventAI):
        item = EventItem(event=event, thread_pool=self.thread_pool)
        
        self.list_view_model.insertRow(0, item)
        self.setIndexWidget(self.list_view_model.index(0, 0), item.main_widget)
        # if self.event_bar.main_controller.events_ai_today != None:
        #     count = self.event_bar.main_controller.events_ai_today.total_results
        #     # self.event_bar.number_event_widget.setText(f'{count}')

    def remove_ai_event_below(self):
        widget = self.indexWidget(self.list_view_model.index(
            self.list_view_model.rowCount() - 1, 0))
        if widget is not None:
            widget.deleteLater()
            self.list_view_model.removeRow(self.list_view_model.rowCount() - 1)

    def update_ai_event_below(self, event: EventAI):
        item = EventItem(event=event, thread_pool=self.thread_pool)
        self.list_view_model.appendRow(item)
        self.setIndexWidget(self.list_view_model.index(
            self.list_view_model.rowCount() - 1, 0), item.main_widget)
        # count = self.event_bar.main_controller.events_ai_today.total_results
        # self.event_bar.number_event_widget.setText(f'{count}')

class ChildEventWidget(QWidget):
    def __init__(self, parent=None, event: EventAI = None, thread_pool: ThreadPoolExecutor = None):
        super().__init__(parent)
        self.thread_pool = thread_pool
        self.text_color = Style.PrimaryColor.white
        self.background_color = Style.PrimaryColor.background
        self.event_ai = event
        self.name = event.name
        self.time = event.createdAtLocalDate
        self.ori_image_path = event.imageFullUrl
        # self.crop_image_path = event.crop_image
        self.event_type = event.type
        self.camera_name = event.cameraName
        self.warning_Config = event.warningConfig
        self.camera_location = event.cameraLocation
        self.status1 = event.status
        self.is_on_clicked = False
        # self.event_warning_type = event.warning_type
        # self.cameraCheckInApply = False
        # self.cameraCheckOutApply = False
        self.event_width = self.width()
        self.drag = None
        self.parent = parent
        self.setup_ui()
        main_controller.theme_change_signal.connect(self.set_dynamic_stylesheet)
        self.set_dynamic_stylesheet()
        self.setFixedHeight(180)

    def setup_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(5,0,5,0)
        self.setLayout(layout)

        self.main_widget = QWidget()  # Create a main widget for the item
        layout.addWidget(self.main_widget)
        self.main_layout = QVBoxLayout(self.main_widget)
        self.main_layout.setContentsMargins(0,0,0,0)
        # self.main_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        top_layout = QHBoxLayout()
        center_layout = QHBoxLayout()
        center_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        bottom_layout = QHBoxLayout()
        # margin_size = 2
        # self.main_layout.setContentsMargins(
        #     margin_size, margin_size, margin_size, margin_size)
        # self.main_layout.setSpacing(margin_size)

        # logger.debug('Time: self.time: ',self.time)

        # try:
        #     # get time from UTC
        #     logger.debug(f'setup_ui time = {self.time}')
        #     utc_datetime = datetime.datetime.fromisoformat(self.time)
        # except Exception as e:
        #     logger.error(f'Error when convert time: {e}')
        #     utc_datetime = datetime.datetime.strptime(
        #         self.time, "%Y-%m-%dT%H:%M:%S.%fZ")
        #     return
        
        # # convert to local timezone
        # local_datetime = utc_datetime.replace(
        #     tzinfo=datetime.timezone.utc).astimezone(tz=None)
        # get only hh:mm:ss
        try:
            # Handle ISO format datetime with timezone
            local_datetime = datetime.datetime.fromisoformat(self.time)
            local_timestamp = local_datetime.strftime("%H:%M")
        except ValueError:
            try:
                # Fallback to parsing without timezone
                local_datetime = datetime.datetime.strptime(self.time, "%Y-%m-%d %H:%M:%S.%f")
                local_timestamp = local_datetime.strftime("%H:%M")
            except ValueError:
                # If all parsing fails, use current time
                local_timestamp = datetime.datetime.now().strftime("%H:%M")
                logger.error(f"Failed to parse datetime: {self.time}")

        self.event_time_label = QLabel(local_timestamp)
        self.event_time_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        # self.event_time_label.setText(local_timestamp)
        # self.event_time_label.setVisible(True)
        # self.event_time_label.setGeometry(70, 20, 50, 20)
        self.image_crop = ImageWidget(event_ai=self.event_ai,
            height=150, allow_click_to_show_origin=False, thread_pool=self.thread_pool, width_image=150,is_event_bar=True)
        self.image_crop.mousePressEvent = self.mousePressEvent
        self.image_crop.mouseMoveEvent = self.mouseMoveEvent

        # self.preview_image_path = self.crop_image_path
        # self.preview_image_path = self.ori_image_path

        # self.image_crop.load_from_url(self.preview_image_path)
        
        # self.image_crop.setSizePolicy(
        #     QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        # self.image_crop.setContentsMargins(0, 0, 0, 0)

        self.event_title_and_description_layout = QVBoxLayout()
        self.event_title_and_description_layout.setContentsMargins(0, 0, 0, 0)
        self.event_title_and_description_layout.setSpacing(4)

        self.event_name_label = QLabel(self)
        self.event_name_label.setText(self.name)
        self.status = QCoreApplication.translate("EventItem", "Undefined")
        # logger.debug(f"self.event_type = {self.event_type} - {self.status1} - {self.name}")
        if self.event_type == 'VEHICLE' or self.event_type == 'CROWD':
            self.status = QCoreApplication.translate(
                "EventItem", "Appears at ")
        elif self.event_type == 'ACCESS_CONTROL' or self.event_type == 'HUMAN':
            pass
            # camera_check_in_apply = self.event_ai.is_checkin
            # camera_check_out_apply = self.event_ai.is_checkout
            # if camera_check_in_apply:
            #     self.status = QCoreApplication.translate(
            #         "EventItem", 'Check in at ')
            #     self.status_color = Style.PrimaryColor.status_checkin
            # elif camera_check_out_apply:
            #     self.status = QCoreApplication.translate(
            #         "EventItem", 'Check out at ')
            #     self.status_color = Style.PrimaryColor.status_checkout
            # else:
            #     self.status = QCoreApplication.translate(
            #         "EventItem", "Appears at ")

        self.event_description_layout = QHBoxLayout()
        self.event_description_layout.setObjectName("event_description_layout")
        self.event_description_layout.setContentsMargins(0, 0, 0, 0)
        self.event_description_layout.setSpacing(0)
        self.event_status_label = QLabel(self.status)
        self.event_status_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        # self.event_status_label warp content
        # self.event_status_label.setWordWrap(True)
        self.event_camera_name_label = QLabel(f'{self.camera_name}')
        # self.event_camera_name_label warp content
        self.event_camera_name_label.setWordWrap(True)
        self.event_description_layout.addWidget(self.event_status_label)
        self.event_description_layout.addWidget(self.event_camera_name_label)

        # alert icon
        self.alert_icon = QLabel()
        self.alert_icon.setObjectName("alert_icon")
        self.alert_icon.setFixedSize(16, 16)
        self.alert_icon.setPixmap(QPixmap(Style.PrimaryImage.alert_event))
        self.ai_widget = QLabel()
        self.ai_widget.setPixmap(self.create_ai_label())

        left_top_layout = QHBoxLayout()
        left_top_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        if self.ai_widget is not None:
            left_top_layout.addWidget(self.ai_widget)
            left_top_layout.addWidget(self.event_name_label)
        else:
            left_top_layout.addWidget(self.event_name_label)
        # top_layout.addWidget(self.event_name_label)
        top_layout.addLayout(left_top_layout)
        top_layout.addWidget(QWidget())
        top_layout.addWidget(self.event_time_label)
        # widget = QWidget()
        # widget.setLayout(top_layout)
        center_layout.addWidget(self.image_crop)
        bottom_layout.addWidget(self.event_camera_name_label)
        bottom_layout.addWidget(QWidget())
        bottom_layout.addWidget(self.event_status_label)
        self.main_layout.addLayout(top_layout,10)
        self.main_layout.addLayout(center_layout,90)
        self.main_layout.addLayout(bottom_layout,10)

        # create listen click to item
        #self.mousePressEvent = self.on_item_click
        # set event self.image_crop
        # self.image_crop.mousePressEvent = self.on_item_click

    def mousePressEvent(self, event):
        # <PySide6.QtGui.QMouseEvent(MouseButtonPress LeftButton pos=100,25 scn=1742,61 gbl=1742,114 dev=QPointingDevice("core pointer" Mouse id=1))>
        if event.button() == Qt.LeftButton:
            # logger.debug(f'mousePressEvent = {self.event_ai}')
            # Start the drag operation
            self.drag = QDrag(self)
            mime_data = QMimeData()
            mime_data.setObjectName("event")
            mime_data.setText(str(self.event_ai.id))
            # mime_data.setData("application/position", data_bytes)
            self.drag.setMimeData(mime_data)
            # show dialog
            if main_controller.event_selected != self:
                try:
                    main_controller.event_selected.unclicked()
                except Exception as e:
                    logger.debug(f'on_item_click error: {e}')
                self.clicked()
                main_controller.event_selected = self
        elif event.button() == Qt.RightButton:
            mouse_position = QCursor.pos()
            self.showMenu(mouse_position)

    def mouseMoveEvent(self, event):
        if self.drag is not None:
            # Execute the drag operation
            self.drag.exec()

    def showMenu(self, position):
        self.main_menu = CustomMenuForEventRightClick(event_ai=self.event_ai)
        self.main_menu.open_new_tab_signal.connect(self.event_open_new_tab)
        self.main_menu.open_in_tab_signal.connect(self.event_open_in_tab)

        self.main_menu.exec_(position)

    def event_open_in_tab(self, data):
        id, tab_name, row, col = data
        # logger.debug(f'data = {data}')
        event_ai = event_manager.get_event(id)
        tab_model = tab_model_manager.get_tab_model(tab_name=tab_name)
        if event_ai is not None and tab_model is not None:
            
            for index,grid_item in tab_model.data.listGridData.items():
                # logger.debug(f'data1 = {grid_item.index,grid_item.row,grid_item.col}')
                if grid_item.row == row and grid_item.col == col:
                    # logger.debug(f'data = {grid_item}')
                    grid_item.type = ItemType.Event
                    tab_model.set_model(grid_item = grid_item,model = event_ai)
                    tab_model.add_grid_item_signal.emit(grid_item.index)
                    break

    def event_open_new_tab(self, data):
        camera_screen = main_controller.list_parent['CameraScreen']
        if camera_screen is not None:
            camera_screen.event_open_new_tab(data)
        logger.debug(f"event_open_new_tab = {data,camera_screen}")

    def create_ai_label(self):
        if self.event_type == 'VEHICLE':
            vehicle_path = QPixmap(main_controller.get_theme_attribute('Image', 'vehicle_detection_on'))
            return vehicle_path.scaledToHeight(16)
        elif self.event_type == 'HUMAN':
            human_path = QPixmap(main_controller.get_theme_attribute('Image', 'face_recognition_on'))
            return human_path.scaledToHeight(16)
        elif self.event_type == 'CROWD':
            crowd_path = QPixmap(main_controller.get_theme_attribute('Image', 'crowd_detection_on'))
            return crowd_path.scaledToHeight(16)
        elif self.event_type == 'ACCESS_CONTROL':
            access_control_path = QPixmap(main_controller.get_theme_attribute('Image', 'access_control_on'))
            return access_control_path.scaledToHeight(16)
        return None
    
    def set_dynamic_stylesheet(self):
        # styleName: Caption/Medium;
        # font-family: Inter;
        # font-size: 10px;
        # font-weight: 500;
        # line-height: 14px;
        # letter-spacing: 0.01em;
        # text-align: left;
        
        time_event_ai_item = f'''
                QLabel {{
                    margin-right: 5px;
                    font-weight: 500;
                    line-height: 14px;
                    letter-spacing: 0.01em;
                    text-align: left;
                    color: {main_controller.get_theme_attribute('Color','text_camera_name')};
                }}
                '''
        self.event_time_label.setStyleSheet(time_event_ai_item)

        name_event_ai_item = f'''
                QLabel {{
                    margin-left: 5px;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 16px;
                    letter-spacing: 0em;
                    text-align: left;
                    color: {main_controller.get_theme_attribute('Color','text_camera_name')};
                }}
                '''
        self.event_name_label.setStyleSheet(time_event_ai_item)

        self.status_color = main_controller.get_theme_attribute('Color','text_camera_name')
        status_event_ai_item = f'''
                QLabel {{
                    margin-right: 5px;
                    font-size: 10px;
                    font-weight: 400;
                    line-height: 16px;
                    letter-spacing: 0em;
                    text-align: left;
                    color: {self.status_color};
                }}
                '''
        self.event_status_label.setStyleSheet(status_event_ai_item)

        camera_name_event_ai_item = f'''
                QLabel {{
                    margin-left: 5px;
                    font-size: 10px;
                    font-weight: 400;
                    line-height: 16px;
                    letter-spacing: 0em;
                    text-align: left;
                    color: {main_controller.get_theme_attribute('Color','text_camera_name')};
                }}
                '''
        self.event_camera_name_label.setStyleSheet(time_event_ai_item)
        self.background_unclicked = f"background-color: {main_controller.get_theme_attribute('Color','camera_widget_background')};"
        self.background_clicked = f"background-color: {main_controller.get_theme_attribute('Color','camera_widget_background_clicked')};"
        self.setStyleSheet(f"{self.background_clicked if self.is_on_clicked else self.background_unclicked} border-radius: 4px;")
        self.ai_widget.setPixmap(self.create_ai_label())

    def clicked(self):
        self.is_on_clicked = True
        self.setStyleSheet(f"background-color: {main_controller.get_theme_attribute('Color','camera_widget_background_clicked')};border-radius: 4px;")

    def unclicked(self):
        self.is_on_clicked = False
        self.setStyleSheet(f"background-color: {main_controller.get_theme_attribute('Color','camera_widget_background')};border-radius: 4px;")

    def on_item_click(self, event):
        # <PySide6.QtGui.QMouseEvent(MouseButtonPress LeftButton pos=100,25 scn=1742,61 gbl=1742,114 dev=QPointingDevice("core pointer" Mouse id=1))>
        if event.button() == Qt.LeftButton:
            # show dialog
            if main_controller.event_selected != self:
                try:
                    main_controller.event_selected.unclicked()
                except Exception as e:
                    logger.debug(f'on_item_click error: {e}')
                self.clicked()
                main_controller.event_selected = self

    def dragEnterEvent(self, event):
        event.acceptProposedAction()

    def retransUi(self):
        # if self.event_type == 'ANPR' or self.event_type == 'CROWD':
        #     self.status = QCoreApplication.translate(
        #         "EventItem", "Appears at ")
        # elif self.event_type == 'ACCESS_CONTROL' or self.event_type == 'HUMAN':
        #     # event_metadata_parser = json.loads(self.event.metadata)
        #     # metadata_access_control = MetadataAccessControl(
        #     #     **event_metadata_parser)
        #     camera_check_in_apply = self.event.is_checkin
        #     camera_check_out_apply = self.event.is_checkout
        #     if camera_check_in_apply:
        #         self.status = QCoreApplication.translate(
        #             "EventItem", 'Check in at ')
        #         self.status_color = Style.PrimaryColor.status_checkin
        #     elif camera_check_out_apply:
        #         self.status = QCoreApplication.translate(
        #             "EventItem", 'Check out at ')
        #         self.status_color = Style.PrimaryColor.status_checkout
        #     else:
        #         self.status = QCoreApplication.translate(
        #             "EventItem", "Appears at ")

        self.event_status_label.setText(self.status)
        self.image_crop.label_loading.setText(self.tr('Loading...'))
class EventItem(QStandardItem):
    def __init__(self, parent=None, event: EventAI = None, thread_pool: ThreadPoolExecutor = None):
        super(EventItem, self).__init__(parent)
        # self.thread_pool = thread_pool
        # self.text_color = Style.PrimaryColor.text
        # self.background_color = Style.PrimaryColor.background
        # self.event = event
        # self.name = event.soCmt
        # self.time = event.thoiGianXuatHien
        # self.ori_image_path = event.image
        # self.crop_image_path = event.crop_image
        # self.event_type = event.event_type
        # self.camera_name = event.camera_model.camera_name
        # self.cameraCheckInApply = False
        # self.cameraCheckOutApply = False
        # self.parent = parent
        # self.setup_ui()
        # self.setup_stylesheet()
        self.main_widget = ChildEventWidget(event = event,thread_pool = thread_pool)
        self.setSizeHint(QSize(-1,200))
        # self.setData(self.main_widget, Qt.UserRole)
    def retransUi(self):
        self.main_widget.retransUi()
   

    # def on_item_click(self, event):
    #     # <PySide6.QtGui.QMouseEvent(MouseButtonPress LeftButton pos=100,25 scn=1742,61 gbl=1742,114 dev=QPointingDevice("core pointer" Mouse id=1))>
    #     if event.button() == Qt.LeftButton:
    #         # show dialog
    #         self.dialog = EventDialog(event=self.event)
    #         self.dialog.exec()

    # def retransUi(self):
    #     if self.event_type == 'ANPR' or self.event_type == 'CROWD':
    #         self.status = QCoreApplication.translate(
    #             "EventItem", "Appears at ")
    #     elif self.event_type == 'ACCESS_CONTROL' or self.event_type == 'HUMAN':
    #         # event_metadata_parser = json.loads(self.event.metadata)
    #         # metadata_access_control = MetadataAccessControl(
    #         #     **event_metadata_parser)
    #         camera_check_in_apply = self.event.is_checkin
    #         camera_check_out_apply = self.event.is_checkout
    #         if camera_check_in_apply:
    #             self.status = QCoreApplication.translate(
    #                 "EventItem", 'Check in at ')
    #             self.status_color = Style.PrimaryColor.status_checkin
    #         elif camera_check_out_apply:
    #             self.status = QCoreApplication.translate(
    #                 "EventItem", 'Check out at ')
    #             self.status_color = Style.PrimaryColor.status_checkout
    #         else:
    #             self.status = QCoreApplication.translate(
    #                 "EventItem", "Appears at ")

    #     self.event_status_label.setText(self.status)


# if __name__ == "__main__":
#     import sys
#     from PySide6.QtWidgets import QApplication

#     app = QApplication(sys.argv)
#     window = EventBar()
#     window.show()
#     sys.exit(app.exec_())


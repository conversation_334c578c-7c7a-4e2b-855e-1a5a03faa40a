import QtQuick
import QtQuick.Controls.Material 
import QtQuick.Layouts

Rectangle{
    id: root
    property bool isPlayingStream: false
    
    property bool isOpenTabActive: false
    property var cameraRtsp: ""
    property var cameraName: ""
    signal closeSignal()
    signal changeTabWidgetOpenSignal(bool open)
    
    width: 512
    height: 340

    color: "#5B5B9F"
    border.color: "white"
    border.width: 2
    radius: 5

    Timer {
        id: updateTimer
        interval: 1000
        running: true
        repeat: true
        onTriggered: {
            let date = new Date();
            timeText.text = (date.getMonth() + 1) + "/" + date.getDate() + "/" + date.getFullYear() +
                            " " + date.getHours().toString().padStart(2, '0') + ":" +
                            date.getMinutes().toString().padStart(2, '0') + ":" +
                            date.getSeconds().toString().padStart(2, '0');
        }
    }

    ColumnLayout{
        anchors.fill: parent
        spacing: 0
        RowLayout{
            spacing: 0
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredHeight: 50
            Item{
                width: 5
                height: 50
            }
            Image{
                source: "qrc:/src/assets/map/camera_stream_icon.svg"
                width: 35
                height: 35
            }
            Text{
                text: cameraName
                color: "white"
                elide: Text.ElideRight
                wrapMode: Text.NoWrap
                clip: true
                font.bold: true
                font.pixelSize: 12
                Layout.preferredWidth: 120
                Layout.leftMargin: 5
            }
            Text {
                id: timeText
                text: "Loading..."
                color: "white"
                font.pixelSize: 12
                Layout.preferredWidth: 150
                Layout.leftMargin: 15
            }
            Button{
                icon.source: "qrc:/src/assets/map/expand_stream.svg"
                icon.color: "transparent"
                icon.width: 25
                icon.height: 25
                Layout.fillWidth: true
                Layout.preferredWidth: 30
                Layout.leftMargin: 35
                background: Rectangle{
                    color: "transparent"
                    border.color: "transparent"
                }
            }
            Button{
                id: changeTabButton
                icon.source: "qrc:/src/assets/map/open_change_tab_widget.svg"
                icon.color: "transparent"
                icon.width: 20
                icon.height: 20
                Layout.fillWidth: true
                Layout.preferredWidth: 30
                background: Rectangle{
                    color: root.isOpenTabActive ? "#4A4579" : "transparent"
                    border.color: "transparent"
                    radius: 5
                }

                onClicked: {
                    root.isOpenTabActive = !root.isOpenTabActive
                    changeTabWidgetOpenSignal(root.isOpenTabActive)
                }
            }
            Button{
                icon.source: "qrc:/src/assets/tool_icons/close_dialog_white.svg"
                icon.color: "transparent"
                icon.width: 20
                icon.height: 20
                Layout.fillWidth: true
                Layout.preferredWidth: 30
                background: Rectangle{
                    color: "transparent"
                    border.color: "transparent"
                }
                onClicked: {
                    closeSignal()
                }
            }
        }

        CustomVideoOutput {
            width: 515
            height: 300
            Layout.fillHeight: true
            Layout.fillWidth: true
            Layout.margins: 2
            id: videoFrame
            rtspUrl: cameraRtsp ? cameraRtsp : ""
            isPlaying: isPlayingStream
        }
    }
}


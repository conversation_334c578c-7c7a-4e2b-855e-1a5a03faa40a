from src.common.widget.widget_for_custom_grid.map_grid_item_widget import MapGridWidgetItem
from src.common.widget.notifications.notify import Notifications
from src.presentation.camera_screen.stream_object_base_widget import StreamObjectBaseWidget
from src.presentation.camera_screen.camera_bottom_toolbar import CameraBottomToolbarWidget
# from src.common.model.map_model import Floor,FloorModel,floor_manager,Building,BuildingModel,building_manager,Map,MapModel,map_manager
from src.common.qml.models.map_controller import FloorModel,MapModel
import logging
from src.common.model.item_grid_model import ItemGridModel
from src.common.widget.widget_for_custom_grid.dialog_edit_grid_layouts import DialogEditGridLayouts
from src.common.model.main_tree_view_model import TreeType
import math
import threading
import time
from PySide6.QtCore import Qt, QPoint, QItemSelection, QItemSelectionModel, QTimer, QSize
from PySide6.QtGui import <PERSON>GuiApplication, QIcon, QResizeEvent,QShortcut,Q<PERSON>eySequence
from PySide6.QtWidgets import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QWidget, QStackedWidget, QVBoxLayout, QLabel, QStyle, QSizePolicy, QHBoxLayout
from src.common.widget.stack_camera_frame import StackFrame
from src.common.model.camera_model import Camera,CameraModel,camera_model_manager
from src.common.controller.main_controller import main_controller
from src.common.widget.camera_widget import CameraWidget,StreamCameraType
from src.styles.style import Style
from PySide6.QtWidgets import QApplication
from src.common.widget.button_state import ButtonState, GridButtonModel
from typing import List
from src.utils.config import Config
from src.common.model.device_models import TabType
from src.common.camera.grid_item_selected import grid_item_selected
from src.common.key_board.key_board_manager import key_board_manager
from src.common.model.tab_model import TabModel, GridItem,ItemType, SignalType
from src.presentation.camera_screen.map.map_widget_item import Map2DWidgetItem
from src.common.widget.event.event_widget import EventWidget
from src.common.model.event_data_model import EventAI
from src.common.controller.controller_manager import Controller, controller_manager
from src.common.camera.video_capture import video_capture_controller
import uuid
logger = logging.getLogger(__name__)
class CameraGridWidget(QWidget):
    def __init__(self, parent=None, tab_model:TabModel = None):
        super().__init__(parent)
        self.current_grid = None
        self.grid = None
        self.controller = None
        self.current_grid_model = GridButtonModel.StandardGrid.DIVISIONS_1
        self.current_grid_type = self.current_grid_model.grid_type
        self.widget_contain_stack = None
        self.is_ui_grid = False
        self.tab_index = -1
        self.screen_available_width = None
        self.screen_available_height = None
        self.original_width = None
        self.original_height = None
        self.list_custom_grid_model = None
        self.resize_timer = None
        self.calculate_layout()
        self.custom_tab_widget = main_controller.list_parent['CustomTabWidget']
        self.main_treeview_widget = main_controller.list_parent['MainTreeView']
        self.camera_full_screen = None
        self.camera_screen = parent
        self.temp = False
        # create layout for camera QWidget
        self.camera_layout = QVBoxLayout()
        self.camera_grid_layout = QVBoxLayout()
        self.camera_grid_layout.setContentsMargins(0, 0, 0, 0)
        self.camera_grid_layout.setSpacing(0)
        self.camera_grid_widget = QWidget()
        self.camera_grid_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.camera_grid_widget.setLayout(self.camera_grid_layout)
        self.root_stackedwidget = None
        self.root_stackedwidget = QStackedWidget()
        self.root_stackedwidget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.root_stackedwidget.setContentsMargins(0, 0, 0, 0)
        self.widget_contain_grid = QWidget()
        self.camera_grid_layout.addWidget(self.root_stackedwidget)
        # create dict with key is camera id and value is camera widget
        self.grid_number = 0
        self.camera_bottom_toolbar: CameraBottomToolbarWidget = parent.camera_bottom_toolbar
        self.camera_bottom_toolbar.stream_flow.action_signal.connect(self.switch_video_stream_from_button)
        self.camera_bottom_toolbar.signal_update_grid.connect(self.on_drop_down_change_grid)
        self.camera_bottom_toolbar.signal_reload_grid.connect(self.on_reload_grid_after_custom)

        self.camera_layout.setContentsMargins(0, 0, 0, 0)
        self.camera_layout.setSpacing(0)
        self.camera_layout.addWidget(self.camera_grid_widget)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.setLayout(self.camera_layout)
        self.screen_index = QLabel(self)
        self.screen_index.setStyleSheet(Style.StyleSheet.label_style2)
        self.screen_index.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.screen_index.setVisible(False)
        self.screen_index.setGeometry(self.grid_layout_width/2 - 100, 0, 100, 100)
        self.camera_thread = None
        self.is_app_closed = False
        # update list camera from server

        self.shortcut = QShortcut(QKeySequence(Qt.CTRL | Qt.Key_S), self)
        self.shortcut.activated.connect(self.shortcut_activated)
        self.tab_model = tab_model
        if self.tab_model is None:
            pass
        else:
            self.tab_model:TabModel = tab_model
            self.tab_model.data.isShow = True
            self.tab_model.register_signal(self)
            self.update_list_camera()

    def switch_tab_model(self,tab_model: TabModel):
        tab_model.data.index = self.tab_model.data.index
        tab_model.data.isShow = True
        self.tab_model.unregister_signal(self)
        self.tab_model = tab_model
        self.tab_model.register_signal(self)
    # kich ban Shortcut ID
    def create_screen_index_widget(self):
        widget = QWidget(self)
        layout = QHBoxLayout()
        # margin left, right 8px
        layout.setContentsMargins(8, 0, 8, 0)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.screen_index = QLabel()
        self.screen_index.setStyleSheet(Style.StyleSheet.label_style2)
        self.screen_index.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.screen_index)
        widget.setLayout(layout)
        return widget

    def show_screen_index(self,number):
        self.screen_index.setVisible(True)
        self.screen_index.setText(str(number))
        timer = QTimer(self)
        timer.setInterval(5000)
        timer.setSingleShot(True)
        timer.timeout.connect(self.hide_screen_index)
        timer.start()
        key_board_manager.timer_list.append(timer)

    def change_screen_index_color(self):
        self.screen_index.setStyleSheet(Style.StyleSheet.label_style4)

    def hide_screen_index(self):
        # Sau thời gian timeout là 5s thì ẩn số màn hình đi, bỏ lắng nghe key lựa chọn màn hình.
        self.screen_index.setStyleSheet(Style.StyleSheet.label_style2)
        self.screen_index.setVisible(False)
        if str(Qt.Key.Key_Alt) in key_board_manager.keys:
            key_board_manager.keys = {}

    def find_item_index(self, number):
        # sau khi đã sử dụng phím tắt đê lựa chọn được number của grid rồi thì cần focus vào item đó
        for index in range(self.grid.count()):
            if index == number:
                grid_item = self.tab_model.data.listGridData.get(index,None)
                if grid_item is not None:
                    grid_item.widget.stack_item.grid_item_clicked(main_controller.current_tab)
                    grid_item.widget.stack_item.change_grid_item_index_color()

    def show_item_index(self):
        for index in range(self.grid.count()):
            stack_item = self.grid.itemAt(index)
            item = stack_item.widget()
            grid_item = self.tab_model.data.listGridData.get(index,None)
            if grid_item is not None:
                grid_item.widget.stack_item.show_grid_item_index(index)

    def process_change_item(self,action = 4):
        # action = 4 <=> bam phim Qt.Key.Key_4
        # action = 6 <=> bam phim Qt.Key.Key_6
        key_selected = 0
        widget = grid_item_selected.data['widget']
        if widget is not None:
            for key, grid_item in self.tab_model.data.listGridData.items():
                if grid_item.widget == widget:
                    if action == 4:
                        key_selected = key - 1 if key > 0 else 0
                    elif action == 6:
                        key_selected = key + 1 if key < self.grid.count() - 1 else key
                    break
            grid_item = self.tab_model.data.listGridData.get(key_selected,None)
            if grid_item is not None:
                if grid_item.widget is not None:
                    grid_item.widget.stack_item.grid_item_clicked(main_controller.current_tab)

    def process_shortcut_id(self,id = None, tree_type = None):
        widget = grid_item_selected.data['widget']
        for key, grid_item in self.tab_model.data.listGridData.items():
            if grid_item.widget == widget:
                stack_item = widget.stack_item
                width = stack_item.frame_width
                height = stack_item.frame_height
                camera_model = camera_model_manager.get_camera_model(id = id)
                if camera_model is not None:
                    grid_item = self.tab_model.data.listGridData.get(key,None)
                    if grid_item.model == camera_model:
                        # case kéo một event trùng nhau
                        return
                    if  grid_item is not None:
                        grid_item.type = ItemType.Camera
                        grid_item.row = stack_item.position.x()
                        grid_item.col = stack_item.position.y()
                        grid_item.width = stack_item.frame_width
                        grid_item.height = stack_item.frame_height
                        self.tab_model.set_model(grid_item = grid_item,model = camera_model)
                    self.tab_model.add_grid_item_signal.emit(key)
                    break

    def calculate_layout(self, desktop_screen_size = None):
        if desktop_screen_size is None:
            screen = QGuiApplication.primaryScreen()
            desktop_screen_size = screen.availableGeometry()
        self.screen_available_width = desktop_screen_size.width()
        self.screen_available_height = desktop_screen_size.height()
        menubar = self.screen_available_width * 0.0165
        margin = 40
        tab_bar = self.screen_available_height * 0.043
        height_of_button_toolbar = self.screen_available_height * 0.037
        percent_width_of_left_side_layout = 0.18
        percent_width_of_right_side_layout = 0.1 if Config.ENABLE_EVENT_BAR else 0.145
        percent_menu_bar = 0.025
        # get window title height
        window_title = QApplication.style().pixelMetric(QStyle.PM_TitleBarHeight)
        self.width_left_side_layout = 0
        self.width_right_side_layout = 0

        self.width_left_side_layout = self.screen_available_width * percent_width_of_left_side_layout
        if self.width_left_side_layout < self.screen_available_width * percent_width_of_right_side_layout:
            self.width_left_side_layout = self.screen_available_width * percent_width_of_right_side_layout

        if Config.ENABLE_EVENT_BAR:
            self.width_right_side_layout = self.screen_available_width * percent_width_of_right_side_layout
            if self.width_right_side_layout < self.screen_available_width * percent_width_of_right_side_layout:
                self.width_right_side_layout = self.screen_available_width * percent_width_of_right_side_layout
        self.width_center_layout = self.screen_available_width * ((1 - percent_width_of_left_side_layout - percent_width_of_right_side_layout - percent_menu_bar) if Config.ENABLE_EVENT_BAR else (1 - percent_width_of_left_side_layout - percent_menu_bar))
        # set size of this widget
        # 9, 16 is ratio on design figma
        self.grid_layout_width = self.width_center_layout
        self.grid_layout_height = self.width_center_layout * 9 / 16
        temp_grid_layout_height = self.screen_available_height - height_of_button_toolbar - margin - tab_bar - window_title - 10
        self.height_available_value = temp_grid_layout_height

        if self.grid_layout_height > temp_grid_layout_height:
            self.grid_layout_height = temp_grid_layout_height
            self.grid_layout_width = self.grid_layout_height * 16 / 9

    def shortcut_activated(self):    
        if self.tab_model.data.type == TabType.Invalid:
            self.custom_tab_widget.tab_widget.tab_bar.setTabText(self.tab_index, self.tab_model.data.name)
            item = self.main_treeview_widget.get_item(name = 'List Saved View',tree_type = TreeType.List_Saved_View)
            if item is not None:
                root_item = self.main_treeview_widget.add_item(item=item, name=self.tab_model.data.name, tree_type=TreeType.Saved_View_Item)
                root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
                self.main_treeview_widget.add_data(name=self.tab_model.data.name,tab_type=TreeType.Saved_View_Item,server=self.main_treeview_widget.server)
                self.tab_model.data.type = TabType.SavedView
                self.tab_model.data.type = TabType.SavedView
                self.tab_model.to_qsetting()

    def switch_video_stream_from_button(self, data):
        index, ai_type, camera_id = data
        current_widget = self.camera_screen.center_stacked_widget.currentWidget()
        current_widget: CameraGridWidget
        camera_widget = None
        virtual_camera_widget = None
        current_tab = main_controller.current_tab
        if grid_item_selected.is_tab_index(current_tab) and grid_item_selected.data['tab_index'] is not None:
            camera_widget = grid_item_selected.data['widget']
            if camera_widget is not None:
                for key, grid_item in current_widget.tab_model.data.listGridData.items():
                    if camera_widget == grid_item.widget:
                        virtual_camera_widget = grid_item.virtual_widget
        if camera_widget is None:
            return
        if index == ButtonState.StreamFlowType.MAIN_STREAM.value:
            # # xu ly update stream voi tat ca camera dang stream tren GridView
            if camera_widget.video_capture.stream_type != StreamCameraType.main_stream:
                # Tạm thời comment chờ có API substream xử lý sau
                camera_widget.switch_video_capture(StreamCameraType.main_stream)

        elif index == ButtonState.StreamFlowType.SUB_STREAM.value:
            # xu ly update stream voi 1 camera duoc chon
            if camera_widget.video_capture.stream_type != StreamCameraType.sub_stream:
                # Tạm thời comment chờ có API substream xử lý sau
                camera_widget.switch_video_capture(StreamCameraType.sub_stream)
        else:
            camera_model = camera_model_manager.get_camera_model(id=camera_id)
            if ai_type in camera_model.data.features:
                stream_type = StreamCameraType.get_stream_type(ai_type)
                if camera_widget.video_capture.stream_type != stream_type:
                    camera_widget.switch_video_capture(stream_type)
                    return

            Notifications(parent=main_controller.list_parent['CameraScreen'],
                            title=self.tr('Server Offline'), icon=Style.PrimaryImage.info_result)

    # def switch_video_stream_from_treeview(self, data):
    #     index, ai_flow_id, camera_id = data
    #     current_widget = self.camera_screen.center_stacked_widget.currentWidget()
    #     current_widget: CameraGridWidget
    #     list_camera_widget = []
    #     list_grid_item = []
    #     current_tab = main_controller.current_tab
    #     # if data_emitted is not None:
    #     camera_model = camera_model_manager.get_camera_model(id=camera_id)
    #     controller = controller_manager.get_controller(server_ip=camera_model.data.server_ip)
    #     for key, grid_item in current_widget.tab_model.data.listGridData.items():
    #         if grid_item.widget is not None and isinstance(grid_item.widget, CameraWidget):
    #             if grid_item.model.data.id == camera_id:
    #                 list_grid_item.append(grid_item)
    #                 list_camera_widget.append(grid_item.widget)
    #     if len(list_grid_item) == 0:
    #         return
    #     for widget in list_camera_widget:
    #         if grid_item_selected.is_tab_index(current_tab) and grid_item_selected.data['tab_index'] is not None and \
    #                 grid_item_selected.data['widget'] == widget:
    #             self.camera_bottom_toolbar.stream_flow.setIcon(
    #                 QIcon(self.camera_bottom_toolbar.stream_flow.list_menu_icon[index]))
    #             break

    #     if index == ButtonState.StreamFlowType.MAIN_STREAM.value:
    #         # # xu ly update stream voi tat ca camera dang stream tren GridView
    #         for grid in list_grid_item:
    #             camera_widget = grid.widget
    #             if camera_widget.camera_model.data.urlMainstream is not None and camera_widget.video_capture.stream_type != StreamCameraType.main_stream:
    #                 camera_widget.switch_video_capture(StreamCameraType.main_stream)
    #                 if grid.virtual_widget is not None:
    #                     grid.virtual_widget.switch_video_capture(StreamCameraType.main_stream)

    #     elif index == ButtonState.StreamFlowType.SUB_STREAM.value:
    #         # xu ly update stream voi 1 camera duoc chon
    #         for grid in list_grid_item:
    #             camera_widget = grid.widget
    #             if camera_widget.video_capture.stream_type != StreamCameraType.sub_stream:
    #                 # Tạm thời comment chờ có API substream xử lý sau
    #                 camera_widget.switch_video_capture(StreamCameraType.sub_stream)
    #                 if grid.virtual_widget is not None:
    #                     grid.virtual_widget.switch_video_capture(StreamCameraType.sub_stream)
    #     else:
    #         # data_merged is aiflow_id
    #         list_aiflow = controller.get_aiflows(id=ai_flow_id)
    #         if len(list_aiflow) == 0:
    #             return
    #         aiflow: AiFlow = list_aiflow[0]
    #         # get main
    #         if aiflow.restreamPort is not None and aiflow.restreamEndpoint is not None:
    #             data = "rtsp://" + controller.api_client.server_ip + ':' + str(
    #                 aiflow.restreamPort) + aiflow.restreamEndpoint
    #             for grid in list_grid_item:
    #                 camera_widget = grid.widget
    #                 if camera_widget.video_capture.stream_type != StreamCameraType.ai_stream and aiflow.state == "PROCESSING":
    #                     # case thay đổi từ các luống khác sang restream
    #                     if data is not None:
    #                         camera_widget.camera_model.data.urlRestream = data
    #                         camera_widget.switch_video_capture(StreamCameraType.ai_stream)
    #                         if grid.virtual_widget is not None:
    #                             grid.virtual_widget.switch_video_capture(StreamCameraType.ai_stream)
    #                 elif camera_widget.video_capture.stream_type == StreamCameraType.ai_stream and camera_widget.camera_model.data.urlRestream != data:
    #                     # case chỉ thay đổi URL của luồng restream
    #                     if data is not None:
    #                         camera_widget.camera_model.data.urlRestream = data
    #                         camera_widget.video_capture.stream_link = data
    #         else:
    #             Notifications(parent=main_controller.list_parent['CameraScreen'],
    #                           title=self.tr('Server Offline'), icon=Style.PrimaryImage.info_result)

    def on_drop_down_change_grid(self, data):
        index, button_feature, data_merged, current_grid_widget = data
        if current_grid_widget != self:
            return
        if data_merged == "EDIT_LAYOUTS_TRIGGERED":
            print(f"on_drop_down_change_grid EDIT_LAYOUTS_TRIGGERED")
            self.on_open_dialog_edit_layouts()
        else:
            model: ItemGridModel = data_merged
            list_data_custom_grid = self.tab_model.data.listGridCustomData
            for grid_data in list_data_custom_grid:
                if grid_data["grid_type"] == model.grid_type:
                    current_model_in_tab = ItemGridModel.from_dict(grid_data)
                    self.update_current_model(current_model_in_tab, list_data_custom_grid)
                else:
                    self.update_current_model(model, list_data_custom_grid)
        self.tab_model.change_grid_view_signal.emit(self.tab_model.data.currentGrid)
        if self.tab_model.data.type != TabType.Invalid:
            self.controller:Controller = controller_manager.get_controller(server_ip = self.tab_model.data.server_ip)
            self.tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.ChangeGridView,'data':{'grid': model.to_dict()}}
            self.controller.update_tabmodel_by_put(parent=self, tab=self.tab_model.data)
            
    def update_current_model(self, model, list_to_save):
        grid_type = model.grid_type
        self.data_merge_list = model.data
        self.current_grid_model = model
        self.current_grid_type = grid_type
        if self.camera_bottom_toolbar.current_grid_value != model:
            self.camera_bottom_toolbar.current_grid_value = model

            grid = self.camera_bottom_toolbar.current_grid_value.to_dict()
            self.tab_model.data.currentGrid = grid
            self.tab_model.data.listGridCustomData = list_to_save
            # nếu không phải tab virtual hoăc saved view thì lưu vào QSetting

    def change_grid_view_signal(self,data):
        grid = ItemGridModel.from_dict(data)
        self.change_grid_view(grid)

    def on_open_dialog_edit_layouts(self):
        self.dialog = DialogEditGridLayouts(list_divisions=self.camera_bottom_toolbar.list_custom_grid_model)
        self.dialog.signal_save_trigger.connect(self.camera_bottom_toolbar.reload_grid_menu)
        self.dialog.exec_()

    def on_reload_grid_after_custom(self, value):
        item_grid_model, list_custom_model, current_grid_widget = value
        if current_grid_widget != self:
            return
        model: ItemGridModel = item_grid_model
        self.data_merge_list = model.data
        grid_type = model.grid_type
        self.camera_bottom_toolbar.list_custom_grid_model = list_custom_model
        self.list_custom_grid_model = list_custom_model
        self.current_grid_model = model
        self.current_grid_type = grid_type
        self.camera_bottom_toolbar.current_grid_value = model
        self.change_grid_view(self.current_grid_model)

    def on_page_index_changed(self, page_id):
        pass


    def change_grid_view(self, grid_model):
        if self.camera_grid_layout.count() == 1:
            self.root_stackedwidget.close()
            self.camera_grid_layout.removeWidget(self.root_stackedwidget)
        # create new grid view
        self.root_stackedwidget = QStackedWidget()
        self.root_stackedwidget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.root_stackedwidget.setContentsMargins(0, 0, 0, 0)
        # create range by index
        # calculate grid number
        if grid_model.grid_type == ButtonState.GridType.GRID_1:
            self.camera_bottom_toolbar.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_off")))
        elif grid_model.grid_type == ButtonState.GridType.GRID_4:
            self.camera_bottom_toolbar.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_off")))
        elif grid_model.grid_type == ButtonState.GridType.GRID_9:
            self.camera_bottom_toolbar.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_2_off")))
        elif grid_model.grid_type == ButtonState.GridType.GRID_16:
            self.camera_bottom_toolbar.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_2_off")))
        elif grid_model.grid_type == ButtonState.GridType.GRID_36:
            self.camera_bottom_toolbar.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_2_off")))
        elif grid_model.grid_type == ButtonState.GridType.GRID_64:
            self.camera_bottom_toolbar.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_2_off")))
        #Custom grid
        elif grid_model.grid_type == ButtonState.GridType.GRID_6_CUSTOM:
            self.camera_bottom_toolbar.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_2_off")))
        elif grid_model.grid_type == ButtonState.GridType.GRID_8_CUSTOM:
            self.camera_bottom_toolbar.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_2_off")))
        elif grid_model.grid_type == ButtonState.GridType.GRID_10_CUSTOM:
            self.camera_bottom_toolbar.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_2_off")))
        elif grid_model.grid_type == ButtonState.GridType.GRID_13_CUSTOM:
            self.camera_bottom_toolbar.stream_flow.setIcon(QIcon(main_controller.get_theme_attribute("Image", "stream_flow_2_off")))
        grid_icon = main_controller.get_theme_attribute('Image', grid_model.grid_path)
        # Create a new QIcon with the resized pixmap
        resized_icon = QIcon(QIcon(grid_icon).pixmap(QSize(24, 24)))
        self.camera_bottom_toolbar.grid.setIcon(resized_icon)
        self.ui_gridview(grid_model)
        main_controller.current_grid_model = grid_model
        self.camera_grid_layout.addWidget(self.root_stackedwidget)
        self.root_stackedwidget.widget(0).show()

    def ui_gridview(self, grid_model: ItemGridModel):
        logger.debug(f'ui_gridview: grid_model: {grid_model}')
        self.is_ui_grid = True
        divisions = grid_model.divisions
        row_from_model = grid_model.row
        col_from_model = grid_model.column
        grid_count = grid_model.total_grid_count
        data_merge_list = grid_model.data
        grid_type = grid_model.grid_type
        divisions_type = grid_model.divisions_type
        widget_contain_grid = QWidget()
        widget_contain_grid.resizeEvent = lambda event: self.on_resize_event(widget_contain_grid, event)
        self.root_stackedwidget.addWidget(widget_contain_grid)
        self.root_stackedwidget.widget(0).hide()
        margin = 5
        max_height = self.grid_layout_height - margin
        max_width = self.grid_layout_width - margin
        item_aspect_ratio = 16 / 9
        self.grid = QGridLayout()
        self.grid.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.grid.setContentsMargins(0, 0, 0, 0)
        self.grid_spacing = 3
        self.grid.setSpacing(self.grid_spacing)
        if divisions_type == ButtonState.DivisionType.CUSTOM_DIVISIONS:
            logger.debug(f'CASE CUSTOM_DIVISIONS')
            size = int(math.sqrt(grid_count))
            self.span_and_create_custom_divisions(size=size, excluded_positions=data_merge_list, rows=row_from_model, cols=col_from_model)
        else:
            logger.debug(f'CASE DEFAULT_DIVISIONS')
            width_calculate = int(max_width / math.sqrt(grid_count))
            height_calculate = int(width_calculate / item_aspect_ratio)
            if height_calculate * math.sqrt(grid_count) > self.height_available_value:
                self.frame_height = int(self.height_available_value / math.sqrt(grid_count))
                self.frame_width = int((self.frame_height) * item_aspect_ratio)
            else:
                self.frame_width = width_calculate
                self.frame_height = height_calculate
            
            for row in range(row_from_model):
                for col in range(col_from_model):
                    root_camera_widget = StackFrame(parent=self, frame_width=self.frame_width,
                                                    frame_height=self.frame_height, position=QPoint(row, col))
                    root_camera_widget.resizeEvent = lambda event: self.item_resize_event(root_camera_widget, event)
                    root_camera_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
                    self.add_grid_item(stack_item = root_camera_widget,row=row,col=col,width=self.frame_width,height=self.frame_height)
                    self.grid.addWidget(root_camera_widget, row, col)
        self.root_stackedwidget.widget(0).setLayout(self.grid)

    def add_grid_item(self,stack_item:StackFrame = None,row = None,col = None,width = None, height = None, index = None):
        if index is None:
            index = self.get_index_of_item_from_row_col(row, col)
        stack_item.index = index
        grid_item:GridItem = self.tab_model.data.listGridData.get(index,None)

        stack_item.update_resize(width, height)
        if grid_item is None:
            # case tạo mới một tab nên không có thong tin grid_item nào
            widget = stack_item.add_widget()
            widget.stack_item = stack_item
            grid_item = GridItem(index = index,type=ItemType.Label, row = row, col = col,width = width,height = height,model= 'Label',widget = widget)
            self.tab_model.add_grid_item(grid_item)
            stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
        else:
            grid_item.index = index
            grid_item.row = row
            grid_item.col = col
            grid_item.width = width
            grid_item.height = height
            widget = grid_item.widget
            if widget is not None:
                widget.stack_item = stack_item
                if isinstance(widget, Map2DWidgetItem):
                    widget.update_resize(width,height)
                elif isinstance(widget, EventWidget):
                    widget.update_resize(width,height)
                elif isinstance(widget, CameraWidget):
                    if self.tab_model.data.currentGrid['row'] <= 2 and self.tab_model.data.currentGrid['column'] <= 2:
                        widget.switch_video_capture(StreamCameraType.main_stream)
                    else:
                        widget.switch_video_capture(StreamCameraType.sub_stream)
                stack_item.add_widget(widget=widget)
                stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
            else:
                if grid_item.type == ItemType.Camera:
                    model:CameraModel = grid_item.model
                    self.add_camera_to_grid(camera_id=model.data.id,width=width,height=height,index_calculated=index,stack_item=stack_item,camera_model=model)
                elif grid_item.type == ItemType.Floor:
                    model:FloorModel = grid_item.model
                    self.add_floor_to_grid(floor_model=model,stack_item=stack_item, item=grid_item, index_calculated = index)
                elif grid_item.type == ItemType.MapOSM:
                    model:MapModel = grid_item.model
                    self.add_map_osm_to_grid(map_model=model,stack_item=stack_item, item=grid_item, index_calculated = index)
                elif grid_item.type == ItemType.Event:
                    model:EventAI = grid_item.model
                    self.add_event_to_grid(event_model=model,stack_item=stack_item, item=grid_item, index_calculated = index)
                else:
                    widget = stack_item.add_widget()
                    widget.stack_item = stack_item
                    grid_item.widget = widget
                    stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)

    def on_resize_event(self, widget_parent=None, event: QResizeEvent = None):
        self.parent_grid_width, self.parent_grid_height = widget_parent.width(), widget_parent.height()
        # Cancel any existing timer
        if self.resize_timer:
            self.resize_timer.cancel()

        # Start a new timer for delaying video capture
        # self.resize_timer = threading.Timer(0.2, self.start_video_capture)  # Delay of 0.5 seconds
        # self.resize_timer.start()

    def item_resize_event(self, stack_item, event: QResizeEvent = None):
        new_size = event.size()
        width = new_size.width()
        height = new_size.height()
        if stack_item is not None:
            stack_item.update_resize(width, height)
            if stack_item.currentWidget() is not None:
                stack_item.currentWidget().update_resize(width, height)
                self.tab_model.data.listGridData[stack_item.index].width = width
                self.tab_model.data.listGridData[stack_item.index].height = height

    def frame_press_event(self, event):
        super().mousePressEvent(event)
    def span_and_create_custom_divisions(self, size=None, excluded_positions=None, rows=None, cols=None):
        if excluded_positions is not None:
            list_grid_item = {}
            new_data = []
            for item in excluded_positions:
                list_to_tuple = set()
                if isinstance(item, list):
                    for list_item in item:
                        list_to_tuple.add(tuple(list_item))
                    new_data.append(set(list_to_tuple))
                else:
                    new_data.append(set(item))
            all_excluded_positions = set.union(*new_data)
            self.frame_width = int((self.grid_layout_width - (cols-1)) / cols)
            self.frame_height = int((self.grid_layout_height - rows) / rows)
            for list_tuple in new_data:
                # Determine min_row, min_col, and span for the current largest_item
                min_row = min(row for row, _ in list_tuple)
                min_col = min(col for _, col in list_tuple)
                max_row = max(row for row, _ in list_tuple)
                max_col = max(col for _, col in list_tuple)
                row_span = max_row - min_row + 1
                col_span = max_col - min_col + 1
                width_large = col_span*self.frame_width + (col_span-1)
                height_large = row_span*self.frame_height + row_span
                root_camera_large = StackFrame(parent=self, frame_width=width_large,
                                               frame_height=height_large, position=QPoint(min_row, min_col))
                root_camera_large.resizeEvent = lambda event: self.item_resize_event(root_camera_large, event)
                root_camera_large.setSizeIncrement(width_large, height_large)
                self.grid.addWidget(root_camera_large, min_row, min_col, row_span, col_span)
                grid_item_data = {
                    'stack_item': root_camera_large,
                    'row': min_row,
                    'col': min_col,
                    'width': width_large,
                    'height': height_large
                }
                list_grid_item[(min_row, min_col)] = grid_item_data
            # Create items surrounding the largest_item
            for row in range(rows):
                for col in range(cols):
                    if (row, col) in all_excluded_positions:
                        continue  # Skip the specified positions
                    root_camera_widget_small = StackFrame(parent=self, frame_width=self.frame_width,
                                                          frame_height=self.frame_height, position=QPoint(row, col))
                    root_camera_widget_small.resizeEvent = lambda event: self.item_resize_event(root_camera_widget_small, event)
                    self.grid.addWidget(root_camera_widget_small, row, col)
                    grid_item_data = {
                        'stack_item': root_camera_widget_small,
                        'row': row,
                        'col': col,
                        'width': self.frame_width,
                        'height': self.frame_height
                    }
                    list_grid_item[(row, col)] = grid_item_data
            widget_positions = []
            for index in range(self.grid.count()):
                item = self.grid.itemAt(index)
                re_row, re_col, re_row_span, re_col_span = self.grid.getItemPosition(index)
                widget_positions.append((item.widget(), re_row, re_col, re_row_span, re_col_span))
            # Sort the list based on positions
            widget_positions.sort(key=lambda x: (x[1], x[2]))
            # Chỗ này Clear the existing widgets đi
            # Nếu ko clear thì sẽ bị stack layout lên
            for i in reversed(range(self.grid.count())):
                widget = self.grid.itemAt(i).widget()
                self.grid.removeWidget(widget)
                widget.setParent(None)
            # Rearrange widgets in the layout
            for new_index, (widget, re_row, re_col, re_row_span, re_col_span) in enumerate(widget_positions):
                self.grid.addWidget(widget, re_row, re_col, re_row_span, re_col_span)
                # Optionally, set row and column stretch to manage spacing
                self.grid.setRowStretch(re_row, 1)
                self.grid.setColumnStretch(re_col, 1)
            # Update the layout
            self.grid.update()
            for key, value in list_grid_item.items():
                row = value['row']
                col = value['col']
                width = value['width']
                height = value['height']
                stack_item = value['stack_item']
                self.add_grid_item(stack_item = stack_item,row=row,col=col,width=width,height=height)

    def find_smallest_available_key(self):
        # Tìm key nhỏ nhất không có trong self.tab_model.data.listGridData
        # Ví dụ: đang có key 0, 1, 2, 5, 6 trong list. Sau khi chạy sẽ tìm đc key 3 là nhỏ nhất chưa có trong list
        smallest_key = None
        count = 0
        while smallest_key == None:
            grid_item = self.tab_model.data.listGridData.get(count,None)
            if grid_item is not None:
                if grid_item.model == 'Label':
                    smallest_key = count
                else:
                    count += 1
            else:
                smallest_key = count
        return smallest_key

    def match_grid_number(self):
        grids_number = [1, 4, 6, 8, 9, 10, 13, 16, 36, 64]
        index = max(self.list_object_stream_widget.keys(), default=0)
        for number in grids_number:
            if index < number:
                return number
        return None

    def get_index_of_item_from_row_col(self, target_row, target_col):
        for index in range(self.grid.count()):
            item = self.grid.itemAt(index)
            row, col, row_span, col_span = self.grid.getItemPosition(index)
            # Check if the target position is within the span of the item
            if (
                    row <= target_row < row + row_span
                    and col <= target_col < col + col_span
            ):
                return index
        # If no item is found within the span, calculate the index
        index_calculated = int(target_row * self.grid.columnCount() + target_col)
        return index_calculated

    def add_grid_item_signal(self, index):
        grid_item = self.tab_model.data.listGridData.get(index,None)
        if grid_item is not None:
            if grid_item.type == ItemType.Camera:
                widget = grid_item.widget
                stack_item = widget.stack_item
                # stop luồng camera nếu widget này là CameraWidget
                if isinstance(widget,CameraWidget):
                    self.stop_live_handle(widget)
                    is_fullscreen = widget.is_fullscreen
                elif isinstance(widget,Map2DWidgetItem):
                    is_fullscreen = widget.is_fullscreen
                elif isinstance(widget,MapGridWidgetItem):
                    is_fullscreen = widget.is_fullscreen
                sorted_dict = {k: v for k, v in sorted(self.tab_model.data.listGridData.items())}
                self.tab_model.data.listGridData = sorted_dict
                final_url_stream = grid_item.model.data.urlMainstream
                final_camera_name = grid_item.model.data.name
                final_id = grid_item.model.data.id
                # Bắt đầu add CameraWidget
                self.add_camera_to_grid(final_url_stream, final_camera_name, final_id, '', grid_item.width, grid_item.height,
                                        index, stack_item, grid_item.model,
                                        self.current_grid_model.total_grid_count,is_fullscreen = grid_item.is_fullscreen)
            elif grid_item.type == ItemType.Floor:
                widget = grid_item.widget
                stack_item = widget.stack_item
                # stop luồng camera nếu widget này là CameraWidget
                if isinstance(widget,CameraWidget):
                    self.stop_live_handle(widget)
                elif isinstance(widget,Map2DWidgetItem):
                    is_fullscreen = widget.is_fullscreen
                elif isinstance(widget,MapGridWidgetItem):
                    pass
                # Update list_grid_item
                # Bắt đầu add CameraWidget
                self.add_floor_to_grid(floor_model=grid_item.model,stack_item=stack_item, item=grid_item, index_calculated = index,is_fullscreen = grid_item.is_fullscreen,is_editable = (self.tab_model.data.type == TabType.FloorView))
            elif grid_item.type == ItemType.Event:
                widget = grid_item.widget
                stack_item = widget.stack_item
                # stop luồng camera nếu widget này là CameraWidget
                if isinstance(widget,CameraWidget):
                    self.stop_live_handle(widget)
                elif isinstance(widget,Map2DWidgetItem):
                    is_fullscreen = widget.is_fullscreen
                elif isinstance(widget,MapGridWidgetItem):
                    pass
                elif isinstance(widget,EventWidget):
                    pass
                # Bắt đầu add CameraWidget
                self.add_event_to_grid(event_model=grid_item.model,stack_item=stack_item, item=grid_item, index_calculated = index,is_fullscreen = grid_item.is_fullscreen)
            elif grid_item.type == ItemType.MapOSM:
                is_fullscreen = False
                widget = grid_item.widget
                stack_item = widget.stack_item
                # stop luồng camera nếu widget này là CameraWidget
                if isinstance(widget,CameraWidget):
                    self.stop_live_handle(widget)
                    is_fullscreen = widget.is_fullscreen
                elif isinstance(widget,Map2DWidgetItem):
                    is_fullscreen = widget.is_fullscreen
                # Bắt đau add CameraWidget
                self.add_map_osm_to_grid(map_model = grid_item.model,stack_item=stack_item, item=grid_item, index_calculated = index,is_fullscreen = grid_item.is_fullscreen,is_editable = (self.tab_model.data.type == TabType.MapView))

    def swap_grid_item_signal(self, value):
        new_index,old_index = value
        new_stack:StackFrame = self.grid.itemAt(new_index).widget()
        old_stack:StackFrame = self.grid.itemAt(old_index).widget()
        new_grid_item:GridItem = self.tab_model.data.listGridData.get(new_index,None)
        if new_grid_item is not None:
            new_temp_widget = new_stack.currentWidget()
        old_grid_item:GridItem = self.tab_model.data.listGridData.get(old_index,None)
        if old_grid_item is not None:
            old_temp_widget = old_stack.currentWidget()
        new_stack.removeWidget(new_stack.currentWidget())
        old_stack.removeWidget(old_stack.currentWidget())
        new_stack.load_widget(old_temp_widget)
        old_temp_widget.stack_item = new_stack
        new_stack.currentWidget().update_resize(new_stack.size().width(),new_stack.size().height())
        new_stack.update_resize(new_stack.size().width(),new_stack.size().height())
        old_stack.load_widget(new_temp_widget)
        new_temp_widget.stack_item = old_stack
        old_stack.currentWidget().update_resize(old_stack.size().width(),old_stack.size().height())
        old_stack.update_resize(old_stack.size().width(),old_stack.size().height())

    def update_grid_widget(self, camera_widget, stack_item):
            current_index = stack_item.currentIndex()
            new_index = current_index + 1
            if stack_item.count() == 2:
                # Remove the current widget at the current index
                stack_item.removeWidget(stack_item.widget(current_index))
                stack_item.addWidget(camera_widget)
                stack_item.setCurrentIndex(current_index)
            else:
                stack_item.addWidget(camera_widget)      # Set the newly added widget as the current widget
                stack_item.setCurrentIndex(new_index)

    def is_camera_model_in_dict(self, camera_model):
        for camera_widget_model in self.tab_model.data.listGridData.values():
            if camera_widget_model.model == camera_model:
                return True
        return False

    def open_in_position_camera(self, value):
        model, row, col, width, height = value
        index_calculated = self.get_index_of_item_from_row_col(row, col)
        grid_item = self.tab_model.data.listGridData.get(index_calculated,None)
        if model == grid_item.model:
            return
        else:
            if isinstance(model,CameraModel):
                grid_item.type = ItemType.Camera
                self.tab_model.set_model(grid_item = grid_item,model = model)
                self.tab_model.add_grid_item_signal.emit(grid_item.index)
                controller:Controller = controller_manager.get_controller(server_ip=model.data.server_ip)
                if self.tab_model.data.type != TabType.Invalid:
                    self.tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.DropCamera,'data':{'key': grid_item.index,'camera_id': model.data.id,'server_ip':model.data.server_ip,'row':grid_item.row,'col':grid_item.col}}
                    controller.update_tabmodel_by_put(parent=self, tab=self.tab_model.data)

    def open_floor_in_position(self, value):
        model, row, col, width, height, server_ip = value
        logger.debug(f'open_map_in_position = {value}')
        index_calculated = self.get_index_of_item_from_row_col(row, col)
        grid_item = self.tab_model.data.listGridData.get(index_calculated,None)
        if model == grid_item.model:
            return
        else:
            is_fullscreen = False
            widget = self.tab_model.data.listGridData[index_calculated].widget
            stack_item = widget.stack_item
            # stop luồng camera nếu widget này là CameraWidget
            if isinstance(widget,CameraWidget):
                self.stop_live_handle(widget)
                is_fullscreen = widget.is_fullscreen
            self.tab_model.data.listGridData[index_calculated].model = model
            self.tab_model.data.listGridData[index_calculated].type = ItemType.Floor
            sorted_dict = {k: v for k, v in sorted(self.tab_model.data.listGridData.items())}
            self.tab_model.data.listGridData = sorted_dict
            self.add_floor_to_grid(floor_model=model,stack_item=stack_item, item=grid_item, index_calculated = index_calculated,is_fullscreen = is_fullscreen)
    # Open map on grid
    def open_map_in_position(self, value):
        model, row, col, width, height, server_ip = value
        index_calculated = self.get_index_of_item_from_row_col(row, col)
        grid_item = self.tab_model.data.listGridData.get(index_calculated,None)
        if model == grid_item.model:
            return
        else:
            is_fullscreen = False
            widget = self.tab_model.data.listGridData[index_calculated].widget
            stack_item = widget.stack_item
            # stop luồng camera nếu widget này là CameraWidget
            if isinstance(widget,CameraWidget):
                self.stop_live_handle(widget)
                is_fullscreen = widget.is_fullscreen
            self.tab_model.data.listGridData[index_calculated].model = model
            self.tab_model.data.listGridData[index_calculated].type = ItemType.MapOSM
            sorted_dict = {k: v for k, v in sorted(self.tab_model.data.listGridData.items())}
            self.tab_model.data.listGridData = sorted_dict
            self.add_map_osm_to_grid(map_model = grid_item.model,stack_item=stack_item, item=grid_item, index_calculated = index_calculated,is_fullscreen = is_fullscreen)

    def add_group_signal(self, list_camera_in_group: List[Camera]):
        if list_camera_in_group is None or len(list_camera_in_group) == 0:
            return
        all_group_opened = True
        for index, camera_model in enumerate(list_camera_in_group):
            if all((camera_model != grid_item.model) for grid_item in self.tab_model.data.listGridData.values()):
                all_group_opened = False
                new_key = self.find_smallest_available_key()
                grid_item = self.tab_model.data.listGridData.get(new_key,None)
                if grid_item is not None:
                    grid_item.type = ItemType.Camera
                    self.tab_model.set_model(grid_item = grid_item,model = camera_model)
                    grid_item.widget = None
                else:
                    grid_item = GridItem(index=new_key,type=ItemType.Camera,model=camera_model)
                    self.tab_model.set_model(grid_item = grid_item,model = camera_model)
                    self.tab_model.add_grid_item(grid_item)
        if all_group_opened:
            return
        max_items_per_grid = 36
        grid_size = math.ceil(math.sqrt(min(len(self.tab_model.data.listGridData), max_items_per_grid)))
        grid_model = GridButtonModel.StandardGrid.DIVISIONS_1
        max_key = max(self.tab_model.data.listGridData.keys())
        if (max_key + 1) == 1:
            pass
        elif 1 < (max_key + 1) <= 4:
            grid_model = GridButtonModel.StandardGrid.DIVISIONS_4
            self.camera_bottom_toolbar.grid.setIcon(QIcon(grid_model.image_url))
        elif 4 < (max_key + 1) <= 9:
            grid_model = GridButtonModel.StandardGrid.DIVISIONS_9
            self.camera_bottom_toolbar.grid.setIcon(QIcon(grid_model.image_url))
        elif 9 < (max_key + 1) <= 16:
            grid_model = GridButtonModel.StandardGrid.DIVISIONS_16
            self.camera_bottom_toolbar.grid.setIcon(QIcon(grid_model.image_url))
        elif 16 < (max_key + 1) <= 36:
            grid_model = GridButtonModel.StandardGrid.DIVISIONS_36
            self.camera_bottom_toolbar.grid.setIcon(QIcon(grid_model.image_url))
        elif (max_key + 1) > 36:
            grid_model = GridButtonModel.StandardGrid.DIVISIONS_64
            self.camera_bottom_toolbar.grid.setIcon(QIcon(grid_model.image_url))
        self.camera_bottom_toolbar.current_grid_value = grid_model
        self.current_grid_model = grid_model
        self.tab_model.data.currentGrid = self.camera_bottom_toolbar.current_grid_value.to_dict()
        self.tab_model.change_grid_view_signal.emit(self.tab_model.data.currentGrid)

    def on_drop_map(self, value):
        map_model, row, col, width, height, mime_object_name, old_position, stack_view = value
        if map_model is not None:
            index_calculated = self.get_index_of_item_from_row_col(row, col)
            stack_item: StackFrame = self.grid.itemAtPosition(row, col).widget()
            map_model: FloorModel
            map_widget = Map2DWidgetItem(parent=stack_item, floor_model=map_model, width=width, height=height)
            current_index = stack_item.currentIndex()
            new_index = current_index + 1
            if stack_item.count() == 2:
                # Remove the current widget at the current index    
                stack_item.removeWidget(stack_item.widget(current_index))
                stack_item.addWidget(map_widget)
                stack_item.setCurrentWidget(map_widget)
            else:
                stack_item.addWidget(map_widget)
                stack_item.setCurrentWidget(map_widget)
        pass

    def calculate_row_col(self, index, grid_size, grid):
        item = grid.itemAt(index)
        re_row, re_col, re_row_span, re_col_span = grid.getItemPosition(index)    # Tính toán ra hàng, cột từ index và size của grid
        return int(re_row), int(re_col)

    def start_video_capture(self):
        if len(self.tab_model.data.listGridData) < 1:
            return
        for key,item in self.tab_model.data.listGridData.items():
            if isinstance(item.widget,CameraWidget):
                video_capture = item.widget.video_capture
                if video_capture is not None and not video_capture.isRunning():
                    video_capture.start_thread()

    def create_video_capture(self,grid_number = 1,camera_id = None, camera_model: CameraModel = None,width = None,height = None,index_calculated = None,stack_item = None):
        camera_widget = CameraWidget(
                stream_link='', camera_name=camera_model.data.name, camera_id=camera_id,
                width=width, height=height,
                root_width=width, root_height=height, camera_model=camera_model,stack_item= stack_item,tab_model= self.tab_model
            )
        return camera_widget

    def add_floor_to_grid(self,floor_model = None,stack_item: StackFrame = None,item = None, index_calculated = None, is_fullscreen=False,is_editable = False):
        map_widget = Map2DWidgetItem(parent=stack_item, floor_model = floor_model, width=item.width, height=item.height, row=item.row, col=item.col, callback_fullscreen=self.callback_fullscreen, stack_item=stack_item,tab_model=self.tab_model, is_editable=is_editable)
        map_widget.drop_when_not_editable_signal.connect(self.on_drop_object_to_map_not_editable)
        self.tab_model.data.listGridData[index_calculated].widget = map_widget
        if is_fullscreen:
            stack_item_fullscreen = self.root_stackedwidget.currentWidget()
            map_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
            stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
            map_widget.is_fullscreen = is_fullscreen
            stack_item_fullscreen.load_widget(map_widget)
        else:
            map_widget.update_resize(stack_item.frame_width, stack_item.frame_height)
            stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
            stack_item.load_widget(map_widget)

    def add_map_osm_to_grid(self, map_model = None,stack_item: StackFrame = None, item = None, index_calculated = None, is_fullscreen=False,is_editable = False):
        map_osm = MapGridWidgetItem(parent=stack_item,map_model = map_model, width=item.width, height=item.height, row=item.row, col=item.col,
                                     stack_item=stack_item,tab_model=self.tab_model, is_editable=is_editable)
        self.tab_model.data.listGridData[index_calculated].widget = map_osm
        if is_fullscreen:
            stack_item_fullscreen = self.root_stackedwidget.currentWidget()
            map_osm.update_resize(self.parent_grid_width, self.parent_grid_height)
            stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
            map_osm.is_fullscreen = is_fullscreen
            stack_item_fullscreen.load_widget(map_osm)
        else:
            map_osm.update_resize(stack_item.frame_width, stack_item.frame_height)
            stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
            stack_item.load_widget(map_osm)

    def add_event_to_grid(self,event_model = None,stack_item: StackFrame = None,item = None, index_calculated = None, is_fullscreen=False):
        event_widget = EventWidget(parent=stack_item, event_model = event_model, width=stack_item.frame_width, height=stack_item.frame_height, row=item.row, col=item.col, stack_item=stack_item,tab_model=self.tab_model)
        self.tab_model.data.listGridData[index_calculated].widget = event_widget
        if is_fullscreen:
            stack_item_fullscreen = self.root_stackedwidget.currentWidget()
            event_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
            stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
            event_widget.is_fullscreen = is_fullscreen
            stack_item_fullscreen.load_widget(event_widget)
        else:
            event_widget.update_resize(stack_item.frame_width, stack_item.frame_height)
            stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
            stack_item.load_widget(event_widget)

    def on_drop_object_to_map_not_editable(self, value):
        event, row, col = value
        mime_data = event.mimeData()
        # send to drop event of stack item
        stack_item: StackFrame = self.grid.itemAtPosition(row, col).widget()
        stack_item.dropEvent(event)
        pass

    def add_camera_to_grid(self, stream_link=None, camera_name = None, camera_id = None, group_names = None, width = None, height = None, index_calculated = None, stack_item = None, camera_model = None,grid_number=1, is_fullscreen=False):
        logger.debug(f'add_camera_to_grid: grid_number: {grid_number} - camera_id: {camera_id} - camera_model: {camera_model} - width: {width} - height: {height} - index_calculated: {index_calculated} - stack_item: {stack_item} - is_fullscreen: {is_fullscreen}')
        camera_widget = self.create_video_capture(grid_number=grid_number,camera_id = camera_id,camera_model = camera_model,width = width,height = height,
                                                      index_calculated=index_calculated,stack_item = stack_item)
        self.tab_model.data.listGridData[index_calculated].widget = camera_widget
        if is_fullscreen:
            stack_item_fullscreen = self.root_stackedwidget.currentWidget()
            camera_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
            camera_widget.is_fullscreen = is_fullscreen
            stack_item_fullscreen.resizeEvent = lambda event: self.item_resize_event(stack_item_fullscreen, event)
            stack_item_fullscreen.load_widget(camera_widget)
        else:
            camera_widget.update_resize(width=width, height=height)
            current_index = stack_item.currentIndex()
            stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
            stack_item.load_widget(camera_widget)
        stack_item.grid_item_clicked_signal.connect(self.handle_widget_clicked)
        camera_widget.ptz_signal.connect(self.ptz_handle)
        camera_widget.right_mouse_event.connect(self.right_mouse_event)

    def fullscreen_grid_item_signal(self,index):
        grid_item = self.tab_model.data.listGridData.get(index,None)
        if grid_item is not None:
            # logger.debug(f'self.tab_model.data.currentGrid: {self.tab_model.data.currentGrid['total_grid_count']} - index: {index}')
            if self.tab_model.data.currentGrid['total_grid_count'] == 1:
                return None
            object_widget: StreamObjectBaseWidget = grid_item.widget
            stack_item: StackFrame = object_widget.stack_item
            if grid_item.is_fullscreen:
                # exit fullscreen
                grid_item.is_fullscreen = False
                object_widget.btn_full_screen.set_icon(main_controller.get_theme_attribute("Image", "expand_camera"))
                if isinstance(object_widget, CameraWidget):
                    object_widget.update_resize(self.original_width, self.original_height, True)
                elif isinstance(object_widget, Map2DWidgetItem):
                    object_widget.update_resize(self.original_width, self.original_height)
                elif isinstance(object_widget, MapGridWidgetItem):
                    object_widget.update_resize(self.original_width, self.original_height)
                elif isinstance(object_widget, EventWidget):
                    object_widget.update_resize(self.original_width, self.original_height)

                stack_item_new = self.grid.itemAt(stack_item.index).widget()
                stack_item_new.load_widget(object_widget)
                object_widget.stack_item = stack_item_new
                self.root_stackedwidget.removeWidget(stack_item)
                self.root_stackedwidget.setCurrentIndex(0)
                # update size stack item
                stack_item_new.update_resize(self.original_width, self.original_height)
                self.tab_model.data.listGridData[index].widget = object_widget
                if isinstance(object_widget, CameraWidget):
                    # Switch to sub stream if grid is 3x3 or larger and stream type is main/sub
                    if self.tab_model.data.currentGrid['row'] > 2 and self.tab_model.data.currentGrid['column'] > 2:
                        current_stream_type = object_widget.video_capture.stream_type
                        logger.debug(f'TAG_INDEX: current_stream_type = {current_stream_type}')
                        if current_stream_type in [StreamCameraType.main_stream, StreamCameraType.sub_stream]:
                            object_widget.switch_video_capture(StreamCameraType.sub_stream)
            else:
                # fullscreen
                grid_item.is_fullscreen = True
                object_widget.btn_full_screen.set_icon(main_controller.get_theme_attribute("Image", "shrink_camera"))
                self.original_width = object_widget.root_width
                self.original_height = object_widget.root_height
                if isinstance(object_widget, CameraWidget):
                    object_widget.update_resize(self.parent_grid_width, self.parent_grid_height, True)
                elif isinstance(object_widget, Map2DWidgetItem):
                    object_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
                elif isinstance(object_widget, MapGridWidgetItem):
                    object_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
                elif isinstance(object_widget, EventWidget):
                    object_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
                stack_item_new = stack_item.clone()
                stack_item_new.load_widget(object_widget)
                object_widget.stack_item = stack_item_new

                self.root_stackedwidget.addWidget(stack_item_new)
                self.root_stackedwidget.setCurrentWidget(stack_item_new)
                # update size stack item
                stack_item_new.update_resize(self.parent_grid_width, self.parent_grid_height)
                self.tab_model.data.listGridData[index].widget = object_widget
                if isinstance(object_widget, CameraWidget):
                    # Only switch to main stream if current stream type is main/sub
                    current_stream_type = object_widget.video_capture.stream_type
                    if current_stream_type in [StreamCameraType.main_stream, StreamCameraType.sub_stream]:
                        object_widget.switch_video_capture(StreamCameraType.main_stream)
            return object_widget

    def callback_fullscreen(self, object_widget: StreamObjectBaseWidget):
        if self.camera_bottom_toolbar.current_grid_value.total_grid_count == 1:
            return None
        index_calculated = None
        for idx, grid_item in self.tab_model.data.listGridData.items():
            grid_item: GridItem
            if object_widget == grid_item.widget:
                index_calculated = idx
        stack_item: StackFrame = object_widget.stack_item
        if object_widget.is_fullscreen:
            # exit fullscreen
            object_widget.is_fullscreen = False
            object_widget.btn_full_screen.set_icon(main_controller.get_theme_attribute("Image", "expand_camera"))
            if isinstance(object_widget, CameraWidget):
                object_widget.update_resize(self.original_width, self.original_height, True)
            if isinstance(object_widget, Map2DWidgetItem):
                object_widget.update_resize(self.original_width, self.original_height)
            if isinstance(object_widget, MapGridWidgetItem):
                object_widget.update_resize(self.original_width, self.original_height)
            stack_item_new = self.grid.itemAt(stack_item.index).widget()
            stack_item_new.load_widget(object_widget)
            object_widget.stack_item = stack_item_new
            self.root_stackedwidget.removeWidget(stack_item)
            self.root_stackedwidget.setCurrentIndex(0)
            # update size stack item
            stack_item_new.update_resize(self.original_width, self.original_height)
            self.tab_model.data.listGridData[index_calculated].widget = object_widget
        else:
            object_widget.is_fullscreen = True
            object_widget.btn_full_screen.set_icon(main_controller.get_theme_attribute("Image", "shrink_camera"))
            self.original_width = object_widget.root_width
            self.original_height = object_widget.root_height
            if isinstance(object_widget, CameraWidget):
                object_widget.update_resize(self.parent_grid_width, self.parent_grid_height, True)
            if isinstance(object_widget, Map2DWidgetItem):
                object_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
            if isinstance(object_widget, MapGridWidgetItem):
                object_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
            stack_item_new = stack_item.clone()
            stack_item_new.load_widget(object_widget)
            object_widget.stack_item = stack_item_new
            self.root_stackedwidget.addWidget(stack_item_new)
            self.root_stackedwidget.setCurrentWidget(stack_item_new)
            # update size stack item
            stack_item_new.update_resize(self.parent_grid_width, self.parent_grid_height)
            self.tab_model.data.listGridData[index_calculated].widget = object_widget
        return object_widget

    def replace_all_tab_model_data_signal(self,data):
        # Hàm này xử lý việc thay đổi tất cả dữ liệu GridItem trên SavedView hoặc Virtual Window
        new_tab_model:TabModel = data
        # cần xóa dữ liệu tabmodel (all grid item) cũ trước 
        for index,grid_item in self.tab_model.data.listGridData.items():
            if grid_item.widget != None and isinstance(grid_item.widget,CameraWidget):
                video_capture_controller.unregister_video_capture(grid_item.widget)
                if grid_item.virtual_widget is not None:
                    video_capture_controller.unregister_video_capture(grid_item.virtual_widget)
            widget = grid_item_selected.data['widget']    
            if widget is not None and (widget == grid_item.widget or widget == grid_item.virtual_widget):
                if hasattr(widget,'grid_item_unclicked'):
                    widget.grid_item_unclicked()
                    grid_item_selected.clear()
                    if Config.ENABLE_STREAM_FLOW_BUTTON:
                        main_controller.stream_status_signal.emit((ButtonState.Status.DISABLE,None))
                    if Config.ENABLE_SPEAKER_BUTTON:
                        self.camera_bottom_toolbar.update_volume_status()
        # Map dữ liệu mới từ new_tab_model sang tab_model cũ
        self.tab_model.data.listGridData = new_tab_model.data.listGridData
        self.tab_model.data.listGridCustomData = new_tab_model.data.listGridCustomData
        self.tab_model.data.currentGrid = new_tab_model.data.currentGrid  
        self.tab_model.change_grid_view_signal.emit(self.tab_model.data.currentGrid)
        # Cài đặt timeout để tránh bị disconnect video capture
        self.timeout()
        main_controller.gc_collect(self.camera_grid_widget)

    def remove_all_grid_item_signal(self,data):
        self.camera_bottom_toolbar.exit_all_clicked(is_websocket_used=True)

    def update_list_camera(self):
        self.camera_bottom_toolbar.current_grid_value = ItemGridModel.from_dict(self.tab_model.data.currentGrid)
        self.current_grid_model = self.camera_bottom_toolbar.current_grid_value
        self.change_grid_view(self.current_grid_model)

    def remove_grid_item_signal(self,index):
        grid_item = self.tab_model.data.listGridData.get(index,None)
        if grid_item is not None:
            widget:StreamObjectBaseWidget = grid_item.widget
            is_selected = True if grid_item_selected.data['widget'] == widget else False
            stack_item: StackFrame = widget.stack_item
            if grid_item.is_fullscreen:
                if widget is not None:
                    if isinstance(widget, CameraWidget):
                        self.stop_live_handle(widget)
                        widget.deleteLater()
                        stack_item.removeWidget(widget)
                    self.root_stackedwidget.removeWidget(stack_item)
                    self.root_stackedwidget.setCurrentIndex(0)
                    stack_item = widget.stack_item
                    background_widget = stack_item.add_widget()
                    self.tab_model.data.listGridData[stack_item.index].type = ItemType.Label
                    self.tab_model.data.listGridData[stack_item.index].model = 'Label'
                    self.tab_model.data.listGridData[stack_item.index].widget = background_widget
                    if is_selected:
                        stack_item.grid_item_clicked(main_controller.current_tab)
                    return
                # sau khi fullscreen -> cần truyền lại giá trị stack item và widget mới để remove khỏi grid
            if isinstance(widget, CameraWidget):
                self.stop_live_handle(widget)
                widget.deleteLater()
                stack_item.removeWidget(widget)
            elif isinstance(widget, Map2DWidgetItem):
                widget.deleteLater()
                stack_item.removeWidget(widget)
            elif isinstance(widget, MapGridWidgetItem):
                widget.deleteLater()
                stack_item.removeWidget(widget)
            elif isinstance(widget, EventWidget):
                widget.deleteLater()
                stack_item.removeWidget(widget)
            background_widget = stack_item.add_widget()
            self.tab_model.data.listGridData[stack_item.index].type = ItemType.Label
            self.tab_model.data.listGridData[stack_item.index].model = 'Label'
            self.tab_model.data.listGridData[stack_item.index].widget = background_widget
            if is_selected:
                stack_item.grid_item_clicked(main_controller.current_tab)
                

    def stop_live_handle(self, data):
        logger.debug(f'stop_live_handle')
        widget = data
        camera_id = widget.camera_id
        video_capture_controller.unregister_video_capture(widget)
        widget.close_widget()
        if widget.fullscreen_status():
            parent_widget = widget.parentWidget()
            stack_item = widget.stack_item
            if parent_widget is not None:
                # kiem tra xem camera nay co dang duoc chon grid_item_selected khong
                if grid_item_selected.data['tab_index'] is not None and grid_item_selected.is_tab_index(main_controller.current_tab) and camera_id == grid_item_selected.data['camera_id']:
                    widget.stack_item.grid_item_unclicked()
                    main_controller.stream_status_signal.emit((ButtonState.Status.DISABLE,None))
                    self.camera_bottom_toolbar.update_volume_status()
                
                widget = None
        else:
            # kiem tra xem camera nay co dang duoc chon grid_item_selected khong
            if grid_item_selected.data['tab_index'] is not None and grid_item_selected.is_tab_index(main_controller.current_tab) and camera_id == grid_item_selected.data['camera_id']:
                widget.stack_item.grid_item_unclicked()

                main_controller.stream_status_signal.emit((ButtonState.Status.DISABLE,None))
                self.camera_bottom_toolbar.update_volume_status()

    def right_mouse_event(self,data):
        event = data[0]
        if event == Style.Actions.start_recording:
            self.camera_bottom_toolbar.record_clicked(camera_model=data[1])
        elif event == Style.Actions.standard:
            self.camera_screen.ptz_widget.ptz_widget.grid_ptz_widget.show()
            self.camera_screen.ptz_widget.ptz_widget.control_speed_widget.show()
            self.camera_screen.ptz_widget.ptz_widget.ptz_advance_widget.hide()
            self.camera_screen.ptz_widget.ptz_widget.preset_patrol_widget.hide()
            self.camera_screen.ptz_widget.ptz_widget.image_adj_widget.hide()
            self.camera_screen.ptz_widget.setFixedHeight(200)
            self.camera_screen.ptz_widget.dropdown_button.setIcon(QIcon(main_controller.get_theme_attribute("Image", "drop_dow")))
        elif event == Style.Actions.advance:
            self.camera_screen.ptz_widget.ptz_widget.grid_ptz_widget.show()
            self.camera_screen.ptz_widget.ptz_widget.control_speed_widget.show()
            self.camera_screen.ptz_widget.ptz_widget.ptz_advance_widget.show()
            self.camera_screen.ptz_widget.ptz_widget.preset_patrol_widget.hide()
            self.camera_screen.ptz_widget.ptz_widget.image_adj_widget.hide()
            self.camera_screen.ptz_widget.setFixedHeight(250)
            self.camera_screen.ptz_widget.dropdown_button.setIcon(QIcon(main_controller.get_theme_attribute("Image", "drop_dow")))
        elif event == Style.Actions.press_patrol:
            self.camera_screen.ptz_widget.ptz_widget.grid_ptz_widget.show()
            self.camera_screen.ptz_widget.ptz_widget.control_speed_widget.show()
            self.camera_screen.ptz_widget.ptz_widget.ptz_advance_widget.hide()
            self.camera_screen.ptz_widget.ptz_widget.preset_patrol_widget.show()
            self.camera_screen.ptz_widget.ptz_widget.image_adj_widget.hide()
            self.camera_screen.ptz_widget.setFixedHeight(600)
            self.camera_screen.ptz_widget.dropdown_button.setIcon(QIcon(main_controller.get_theme_attribute("Image", "drop_dow")))
        elif event == Style.Actions.enable_audio:
            self.camera_bottom_toolbar.volume_clicked(camera_model= data[1])
        elif event == Style.Actions.disable_audio:
            self.camera_bottom_toolbar.exit_audio_thread()
        elif event == Style.Actions.image_adj:
            logger.debug(f'right_mouse_event = {event}')
            self.camera_screen.ptz_widget.ptz_widget.grid_ptz_widget.hide()
            self.camera_screen.ptz_widget.ptz_widget.control_speed_widget.hide()
            self.camera_screen.ptz_widget.ptz_widget.ptz_advance_widget.hide()
            self.camera_screen.ptz_widget.ptz_widget.preset_patrol_widget.hide()
            self.camera_screen.ptz_widget.ptz_widget.image_adj_widget.show()
            self.camera_screen.ptz_widget.setFixedHeight(280)
            self.camera_screen.ptz_widget.dropdown_button.setIcon(QIcon(main_controller.get_theme_attribute("Image", "drop_dow_right")))
            self.camera_screen.ptz_widget.is_hidden = True

    def ptz_handle(self,data):
        self.camera_screen.ptz_widget.hide_ptz(data)

    def find_item_by_text(self, parent_item, target_text):
        # Recursive function to find an item by text
        for row in range(parent_item.rowCount()):
            item = parent_item.child(row)
            if item.text() == target_text:
                return item
            # Recursively search in child items
            found_item = self.find_item_by_text(item, target_text)
            if found_item is not None:
                return found_item
        return None

    def handle_widget_clicked(self, data):
        widget, is_show_border = data
        if not isinstance(widget, CameraWidget):
            return
        controller = widget.controller
        camera_list = camera_model_manager.get_camera_list(server_ip=controller.server.data.server_ip)
        self.camera_screen.main_treeview_widget.tree_view.selectionModel().clearSelection()
        self.camera_screen.main_treeview_widget.tree_view.clearSelection()
        if is_show_border:
            model = self.camera_screen.main_treeview_widget.model.invisibleRootItem()
            for idx, item in camera_list.items():
                if item.data.id == widget.camera_id:
                    item_standard = self.find_item_by_text(model, widget.camera_name)
                    if item_standard is not None:
                        # Select the item and its descendants
                        index = self.camera_screen.main_treeview_widget.model.indexFromItem(item_standard)
                        selection_model = self.camera_screen.main_treeview_widget.tree_view.selectionModel()
                        selection = QItemSelection(index, index)
                        selection_model.select(selection, QItemSelectionModel.SelectionFlag.Toggle)
        if grid_item_selected.data['tab_index'] is None:
            # disable viec click vao cac icon button trong camera_bottom_toolbar
            if Config.ENABLE_RECORD_BUTTON:
                self.camera_bottom_toolbar.record.setDisabled(True)
                self.camera_bottom_toolbar.current_video_record_value = ButtonState.RecordType.DISABLE
                self.camera_bottom_toolbar.record.setIcon(QIcon(Style.PrimaryImage.record_disable))
                self.camera_bottom_toolbar.record.setStyleSheet(Style.StyleSheet.button_disable)
            if Config.ENABLE_SPEAKER_BUTTON:
                self.camera_bottom_toolbar.update_volume_status()
            if Config.ENABLE_STREAM_FLOW_BUTTON:
                main_controller.stream_status_signal.emit((ButtonState.Status.DISABLE,widget))
        else:
            # for id, camera_model in camera_list.items():
            for idx, item in camera_list.items():
                if item.data.id == widget.camera_id:
                    if item.data.recordSetting is not None and not item.data.recordSetting:
                        if Config.ENABLE_RECORD_BUTTON:
                            self.camera_bottom_toolbar.record.setDisabled(False)
                            self.camera_bottom_toolbar.current_video_record_value = ButtonState.RecordType.STOP_RECORD
                            self.camera_bottom_toolbar.record.setIcon(QIcon(Style.PrimaryImage.record_off))
                            self.camera_bottom_toolbar.record.setStyleSheet(Style.StyleSheet.button_on)
                    elif item.data.recordSetting != None and item.data.recordSetting:
                        if Config.ENABLE_RECORD_BUTTON:
                            self.camera_bottom_toolbar.record.setDisabled(False)
                            self.camera_bottom_toolbar.current_video_record_value = ButtonState.RecordType.RECORD
                            self.camera_bottom_toolbar.record.setIcon(QIcon(Style.PrimaryImage.record_on))
                            self.camera_bottom_toolbar.record.setStyleSheet(Style.StyleSheet.button_on)
                    if Config.ENABLE_SPEAKER_BUTTON:
                        self.camera_bottom_toolbar.update_volume_status(status=ButtonState.Status.ENABLE)
                    if Config.ENABLE_STREAM_FLOW_BUTTON:
                        # update trang thai style cua icon button stream_flow
                        main_controller.stream_status_signal.emit((ButtonState.Status.ENABLE,widget))

    # Cập nhật trạng thái start or stop video capture camera stream khi chuyển tab
    def update_status_camera_stream(self, status = True,list_camera_ids = []):
        if self.tab_model is not None:
            for index,grid_item in self.tab_model.data.listGridData.items():
               if grid_item.widget is not None:
                    if isinstance(grid_item.widget,CameraWidget):
                        if grid_item.widget.camera_model.data.id not in list_camera_ids:
                            grid_item.widget.frame_delay = 3
                            grid_item.widget.camera_status = True if status == True else False
                            grid_item.widget.video_capture.connect_status = True if status == True else False
                        else:
                            grid_item.widget.frame_delay = 3
                            grid_item.widget.camera_status = True if status == True else False


    def update_status_video_capture(self, status = True):
        main_controller.list_camera_ids = []
        if self.tab_model is not None:
            for index,grid_item in self.tab_model.data.listGridData.items():
                if grid_item.widget is not None:
                    if isinstance(grid_item.widget,CameraWidget):
                        main_controller.list_camera_ids.append(grid_item.widget.camera_model.data.id)
                        grid_item.widget.camera_status = True if status == True else False
                        grid_item.widget.video_capture.connect_status = True if status == True else False
                        grid_item.widget.update_resize(grid_item.widget.root_width,grid_item.widget.root_height)

    # sử dụng timeout để trigger video capture có chứa cùng 1 luồng camera (stop video_capture xong start nó luôn) dẫn đến bất đồng bộ lên cần timeout để restart lại luồng video capture
    # case cụ thể
    # kéo SavedView vào Tab không định danh hoặc Virtual có chứa cùng luồng camera: pipeline hiện tại là exit all list_object_stream_widget của 
    # Tab không định danh hoặc Virtual xong rồi lại start chính luồng camera vừa exit nên bị bất đồng bộ => luồng video capture luồng camera bị disconnect
    def timeout(self):
        self.timer = QTimer(self)
        self.timer.setInterval(500)
        self.timer.setSingleShot(True)
        self.timer.timeout.connect(self.start_video_capture)
        self.timer.start()

    def retranslateUi(self):
        self.camera_bottom_toolbar.retranslateUi()

    def restyle_camera_grid_widget(self):
        if self.grid is not None:
            for index in range(self.grid.count()):
                item = self.grid.itemAt(index)
                if item is not None:
                    widget: StackFrame = item.widget()
                    if isinstance(widget.currentWidget(), CameraWidget):
                        widget.currentWidget().update_dynamic_stylesheet()
                    elif isinstance(widget.currentWidget(), EventWidget):
                        widget.currentWidget().setup_event_widget_stylesheet()
                    else:
                        if widget.background_widget is not None:
                            widget.background_widget.background_label.setStyleSheet(f'''
                                background-color: {main_controller.get_theme_attribute("Color", "camera_widget_background")};
                            ''')

        self.grid.update()

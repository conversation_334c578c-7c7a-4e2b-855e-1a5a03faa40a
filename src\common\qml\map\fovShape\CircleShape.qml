import QtQuick
import QtQuick.Controls
import QtQuick.Layouts

Canvas {
    id: root
    property var camData: null
    property var _hoverArea: null
    property var _mapState: null
    property var _item: null
    
    property var cameraId: ""
    property var posCoord: {
        if (!camData || !camData.fovData) return { x: 0, y: 0 };
        try {
            var parsed = JSON.parse(camData.fovData);
            var position = parsed.position || "0.5,0.5";
            var parts = position.split(",");
            if (parts.length < 2) return { x: 0, y: 0 };
            var relX = parseFloat(parts[0]);
            var relY = parseFloat(parts[1]);
            if (isNaN(relX) || isNaN(relY)) return { x: 0, y: 0 };

            return {
                x: relX * _hoverArea.width,
                y: relY * _hoverArea.height
            };
        } catch(e) {
            return { x: 0, y: 0 };
        }
    }

    parent: _hoverArea
    anchors.fill: parent
    signal clicked()
    signal camDataUpdated(var newData)
    property bool isHovered: false
    function updateCamData(prop, value) {
        var newData = Object.assign({}, camData)
        newData[prop] = value
        camData = newData
        camDataUpdated(camData)
    }

    property real centerX: posCoord.x
    property real centerY: posCoord.y
    property real scaleRatio: _hoverArea.scaleRatio || 1
    property real rawXSize: (camData && camData.fovData) ? (function() {
        try {
            var parsed = JSON.parse(camData.fovData);
            return parsed.width || 40;
        } catch (e) {
            return 40;
        }
    }()) : 40

    property real rawYSize: (camData && camData.fovData) ? (function() {
        try {
            var parsed = JSON.parse(camData.fovData);
            return parsed.height || 40;
        } catch (e) {
            return 40;
        }
    }()) : 40

    property real xSize: rawXSize * scaleRatio
    property real ySize: rawYSize * scaleRatio

    property real controlSize: 10

    onPaint: {
        var ctx = getContext("2d")
        ctx.clearRect(0, 0, width, height)

        var topLeftX = centerX - xSize / 2
        var topLeftY = centerY - ySize / 2

        ctx.beginPath()
        ctx.ellipse(
            centerX - xSize / 2,
            centerY - ySize / 2,
            xSize,
            ySize,
            0,
            0,
            2 * Math.PI
        );
        ctx.closePath()

        ctx.strokeStyle = camData ? camData.color : "blue"
        ctx.lineWidth = 2
        ctx.stroke()

        var baseColor = Qt.rgba(0, 0.5, 1, 0.4); // fallback
        if (camData && camData.color) {
            try {
                var tmpColor = Qt.color(camData.color);
                baseColor = `rgba(${Math.round(tmpColor.r * 255)}, ${Math.round(tmpColor.g * 255)}, ${Math.round(tmpColor.b * 255)}, 0.4)`;
            } catch(e) {
                console.warn("Invalid camData.color:", camData.color);
            }
        }
        ctx.fillStyle = baseColor;
        ctx.fill()
    }

    Component.onCompleted: requestPaint()

    // ===== TOP LEFT =====
    Rectangle {
        id: topLeft
        width: root.controlSize
        height: root.controlSize
        color: "white"
        border.color: camData ? camData.color : "blue"
        radius: root.controlSize / 2
        visible: _mapState ? _mapState.editMode : false

        x: root.centerX - root.xSize/2 - width/2
        y: root.centerY - root.ySize/2 - height/2
        z: 2

        MouseArea {
            anchors.fill: parent
            acceptedButtons: Qt.LeftButton
            preventStealing: true

            onPositionChanged: function(mouse) {
                var newPos = topLeft.mapToItem(root, mouse.x + topLeft.width/2, mouse.y + topLeft.height/2);
                // Giữ cố định đỉnh đối diện (bottomRight)
                var fixedX = root.centerX + root.xSize / 2;
                var fixedY = root.centerY + root.ySize / 2;

                // Clamp new position
                newPos.x = Math.min(Math.max(newPos.x, 0), fixedX - 10);
                newPos.y = Math.min(Math.max(newPos.y, 0), fixedY - 10);
                var newWidth = fixedX - newPos.x;
                var newHeight = fixedY - newPos.y;
                root.rawXSize = newWidth / _hoverArea.scaleRatio;
                root.rawYSize = newHeight / _hoverArea.scaleRatio;
                root.centerX = (newPos.x + fixedX) / 2;
                root.centerY = (newPos.y + fixedY) / 2;

                var newRelX = root.centerX / _hoverArea.width;
                var newRelY = root.centerY / _hoverArea.height;
                var newFovData = JSON.parse(camData.fovData);
                newFovData['position'] = `${newRelX},${newRelY}`;
                newFovData['width'] = root.rawXSize;
                newFovData['height'] = root.rawYSize;
                newFovData = JSON.stringify(newFovData);
                updateCamData('fovData', newFovData);
                root.requestPaint();
            }

        }
    }

    // ===== TOP RIGHT =====
    Rectangle {
        id: topRight
        width: root.controlSize
        height: root.controlSize
        color: "white"
        border.color: camData ? camData.color : "blue"
        radius: root.controlSize / 2
        visible: _mapState ? _mapState.editMode : false

        x: root.centerX + root.xSize/2 - width/2
        y: root.centerY - root.ySize/2 - height/2
        z: 2

        MouseArea {
            anchors.fill: parent
            acceptedButtons: Qt.LeftButton
            preventStealing: true

            onPositionChanged: function(mouse) {
                var newPos = topRight.mapToItem(root, mouse.x + topRight.width/2, mouse.y + topRight.height/2);
                // Giữ cố định đỉnh đối diện (bottomLeft)
                var fixedX = root.centerX - root.xSize / 2;
                var fixedY = root.centerY + root.ySize / 2;
                newPos.x = Math.min(Math.max(newPos.x, fixedX + 10), _hoverArea.width);
                newPos.y = Math.min(Math.max(newPos.y, 0), fixedY - 10);
                var newWidth = newPos.x - fixedX;
                var newHeight = fixedY - newPos.y;
                root.rawXSize = newWidth / _hoverArea.scaleRatio;
                root.rawYSize = newHeight / _hoverArea.scaleRatio;
                root.centerX = (newPos.x + fixedX) / 2;
                root.centerY = (newPos.y + fixedY) / 2;

                var newRelX = root.centerX / _hoverArea.width;
                var newRelY = root.centerY / _hoverArea.height;
                var newFovData = JSON.parse(camData.fovData);
                newFovData['position'] = `${newRelX},${newRelY}`;
                newFovData['width'] = root.rawXSize;
                newFovData['height'] = root.rawYSize;
                newFovData = JSON.stringify(newFovData);
                updateCamData('fovData', newFovData);
                root.requestPaint();
            }
        }
    }

    // ===== BOTTOM LEFT =====
    Rectangle {
        id: bottomLeft
        width: root.controlSize
        height: root.controlSize
        color: "white"
        border.color: camData ? camData.color : "blue"
        radius: root.controlSize / 2
        visible: _mapState ? _mapState.editMode : false

        x: root.centerX - root.xSize/2 - width/2
        y: root.centerY + root.ySize/2 - height/2
        z: 2

        MouseArea {
            anchors.fill: parent
            acceptedButtons: Qt.LeftButton
            preventStealing: true

            onPositionChanged: function(mouse) {
                var newPos = bottomLeft.mapToItem(root, mouse.x + bottomLeft.width/2, mouse.y + bottomLeft.height/2);
                // Giữ cố định đỉnh đối diện (topRight)
                var fixedX = root.centerX + root.xSize / 2;
                var fixedY = root.centerY - root.ySize / 2;
                newPos.x = Math.min(Math.max(newPos.x, 0), fixedX - 10);
                newPos.y = Math.min(Math.max(newPos.y, fixedY + 10), _hoverArea.height);
                var newWidth = fixedX - newPos.x;
                var newHeight = newPos.y - fixedY;
                root.rawXSize = newWidth / _hoverArea.scaleRatio;
                root.rawYSize = newHeight / _hoverArea.scaleRatio;
                root.centerX = (newPos.x + fixedX) / 2;
                root.centerY = (newPos.y + fixedY) / 2;

                var newRelX = root.centerX / _hoverArea.width;
                var newRelY = root.centerY / _hoverArea.height;
                var newFovData = JSON.parse(camData.fovData);
                newFovData['position'] = `${newRelX},${newRelY}`;
                newFovData['width'] = root.rawXSize;
                newFovData['height'] = root.rawYSize;
                newFovData = JSON.stringify(newFovData);
                updateCamData('fovData', newFovData);
                root.requestPaint();
            }
        }
    }

    // ===== BOTTOM RIGHT =====
    Rectangle {
        id: bottomRight
        width: root.controlSize
        height: root.controlSize
        color: "white"
        border.color: camData ? camData.color : "blue"
        radius: root.controlSize / 2
        visible: _mapState ? _mapState.editMode : false

        x: root.centerX + root.xSize/2 - width/2
        y: root.centerY + root.ySize/2 - height/2
        z: 2

        MouseArea {
            anchors.fill: parent
            acceptedButtons: Qt.LeftButton
            preventStealing: true

            onPositionChanged: function(mouse) {
                var newPos = bottomRight.mapToItem(root, mouse.x + bottomRight.width/2, mouse.y + bottomRight.height/2);
                // Giữ cố định đỉnh đối diện (topLeft)
                var fixedX = root.centerX - root.xSize / 2;
                var fixedY = root.centerY - root.ySize / 2;
                newPos.x = Math.min(Math.max(newPos.x, fixedX + 10), _hoverArea.width);
                newPos.y = Math.min(Math.max(newPos.y, fixedY + 10), _hoverArea.height);
                var newWidth = newPos.x - fixedX;
                var newHeight = newPos.y - fixedY;
                root.rawXSize = newWidth / _hoverArea.scaleRatio;
                root.rawYSize = newHeight / _hoverArea.scaleRatio;
                root.centerX = (newPos.x + fixedX) / 2;
                root.centerY = (newPos.y + fixedY) / 2;

                var newRelX = root.centerX / _hoverArea.width;
                var newRelY = root.centerY / _hoverArea.height;
                var newFovData = JSON.parse(camData.fovData);
                newFovData['position'] = `${newRelX},${newRelY}`;
                newFovData['width'] = root.rawXSize;
                newFovData['height'] = root.rawYSize;
                newFovData = JSON.stringify(newFovData);
                updateCamData('fovData', newFovData);
                root.requestPaint();
            }
        }
    }
    
    MouseArea {
        id: hoverDetector
        x: root.centerX - root.xSize / 2
        y: root.centerY - root.ySize / 2
        width: root.xSize
        height: root.ySize

        hoverEnabled: true
        onEntered: {
            root.isHovered = true
        }

        onExited: {
            root.isHovered = false
        }
    }

    MouseArea {
        id: dragArea
        // Vị trí và kích thước được tính từ center và kích thước của hình chữ nhật
        x: root.centerX - root.xSize / 2
        y: root.centerY - root.ySize / 2
        width: root.xSize
        height: root.ySize
        z: 0
        acceptedButtons: Qt.LeftButton
        enabled: _mapState ? _mapState.editMode : false

        // Lưu lại offset giữa vị trí nhấn và center hiện tại
        property real dragOffsetX: 0
        property real dragOffsetY: 0
        // Các thuộc tính mới để xác định thao tác click hay drag
        property real pressX: 0
        property real pressY: 0
        property bool dragged: false

        onPressed: function(mouse) {
            // Lấy tọa độ nhấn chuột trong hệ tọa độ của root
            var posInRoot = mapToItem(root, mouse.x, mouse.y);
            dragOffsetX = posInRoot.x - root.centerX;
            dragOffsetY = posInRoot.y - root.centerY;
            // Lưu vị trí nhấn ban đầu
            pressX = mouse.x;
            pressY = mouse.y;
            dragged = false;
        }

        onPositionChanged: function(mouse) {
            // Nếu mouse di chuyển vượt quá ngưỡng (vd: 5 pixel) thì coi là drag
            if (!dragged && (Math.abs(mouse.x - pressX) > 5 || Math.abs(mouse.y - pressY) > 5)) {
                dragged = true;
            }
            var posInRoot = mapToItem(root, mouse.x, mouse.y);
            var newCenterX = posInRoot.x - dragOffsetX;
            var newCenterY = posInRoot.y - dragOffsetY;
            // Clamp center để hình chữ nhật không vượt ra ngoài _hoverArea
            root.centerX = Math.min(Math.max(newCenterX, root.xSize/2), _hoverArea.width - root.xSize/2);
            root.centerY = Math.min(Math.max(newCenterY, root.ySize/2), _hoverArea.height - root.ySize/2);

            var newRelX = root.centerX / _hoverArea.width;
            var newRelY = root.centerY / _hoverArea.height;
            var newFovData = JSON.parse(camData.fovData);
            newFovData['position'] = `${newRelX},${newRelY}`;
            newFovData = JSON.stringify(newFovData);
            updateCamData('fovData', newFovData);

            root.requestPaint();
        }

        onClicked: {
            // Chỉ gọi click nếu không có thao tác kéo (drag)
            if (!dragged) {
                root.clicked();
            }
        }
    }

    Rectangle {
        id: cameraLabelBackground
        visible: isHovered && (camData ? camData.nameEnable : false)
        color: Qt.rgba(0, 0, 0, 0.6)
        radius: 4
        z: 3

        width: cameraLabel.paintedWidth + 12
        height: cameraLabel.paintedHeight + 6
        
        x: root.centerX - width / 2
        y: root.centerY - height / 2

        Text {
            id: cameraLabel
            anchors.centerIn: parent
            text: camData ? camData.name : ""
            color: "white"
            font.pixelSize: 2 * (camData ? camData.size : 1) + 10
        }
    }

}
